{"Version": 1, "WorkspaceRootPath": "G:\\codesecondfolder\\BRU-Avtopark-Av<PERSON><PERSON>ov\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|g:\\codesecondfolder\\bru-avtopark-a<PERSON><PERSON><PERSON><PERSON>\\supernova\\app.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|solutionrelative:supernova\\app.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\supernova\\mainwindow.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|solutionrelative:supernova\\mainwindow.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\supernova\\mainview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|solutionrelative:supernova\\mainview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\supernova\\mainviewviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6E0CE4A9-6B86-908E-5968-8BCF84ABFCE2}|SuperNova\\SuperNova.csproj|solutionrelative:supernova\\mainviewviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A03F3047-76B2-A544-8D18-A41F178A4F16}|SuperNova.Desktop\\SuperNova.Desktop.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\supernova.desktop\\supernova.desktop.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A03F3047-76B2-A544-8D18-A41F178A4F16}|SuperNova.Desktop\\SuperNova.Desktop.csproj|solutionrelative:supernova.desktop\\supernova.desktop.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.RuntimeIdentifierInference.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-avto<PERSON>ov\\ticketsalesapp.adminserver\\controllers\\busescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\busescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\employeescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\employeescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\studio\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.CSharp.Core.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\incomereportviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\incomereportviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\salesstatisticsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\salesstatisticsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\studio\\MSBuild\\Microsoft\\VisualStudio\\v17.0\\Fakes\\Microsoft.QualityTools.Testing.Fakes.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON>ov\\ticketsalesapp.adminserver\\controllers\\ticketscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\ticketscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON>ov\\ticketsalesapp.adminserver\\controllers\\ticketsalescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\ticketsalescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\routescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\routescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\routeschedulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\routeschedulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\rolescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\rolescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\permissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\permissionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\maintenancecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\maintenancecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\jobscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\jobscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\24wh3phf.dgml||{295A0962-5A59-4F4F-9E12-6BC670C15C3B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\32v5eieb.dgml||{295A0962-5A59-4F4F-9E12-6BC670C15C3B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\bkk22ocw.dgml||{295A0962-5A59-4F4F-9E12-6BC670C15C3B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\z5uoqowp.dgml||{295A0962-5A59-4F4F-9E12-6BC670C15C3B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\lnbzfjsj.dgml||{295A0962-5A59-4F4F-9E12-6BC670C15C3B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\nuh05yuc.dgml||{295A0962-5A59-4F4F-9E12-6BC670C15C3B}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\samplecode.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\samplecode.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\underconstructionwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\underconstructionwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\splashscreen.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\splashscreen.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\toolwindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\toolwindow.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\modaldialog.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\modaldialog.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\mainwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\mainwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\helpwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\helpwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\authwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\authwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\aboutwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\aboutwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\usermanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\usermanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\ticketmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\ticketmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\salesstatisticstoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\salesstatisticstoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\salesmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\salesmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\routeschedulesmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\routeschedulesmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\routemanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\routemanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\maintenancemanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\maintenancemanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\jobmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\jobmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\incomereporttoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\incomereporttoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\employeemanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\employeemanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON>ov\\ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\busmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\views\\managementtoolwindowsviews\\busmanagementtoolwindow.axaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\viewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\viewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\usermanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\usermanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\toolwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\toolwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\ticketmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\ticketmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\searchviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\searchviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\salesmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\salesmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\routeschedulesmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\routeschedulesmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\routemanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\routemanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\propertiesviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\propertiesviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\problemviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\problemviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\notificationsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\notificationsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\modaldialogviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\modaldialogviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\modaldialogtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\modaldialogtype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\maintenancemanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\maintenancemanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\jobmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\jobmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\explorerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\explorerviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\employeemanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\employeemanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\debugviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\debugviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\busmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\busmanagementviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.administration.avalonia\\viewmodels\\authviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\viewmodels\\authviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON>ov\\ticketsalesapp.ui.administration.avalonia\\services\\apiclientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54375997-44B5-4CE0-AE45-D3DC6037E727}|TicketSalesApp.UI.Administration.Avalonia\\TicketSalesApp.UI.Administration.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.administration.avalonia\\services\\apiclientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\ticketsalesservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\ticketsalesservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\roleservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\roleservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\qrauthenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\qrauthenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\exportservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\exportservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\dataservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\dataservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\authenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\authenticationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\implementations\\adminactionlogger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\implementations\\adminactionlogger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\skladorders.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\skladorders.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\routeschedules.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\routeschedules.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\roles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\roles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\qrloginpayload.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\qrloginpayload.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\prodazha.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\prodazha.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\permissions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\permissions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\obsluzhivanie.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\obsluzhivanie.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\marshut.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\marshut.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\job.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\job.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\form.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\form.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\employee.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\employee.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\bilet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\bilet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.core\\models\\avtobus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\avtobus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\models\\adminactionlog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\models\\adminactionlog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\data\\dbinitializer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\data\\dbinitializer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3048B295-F660-42D4-829E-2A920D0BB0B4}|TicketSalesApp.Core\\TicketSalesApp.Core.csproj|solutionrelative:ticketsalesapp.core\\data\\appdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\startup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmrouteschedulesmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmrouteschedulesmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmroutemanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmroutemanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\bilet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\bilet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\permissions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\permissions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.core.legacy\\models\\avtobus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\avtobus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\prodazha.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\prodazha.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\marshut.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\marshut.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\employee.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\employee.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.core.legacy\\models\\routeschedules.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B334C1DD-62C8-64BF-2554-8A7FF500B772}|TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj|solutionrelative:ticketsalesapp.core.legacy\\models\\routeschedules.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\ticketsalesapp.ui.legacyforms.dx.windows.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\ticketsalesapp.ui.legacyforms.dx.windows.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.TargetFrameworkInference.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.adminserver\\ticketsalesapp.adminserver.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{BA7308AF-1506-4369-8F7F-32E3574B5313}|TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj|solutionrelative:ticketsalesapp.adminserver\\ticketsalesapp.adminserver.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|g:\\codesecondfolder\\bru-avtopark-avtobusov\\ticketsalesapp.services\\ticketsalesapp.services.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{EBEEF00C-C72D-4A35-AF76-689A3C7BC1B1}|TicketSalesApp.Services\\TicketSalesApp.Services.csproj|solutionrelative:ticketsalesapp.services\\ticketsalesapp.services.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.PackageDependencyResolution.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmsalesmanagement.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmsalesmanagement.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmsalesstatistics.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmsalesstatistics.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmticketmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmticketmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmincomereport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmincomereport.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmmaintenancemanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmmaintenancemanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmrouteschedulesmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmrouteschedulesmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmbusmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmbusmanagement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmlogin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmlogin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\frmlogin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\frmlogin.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON>ov\\ticketsalesapp.ui.avalonia\\views\\authwindow.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\views\\authwindow.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.avalonia\\viewmodels\\authviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\viewmodels\\authviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.avalonia\\viewmodels\\searchresultsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\viewmodels\\searchresultsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.avalonia\\viewmodels\\myticketsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\viewmodels\\myticketsviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.avalonia\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.avalonia\\viewmodels\\helpviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\viewmodels\\helpviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.avalonia\\viewmodels\\aboutviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\viewmodels\\aboutviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON>ov\\ticketsalesapp.ui.avalonia\\views\\pages\\aboutview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}", "RelativeMoniker": "D:0:0:{C4B6D79E-E105-415D-9C0F-1E22A39B4428}|TicketSalesApp.UI.Avalonia\\TicketSalesApp.UI.Avalonia.csproj|solutionrelative:ticketsalesapp.ui.avalonia\\views\\pages\\aboutview.axaml||{6D5344A2-2FCD-49DE-A09D-6A14FD1B1224}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\apiclientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\apiclientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|g:\\codesecondfolder\\bru-avtopark-av<PERSON><PERSON><PERSON>\\ticketsalesapp.ui.legacyforms.dx.windows\\form1.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69725F72-088D-42CC-A87B-6A493A31EB92}|TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj|solutionrelative:ticketsalesapp.ui.legacyforms.dx.windows\\form1.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "Form1.cs [Design]", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.cs [Design]", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-06T17:38:11.531Z", "IsPinned": true, "EditorCaption": " [Design]"}, {"$type": "Bookmark", "Name": "ST:1:0:{e8b06f52-6d01-11d2-aa7d-00c04f990343}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{269a02dc-6af8-11d3-bdc4-00c04f688e50}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainView.axaml", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\MainView.axaml", "RelativeDocumentMoniker": "SuperNova\\MainView.axaml", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\MainView.axaml", "RelativeToolTip": "SuperNova\\MainView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-22T14:28:31.558Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.axaml", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\MainWindow.axaml", "RelativeDocumentMoniker": "SuperNova\\MainWindow.axaml", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\MainWindow.axaml", "RelativeToolTip": "SuperNova\\MainWindow.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-22T14:28:27.944Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "App.axaml", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\App.axaml", "RelativeDocumentMoniker": "SuperNova\\App.axaml", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\App.axaml", "RelativeToolTip": "SuperNova\\App.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-22T14:27:18.869Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "TicketSalesController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\TicketSalesController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\TicketSalesController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\TicketSalesController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\TicketSalesController.cs", "ViewState": "AgIAAD0BAAAAAAAAAAAqwF4BAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:39.974Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "IncomeReportViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\IncomeReportViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\IncomeReportViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\IncomeReportViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\IncomeReportViewModel.cs", "ViewState": "AgIAAEkBAAAAAAAAAAAcwHIBAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:15.29Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Program.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Program.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Program.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Program.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Program.cs", "ViewState": "AgIAAA4AAAAAAAAAAADgvygAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:40:22.753Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "BusesController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\BusesController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\BusesController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\BusesController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\BusesController.cs", "ViewState": "AgIAALcAAAAAAAAAAAAYwNIAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:37:36.889Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Microsoft.NET.RuntimeIdentifierInference.targets", "DocumentMoniker": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.RuntimeIdentifierInference.targets", "ToolTip": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.RuntimeIdentifierInference.targets", "ViewState": "AgIAADQBAAAAAAAAAIAwwEUBAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-04-22T15:18:46.4Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "SuperNova.Desktop.csproj", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova.Desktop\\SuperNova.Desktop.csproj", "RelativeDocumentMoniker": "SuperNova.Desktop\\SuperNova.Desktop.csproj", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova.Desktop\\SuperNova.Desktop.csproj", "RelativeToolTip": "SuperNova.Desktop\\SuperNova.Desktop.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-22T14:19:17.385Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainViewViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\MainViewViewModel.cs", "RelativeDocumentMoniker": "SuperNova\\MainViewViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova\\MainViewViewModel.cs", "RelativeToolTip": "SuperNova\\MainViewViewModel.cs", "ViewState": "AgIAANYCAAAAAAAAAAASwOYCAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T14:18:50.764Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "RoutesController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\RoutesController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\RoutesController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\RoutesController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\RoutesController.cs", "ViewState": "AgIAAO4AAAAAAAAAAAAUwA0BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:33.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "Microsoft.CSharp.Core.targets", "DocumentMoniker": "D:\\studio\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.CSharp.Core.targets", "ToolTip": "D:\\studio\\MSBuild\\Current\\Bin\\Roslyn\\Microsoft.CSharp.Core.targets", "ViewState": "AgIAAEEAAAAAAAAAAAAjwFMAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-07-18T17:20:13.996Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "EmployeesController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\EmployeesController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\EmployeesController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\EmployeesController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\EmployeesController.cs", "ViewState": "AgIAAKMAAAAAAAAAAAAuwMsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:37:50.496Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "RouteSchedulesController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\RouteSchedulesController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\RouteSchedulesController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\RouteSchedulesController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\RouteSchedulesController.cs", "ViewState": "AgIAAIwCAAAAAAAAAAA0wKUCAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-21T13:57:19.685Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "RolesController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\RolesController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\RolesController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\RolesController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\RolesController.cs", "ViewState": "AgIAALwAAAAAAAAAAAAqwN0AAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:19.726Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "Microsoft.QualityTools.Testing.Fakes.targets", "DocumentMoniker": "D:\\studio\\MSBuild\\Microsoft\\VisualStudio\\v17.0\\Fakes\\Microsoft.QualityTools.Testing.Fakes.targets", "ToolTip": "D:\\studio\\MSBuild\\Microsoft\\VisualStudio\\v17.0\\Fakes\\Microsoft.QualityTools.Testing.Fakes.targets", "ViewState": "AgIAAPQAAAAAAAAAAAAYwAwBAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-05-01T17:21:04.917Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "SalesStatisticsViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesStatisticsViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesStatisticsViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesStatisticsViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesStatisticsViewModel.cs", "ViewState": "AgIAAD8BAAAAAAAAAAAYwGgBAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:09.599Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "PermissionsController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\PermissionsController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\PermissionsController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\PermissionsController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\PermissionsController.cs", "ViewState": "AgIAAK8AAAAAAAAAAIBJwNMAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:12.615Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "MaintenanceController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\MaintenanceController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\MaintenanceController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\MaintenanceController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\MaintenanceController.cs", "ViewState": "AgIAAO8AAAAAAAAAAAAswA0BAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:05.143Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "TicketsController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\TicketsController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\TicketsController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\TicketsController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\TicketsController.cs", "ViewState": "AgIAANUAAAAAAAAAAAAgwPgAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:51.398Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "UsersController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\UsersController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\UsersController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\UsersController.cs", "ViewState": "AgIAAMkBAAAAAAAAAAAkwNsBAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:38:57.625Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "JobsController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\JobsController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\JobsController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\JobsController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\JobsController.cs", "ViewState": "AgIAAIUAAAAAAAAAAAAuwK0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:37:56.433Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "AuthController.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Controllers\\AuthController.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Controllers\\AuthController.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Controllers\\AuthController.cs", "ViewState": "AgIAAB4FAAAAAAAAAAAswDwFAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T19:37:31.302Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "24wh3phf.dgml", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\24wh3phf.dgml", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\24wh3phf.dgml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000968|", "WhenOpened": "2025-05-01T16:07:25.971Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "32v5eieb.dgml", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\32v5eieb.dgml", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\32v5eieb.dgml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000968|", "WhenOpened": "2025-05-01T16:06:51.611Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "bkk22ocw.dgml", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\bkk22ocw.dgml", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\bkk22ocw.dgml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000968|", "WhenOpened": "2025-05-01T16:05:14.735Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "z5uoqowp.dgml", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\z5uoqowp.dgml", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\z5uoqowp.dgml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000968|", "WhenOpened": "2025-05-01T16:04:49.459Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "lnbzfjsj.dgml", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\lnbzfjsj.dgml", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\lnbzfjsj.dgml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000968|", "WhenOpened": "2025-05-01T16:04:04.683Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "nuh05yuc.dgml", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\nuh05yuc.dgml", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\nuh05yuc.dgml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000968|", "WhenOpened": "2025-05-01T15:55:57.336Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "Program.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Program.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Program.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Program.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Program.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAuwBoAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:46:44.983Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "UnderConstructionWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\UnderConstructionWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\UnderConstructionWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\UnderConstructionWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\UnderConstructionWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:46:39.383Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "samplecode.txt", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\samplecode.txt", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\samplecode.txt", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\samplecode.txt", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\samplecode.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-05-01T15:46:44.436Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "SplashScreen.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\SplashScreen.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\SplashScreen.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\SplashScreen.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\SplashScreen.axaml.cs", "ViewState": "AgIAAA4AAAAAAAAAAAA8wDYAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:46:33.587Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "ToolWindow.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ToolWindow.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ToolWindow.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ToolWindow.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ToolWindow.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAIwCwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:46:24.353Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "ModalDialog.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ModalDialog.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ModalDialog.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ModalDialog.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ModalDialog.axaml.cs", "ViewState": "AgIAACMAAAAAAAAAAAAcwEkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:46:10.041Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "MainWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\MainWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\MainWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\MainWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\MainWindow.axaml.cs", "ViewState": "AgIAABEBAAAAAAAAAAAmwDgBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:46:04.848Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "HelpWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\HelpWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\HelpWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\HelpWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\HelpWindow.axaml.cs", "ViewState": "AgIAADICAAAAAAAAAAAmwFkCAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:58.256Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "AuthWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\AuthWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\AuthWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\AuthWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\AuthWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:45.326Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "AboutWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\AboutWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\AboutWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\AboutWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\AboutWindow.axaml.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAuwHUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:40.856Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 40, "Title": "UserManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\UserManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\UserManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\UserManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\UserManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:33.889Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "TicketManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\TicketManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\TicketManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\TicketManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\TicketManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:28.059Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "SalesStatisticsToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesStatisticsToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesStatisticsToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesStatisticsToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesStatisticsToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:23.498Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "SalesManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\SalesManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:16.861Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "RouteSchedulesManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteSchedulesManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteSchedulesManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteSchedulesManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteSchedulesManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:11.203Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "RouteManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\RouteManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:06.572Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 46, "Title": "MaintenanceManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\MaintenanceManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\MaintenanceManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\MaintenanceManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\MaintenanceManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:45:02.29Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "JobManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\JobManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\JobManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\JobManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\JobManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:56.964Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "IncomeReportToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\IncomeReportToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\IncomeReportToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\IncomeReportToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\IncomeReportToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:51.614Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "EmployeeManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\EmployeeManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\EmployeeManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\EmployeeManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\EmployeeManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:45.574Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "BusManagementToolWindow.axaml.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\BusManagementToolWindow.axaml.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\BusManagementToolWindow.axaml.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\BusManagementToolWindow.axaml.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Views\\ManagementToolWindowsViews\\BusManagementToolWindow.axaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:39.357Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "ViewModelBase.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ViewModelBase.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ViewModelBase.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ViewModelBase.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ViewModelBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:30.109Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 52, "Title": "UserManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\UserManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\UserManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\UserManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\UserManagementViewModel.cs", "ViewState": "AgIAAC4DAAAAAAAAAAAAwFYDAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:26.23Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 53, "Title": "ToolWindowViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ToolWindowViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ToolWindowViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ToolWindowViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ToolWindowViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:22.614Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 54, "Title": "TicketManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\TicketManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\TicketManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\TicketManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\TicketManagementViewModel.cs", "ViewState": "AgIAAKUBAAAAAAAAAAAYwM4BAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:18.013Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 55, "Title": "SearchViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SearchViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SearchViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SearchViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SearchViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:15.912Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 56, "Title": "SalesManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\SalesManagementViewModel.cs", "ViewState": "AgIAAAgCAAAAAAAAAAAYwDECAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:44:05.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 57, "Title": "RouteSchedulesManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteSchedulesManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteSchedulesManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteSchedulesManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteSchedulesManagementViewModel.cs", "ViewState": "AgIAAPQCAAAAAAAAAAAAwBwDAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:57.233Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 58, "Title": "RouteManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\RouteManagementViewModel.cs", "ViewState": "AgIAAAkCAAAAAAAAAAAAwDECAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:49.141Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 59, "Title": "PropertiesViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\PropertiesViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\PropertiesViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\PropertiesViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\PropertiesViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:47.598Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 60, "Title": "ProblemViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ProblemViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ProblemViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ProblemViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ProblemViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:43.228Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 61, "Title": "NotificationsViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\NotificationsViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\NotificationsViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\NotificationsViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\NotificationsViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:39.673Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 62, "Title": "ModalDialogViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogViewModel.cs", "ViewState": "AgIAAEcAAAAAAAAAAAAIwGwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:35.7Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 63, "Title": "ModalDialogType.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogType.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogType.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogType.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ModalDialogType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:31.934Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 64, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MainWindowViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAiwIAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:28.172Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 65, "Title": "MaintenanceManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MaintenanceManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MaintenanceManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MaintenanceManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\MaintenanceManagementViewModel.cs", "ViewState": "AgIAANkBAAAAAAAAAAAAwAECAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:24.428Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 66, "Title": "JobManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\JobManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\JobManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\JobManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\JobManagementViewModel.cs", "ViewState": "AgIAAJ8BAAAAAAAAAAAAwMcBAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:19.485Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 67, "Title": "ExplorerViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ExplorerViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ExplorerViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ExplorerViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\ExplorerViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:11.523Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 68, "Title": "EmployeeManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\EmployeeManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\EmployeeManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\EmployeeManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\EmployeeManagementViewModel.cs", "ViewState": "AgIAANYBAAAAAAAAAAAAwP4BAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:43:03.598Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 69, "Title": "DebugViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\DebugViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\DebugViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\DebugViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\DebugViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:59.029Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 70, "Title": "BusManagementViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\BusManagementViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\BusManagementViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\BusManagementViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\BusManagementViewModel.cs", "ViewState": "AgIAAKkBAAAAAAAAAAAAwNEBAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:55.216Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 71, "Title": "AuthViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\AuthViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\AuthViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\AuthViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\ViewModels\\AuthViewModel.cs", "ViewState": "AgIAAGgAAAAAAAAAAAAuwJAAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:51.397Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 72, "Title": "ApiClientService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Services\\ApiClientService.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Administration.Avalonia\\Services\\ApiClientService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Administration.Avalonia\\Services\\ApiClientService.cs", "RelativeToolTip": "TicketSalesApp.UI.Administration.Avalonia\\Services\\ApiClientService.cs", "ViewState": "AgIAABQAAAAAAAAAAAAmwDsAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:43.349Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 73, "Title": "TicketSalesService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\TicketSalesService.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\TicketSalesService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\TicketSalesService.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\TicketSalesService.cs", "ViewState": "AgIAAOkAAAAAAAAAAAAYwBIBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:36.26Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 74, "Title": "RoleService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\RoleService.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\RoleService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\RoleService.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\RoleService.cs", "ViewState": "AgIAANoAAAAAAAAAAAAmwAEBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:30.814Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 75, "Title": "QRAuthenticationService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\QRAuthenticationService.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\QRAuthenticationService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\QRAuthenticationService.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\QRAuthenticationService.cs", "ViewState": "AgIAAIYBAAAAAAAAAAAAwK4BAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:25.518Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 76, "Title": "ExportService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\ExportService.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\ExportService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\ExportService.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\ExportService.cs", "ViewState": "AgIAAIIAAAAAAAAAAAAAwKoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:20.924Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 77, "Title": "DataService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\DataService.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\DataService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\DataService.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\DataService.cs", "ViewState": "AgIAABEAAAAAAAAAAAAwwDYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:12.537Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 79, "Title": "AdminActionLogger.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\AdminActionLogger.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\AdminActionLogger.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\AdminActionLogger.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\AdminActionLogger.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAAwHsAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:42:03.704Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 81, "Title": "SkladOrders.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\SkladOrders.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\SkladOrders.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\SkladOrders.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\SkladOrders.cs", "ViewState": "AgIAADMAAAAAAAAAAAAgwFYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:41.571Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 82, "Title": "RouteSchedules.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\RouteSchedules.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\RouteSchedules.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\RouteSchedules.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\RouteSchedules.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAowH4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:37.545Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 84, "Title": "QRLoginPayload.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\QRLoginPayload.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\QRLoginPayload.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\QRLoginPayload.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\QRLoginPayload.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:29.662Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 85, "Title": "Prodazha.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Prodazha.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Prodazha.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Prodazha.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Prodazha.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:25.315Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 87, "Title": "Obsluzhivanie.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Obsluzhivanie.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Obsluzhivanie.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Obsluzhivanie.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Obsluzhivanie.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:17.307Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 88, "Title": "Marshut.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Marshut.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Marshut.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Marshut.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Marshut.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:13.504Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 89, "Title": "Job.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Job.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Job.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Job.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Job.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:09.131Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 90, "Title": "Form.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Form.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Form.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Form.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Form.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:41:03.937Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 78, "Title": "AuthenticationService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\AuthenticationService.cs", "RelativeDocumentMoniker": "TicketSalesApp.Services\\Implementations\\AuthenticationService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\Implementations\\AuthenticationService.cs", "RelativeToolTip": "TicketSalesApp.Services\\Implementations\\AuthenticationService.cs", "ViewState": "AgIAAJEAAAAAAAAAAAAYwLoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:24:35.492Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 91, "Title": "Employee.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Employee.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Employee.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Employee.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Employee.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:40:59.68Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 93, "Title": "Avtobus.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\TicketSalesApp.Core\\Models\\Avtobus.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Avtobus.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\TicketSalesApp.Core\\Models\\Avtobus.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Avtobus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:40:52.185Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 80, "Title": "User.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\User.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\User.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\User.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\User.cs", "ViewState": "AgIAABsAAAAAAAAAAAA1wD4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T15:08:58.013Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 94, "Title": "AdminActionLog.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\AdminActionLog.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\AdminActionLog.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\AdminActionLog.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\AdminActionLog.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:40:47.925Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 95, "Title": "DbInitializer.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Data\\DbInitializer.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Data\\DbInitializer.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Data\\DbInitializer.cs", "RelativeToolTip": "TicketSalesApp.Core\\Data\\DbInitializer.cs", "ViewState": "AgIAALsEAAAAAAAAAAAAwOMEAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:40:43.43Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 83, "Title": "Roles.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Roles.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Roles.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Roles.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Roles.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAgwGgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T15:08:51.694Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 97, "Title": "Startup.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Startup.cs", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\Startup.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\Startup.cs", "RelativeToolTip": "TicketSalesApp.AdminServer\\Startup.cs", "ViewState": "AgIAAKwBAAAAAAAAAAAYwNUBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:40:26.788Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 86, "Title": "Permissions.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Permissions.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Permissions.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Permissions.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Permissions.cs", "ViewState": "AgIAACgAAAAAAAAAAAAgwEsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T15:08:55.333Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 92, "Title": "Bilet.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Bilet.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Models\\Bilet.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Models\\Bilet.cs", "RelativeToolTip": "TicketSalesApp.Core\\Models\\Bilet.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:28:13.885Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 96, "Title": "AppDbContext.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Data\\AppDbContext.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core\\Data\\AppDbContext.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core\\Data\\AppDbContext.cs", "RelativeToolTip": "TicketSalesApp.Core\\Data\\AppDbContext.cs", "ViewState": "AgIAAJcAAAAAAAAAAAAYwMAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:23:15.621Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 101, "Title": "User.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\User.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\User.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\User.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\User.cs", "ViewState": "AgIAABIAAAAAAAAAAAAowBUAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T19:07:07.944Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 103, "Title": "Permissions.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Permissions.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\Permissions.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Permissions.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\Permissions.cs", "ViewState": "AgIAABEAAAAAAAAAAAAmwBMAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T19:06:37.211Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 105, "Title": "Prodazha.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Prodazha.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\Prodazha.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Prodazha.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\Prodazha.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:59:45.797Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 107, "Title": "Employee.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Employee.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\Employee.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Employee.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\Employee.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:59:23.057Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 104, "Title": "Avtobus.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\TicketSalesApp.Core.Legacy\\Models\\Avtobus.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\Avtobus.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\TicketSalesApp.Core.Legacy\\Models\\Avtobus.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\Avtobus.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAxwBIAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:59:12.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 102, "Title": "Bilet.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Bilet.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\Bilet.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Bilet.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\Bilet.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAiwBQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:59:00.354Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 106, "Title": "Marshut.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Marshut.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\Marshut.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\Marshut.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\Marshut.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAywBsAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:58:51.21Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 108, "Title": "RouteSchedules.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\RouteSchedules.cs", "RelativeDocumentMoniker": "TicketSalesApp.Core.Legacy\\Models\\RouteSchedules.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\Models\\RouteSchedules.cs", "RelativeToolTip": "TicketSalesApp.Core.Legacy\\Models\\RouteSchedules.cs", "ViewState": "AgIAABkAAAAAAAAAAAAewCYAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T18:53:16.444Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 109, "Title": "TicketSalesApp.UI.LegacyForms.DX.Windows.csproj", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\TicketSalesApp.UI.LegacyForms.DX.Windows.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAADYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-04-22T15:16:26.586Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 110, "Title": "Microsoft.NET.TargetFrameworkInference.targets", "DocumentMoniker": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.TargetFrameworkInference.targets", "ToolTip": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.NET.TargetFrameworkInference.targets", "ViewState": "AgIAAEwAAAAAAAAAAAApwF8AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-04-22T15:05:47.098Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 111, "Title": "TicketSalesApp.AdminServer.csproj", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj", "RelativeDocumentMoniker": "TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj", "RelativeToolTip": "TicketSalesApp.AdminServer\\TicketSalesApp.AdminServer.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-04-22T15:05:23.266Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 112, "Title": "TicketSalesApp.Services.csproj", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\TicketSalesApp.Services.csproj", "RelativeDocumentMoniker": "TicketSalesApp.Services\\TicketSalesApp.Services.csproj", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Services\\TicketSalesApp.Services.csproj", "RelativeToolTip": "TicketSalesApp.Services\\TicketSalesApp.Services.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-04-22T15:05:19.989Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 113, "Title": "Microsoft.PackageDependencyResolution.targets", "DocumentMoniker": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.PackageDependencyResolution.targets", "ToolTip": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\Sdks\\Microsoft.NET.Sdk\\targets\\Microsoft.PackageDependencyResolution.targets", "ViewState": "AgIAAPgAAAAAAAAAAAAMwAkBAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-04-22T14:37:38.652Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 114, "Title": "frmSalesManagement.Designer.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesManagement.Designer.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesManagement.Designer.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesManagement.Designer.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesManagement.Designer.cs", "ViewState": "AgIAACEAAAAAAAAAAAAwwCEAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-21T11:31:19.885Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 115, "Title": "frmSalesStatistics.Designer.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesStatistics.Designer.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesStatistics.Designer.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesStatistics.Designer.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmSalesStatistics.Designer.cs", "ViewState": "AgIAABYAAAAAAAAAAAAmwBkAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-21T11:30:03.751Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 116, "Title": "frmTicketManagement.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmTicketManagement.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmTicketManagement.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmTicketManagement.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmTicketManagement.cs", "ViewState": "AgIAAAsBAAAAAAAAAAAswBkBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T10:47:54.866Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 117, "Title": "frmIncomeReport.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmIncomeReport.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmIncomeReport.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmIncomeReport.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmIncomeReport.cs", "ViewState": "AgIAALoAAAAAAAAAAAAqwM4AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-20T10:47:39.217Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 118, "Title": "frmMaintenanceManagement.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmMaintenanceManagement.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmMaintenanceManagement.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmMaintenanceManagement.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmMaintenanceManagement.cs", "ViewState": "AgIAAPQAAAAAAAAAAAAUwAMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T12:53:19.903Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 119, "Title": "frmRouteSchedulesManagement.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs", "ViewState": "AgIAAHwAAAAAAAAAAAAWwOUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T12:48:04.536Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 100, "Title": "Program.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\Program.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\Program.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\Program.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T12:19:33.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 120, "Title": "frmBusManagement.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmBusManagement.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmBusManagement.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmBusManagement.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmBusManagement.cs", "ViewState": "AgIAACMAAAAAAAAAAAAgwA4AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T11:31:23.518Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 99, "Title": "frmRouteManagement.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteManagement.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteManagement.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteManagement.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteManagement.cs", "ViewState": "AgIAADIBAAAAAAAAAAAmwDkBAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T11:31:12.413Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 122, "Title": "frmLogin.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs", "ViewState": "AgIAAEQAAAAAAAAAAIBZwAcBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:43:04.438Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 125, "Title": "SearchResultsViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\SearchResultsViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\ViewModels\\SearchResultsViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\SearchResultsViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\ViewModels\\SearchResultsViewModel.cs", "ViewState": "AgIAAAkAAAAAAAAAAIBZwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:21:45.238Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 126, "Title": "MyTicketsViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\MyTicketsViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\ViewModels\\MyTicketsViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\MyTicketsViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\ViewModels\\MyTicketsViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:21:44.013Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 123, "Title": "AuthWindow.axaml", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\Views\\AuthWindow.axaml", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\Views\\AuthWindow.axaml", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\Views\\AuthWindow.axaml", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\Views\\AuthWindow.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-04-19T10:20:59.418Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 130, "Title": "AboutView.axaml", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\Views\\Pages\\AboutView.axaml", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\Views\\Pages\\AboutView.axaml", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\Views\\Pages\\AboutView.axaml", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\Views\\Pages\\AboutView.axaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-04-19T10:20:55.514Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 127, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\ViewModels\\MainWindowViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAYwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:20:50.875Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 128, "Title": "HelpViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\HelpViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\ViewModels\\HelpViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\HelpViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\ViewModels\\HelpViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:20:50.088Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 129, "Title": "AboutViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\AboutViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\ViewModels\\AboutViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\AboutViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\ViewModels\\AboutViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:20:47.914Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 124, "Title": "AuthViewModel.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\AuthViewModel.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.Avalonia\\ViewModels\\AuthViewModel.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.Avalonia\\ViewModels\\AuthViewModel.cs", "RelativeToolTip": "TicketSalesApp.UI.Avalonia\\ViewModels\\AuthViewModel.cs", "ViewState": "AgIAAAsBAAAAAAAAAAAYwCEBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:20:14.969Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 132, "Title": "Form1.Designer.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.Designer.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.Designer.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.Designer.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\Form1.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:19:40.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 131, "Title": "ApiClientService.cs", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\ApiClientService.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\ApiClientService.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\ApiClientService.cs", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\ApiClientService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:19:36.02Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 98, "Title": "frmRouteSchedulesManagement.cs [Design]", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs [Design]", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmRouteSchedulesManagement.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T15:37:51.674Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 121, "Title": "frmLogin.cs [Design]", "DocumentMoniker": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs", "RelativeDocumentMoniker": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs", "ToolTip": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs [Design]", "RelativeToolTip": "TicketSalesApp.UI.LegacyForms.DX.Windows\\frmLogin.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-19T10:31:35.175Z", "EditorCaption": " [Design]"}]}]}]}