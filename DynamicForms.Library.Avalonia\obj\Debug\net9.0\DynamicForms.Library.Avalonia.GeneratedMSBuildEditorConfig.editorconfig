is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = DynamicForms.Library.Avalonia
build_property.ProjectDir = G:\codesecondfolder\BRU-Avtopark-Avtobusov\DynamicForms.Library.Avalonia\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/DynamicFormControl.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormColorPicker.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormDictionaryComboBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormEnableDisableReorderControl.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormEnableDisableReorderControlItem.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormLabeledFieldSideBySide.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormLabeledFieldVertical.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Fields/DynamicFormSliderControl.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Groups/DynamicFormGroupLayoutTwoColumn.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Groups/DynamicFormGroupLayoutVertical.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Groups/DynamicFormGroupStyleBasic.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Groups/DynamicFormGroupStyleExpander.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/DynamicForms.Library.Avalonia/Groups/DynamicFormGroupStyleGroupBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
