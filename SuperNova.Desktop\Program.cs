﻿using Avalonia;
using System;
using System.Threading.Tasks;
using Serilog;
using Serilog.Events;
using Avalonia.ReactiveUI;
using Avalonia.Controls;
using Classic.CommonControls;
using Avalonia.Media.Fonts;
using System.IO;

namespace SuperNova.Desktop;

sealed class Program
{
    // Initialization code. Don't use any Avalonia, third-party APIs or any
    // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
    // yet and stuff might break.
    [STAThread]
    public static int Main(string[] args)
    {
        // CRITICAL: Initialize stack overflow protection BEFORE anything else
        try
        {
            InitializeStackOverflowProtection();
        }
        catch (Exception protectionEx)
        {
            Console.WriteLine($"Failed to initialize stack overflow protection: {protectionEx.Message}");
            // Continue anyway - better to try running than fail completely
        }

        try
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.Debug()
                .WriteTo.File("logs/app-.log",
                    rollingInterval: RollingInterval.Day,
                    restrictedToMinimumLevel: LogEventLevel.Information)
                .CreateLogger();

            Log.Information("Starting application with stack overflow protection...");
            FixCurrentWorkingDictionary();

            // Wrap Avalonia startup in additional protection
            return SafeStartAvalonia(args);
        }
        catch (StackOverflowException)
        {
            Console.WriteLine("CRITICAL: Stack overflow detected in main application startup!");
            try
            {
                Log.Fatal("Stack overflow exception in main application startup");
            }
            catch
            {
                // If logging fails, just exit
            }
            return 2;
        }
        catch (Exception ex)
        {
            try
            {
                Log.Fatal(ex, "Application terminated unexpectedly");
            }
            catch
            {
                Console.WriteLine($"Application terminated unexpectedly: {ex.Message}");
            }
            return 1;
        }
        finally
        {
            try
            {
                Log.CloseAndFlush();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }

    /// <summary>
    /// Initialize all stack overflow protection systems
    /// </summary>
    private static void InitializeStackOverflowProtection()
    {
        try
        {
            // Initialize all protection systems in the correct order
            SuperNova.Converters.CultureInfoProtection.Initialize();
            SuperNova.Converters.StringFormatInterceptor.Initialize();
            SuperNova.Converters.GlobalTypeDescriptorHook.InstallHook();
            SuperNova.Converters.AvaloniaTypeConverterInterceptor.Initialize();
            SuperNova.Converters.AvaloniaBindingInterceptor.Initialize();

            Console.WriteLine("Stack overflow protection initialized successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Stack overflow protection initialization failed: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Safely start Avalonia with additional protection
    /// </summary>
    private static int SafeStartAvalonia(string[] args)
    {
        try
        {
            var appBuilder = BuildAvaloniaApp();

            // Add additional safety checks before starting
            if (appBuilder == null)
            {
                Log.Error("Failed to build Avalonia app");
                return 3;
            }

            return appBuilder.StartWithClassicDesktopLifetime(args, ShutdownMode.OnExplicitShutdown);
        }
        catch (StackOverflowException)
        {
            Log.Fatal("Stack overflow in Avalonia startup");
            return 4;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Error starting Avalonia application");
            return 5;
        }
    }

    private static void FixCurrentWorkingDictionary()
    {
        try
        {
            if (Path.GetDirectoryName(Environment.ProcessPath) is { } dir)
            {
                Environment.CurrentDirectory = dir;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Failed to fix working directory: {ex.Message}");
            // Continue anyway
        }
    }

    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp()
    {
        try
        {
            var builder = AppBuilder.Configure<App>()
                .UsePlatformDetect()
                .UseMessageBoxSounds()
                .LogToTrace();

            // Safely configure fonts with error handling
            try
            {
                builder = builder.ConfigureFonts(manager =>
                {
                    try
                    {
                        manager.AddFontCollection(new EmbeddedFontCollection(new Uri("fonts:App", UriKind.Absolute),
                            new Uri("avares://AvaloniaVisualBasic/Resources", UriKind.Absolute)));
                    }
                    catch (Exception fontEx)
                    {
                        Console.WriteLine($"Warning: Failed to configure fonts: {fontEx.Message}");
                        // Continue without custom fonts
                    }
                });
            }
            catch (Exception configEx)
            {
                Console.WriteLine($"Warning: Failed to configure font manager: {configEx.Message}");
                // Continue with basic configuration
            }

            return builder;
        }
        catch (StackOverflowException)
        {
            Console.WriteLine("Stack overflow in BuildAvaloniaApp");
            throw;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error building Avalonia app: {ex.Message}");
            throw;
        }
    }
}
