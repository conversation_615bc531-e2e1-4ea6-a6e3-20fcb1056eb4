2025-07-22 17:25:35.562 +03:00 [INF] Starting application...
2025-07-22 17:26:32.152 +03:00 [INF] Starting application...
2025-07-22 17:27:25.055 +03:00 [INF] Starting application...
2025-07-22 17:29:01.656 +03:00 [INF] Starting application...
2025-07-22 17:33:24.205 +03:00 [INF] Starting application...
2025-07-22 17:33:50.368 +03:00 [INF] Starting application...
2025-07-22 17:34:13.646 +03:00 [INF] Starting application...
2025-07-22 17:35:05.277 +03:00 [INF] Starting application...
2025-07-22 17:39:25.016 +03:00 [INF] Starting application...
2025-07-22 17:47:58.253 +03:00 [INF] Starting application...
2025-07-22 18:24:26.880 +03:00 [INF] Starting application...
2025-07-22 18:30:00.489 +03:00 [INF] Starting application...
2025-07-22 18:30:01.112 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 18:36:30.747 +03:00 [INF] Starting application...
2025-07-22 18:36:31.321 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 18:36:31.323 +03:00 [INF] String formatting protection initialized
2025-07-22 18:43:35.858 +03:00 [INF] Starting application...
2025-07-22 18:43:36.456 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 18:43:36.458 +03:00 [INF] String formatting protection initialized
2025-07-22 18:50:42.705 +03:00 [INF] Starting application...
2025-07-22 18:50:43.353 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 18:50:43.355 +03:00 [INF] String formatting protection initialized
2025-07-22 18:51:24.385 +03:00 [INF] Starting application...
2025-07-22 18:51:25.053 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 18:51:25.055 +03:00 [INF] String formatting protection initialized
2025-07-22 18:54:02.933 +03:00 [INF] Starting application...
2025-07-22 18:54:03.878 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 18:54:03.880 +03:00 [INF] String formatting protection initialized
2025-07-22 19:02:07.465 +03:00 [INF] Starting application...
2025-07-22 19:02:08.394 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 19:02:08.395 +03:00 [INF] String formatting protection initialized
2025-07-22 19:05:25.337 +03:00 [INF] Starting application with stack overflow protection...
2025-07-22 19:05:25.870 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 19:05:25.872 +03:00 [INF] String formatting protection initialized
2025-07-22 19:09:00.896 +03:00 [INF] Starting application with stack overflow protection...
2025-07-22 19:09:01.466 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 19:09:01.468 +03:00 [INF] String formatting protection initialized with InvariantCulture
2025-07-22 19:12:34.768 +03:00 [INF] Starting application with stack overflow protection...
2025-07-22 19:12:35.357 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 19:12:35.358 +03:00 [INF] String formatting protection initialized with InvariantCulture
2025-07-22 19:21:04.088 +03:00 [INF] Starting application with stack overflow protection...
2025-07-22 19:21:04.579 +03:00 [INF] TypeConverter protection system initialized
2025-07-22 19:21:04.580 +03:00 [INF] String formatting protection initialized with InvariantCulture
[2025-07-22 19:42:06.192 INF] [] === APPLICATION STARTUP INITIATED ===
[2025-07-22 19:42:06.253 INF] [] Serilog logger initialized successfully
[2025-07-22 19:42:06.255 INF] [] Starting application with comprehensive stack overflow protection...
[2025-07-22 19:42:06.258 DBG] [] Fixing current working directory...
[2025-07-22 19:42:06.261 DBG] [] Fixing current working directory...
[2025-07-22 19:42:06.263 DBG] [] Process path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\bin\Debug\net9.0\SuperNova.Desktop.exe
[2025-07-22 19:42:06.266 DBG] [] Setting current directory to: G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\bin\Debug\net9.0
[2025-07-22 19:42:06.268 DBG] [] Current directory set successfully to: G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\bin\Debug\net9.0
[2025-07-22 19:42:06.269 DBG] [] Working directory fixed successfully
[2025-07-22 19:42:06.271 INF] [] Initiating safe Avalonia startup...
[2025-07-22 19:42:06.343 INF] [] === AVALONIA STARTUP PHASE ===
[2025-07-22 19:42:06.344 DBG] [] Building Avalonia application...
[2025-07-22 19:42:06.379 DBG] [] === BUILDING AVALONIA APP ===
[2025-07-22 19:42:06.382 DBG] [] Configuring base AppBuilder...
[2025-07-22 19:42:06.384 DBG] [] AppBuilder.Configure<App>() completed
[2025-07-22 19:42:06.385 DBG] [] Adding UsePlatformDetect()...
[2025-07-22 19:42:06.465 DBG] [] UsePlatformDetect() completed
[2025-07-22 19:42:06.466 DBG] [] Adding UseMessageBoxSounds()...
[2025-07-22 19:42:06.476 DBG] [] UseMessageBoxSounds() completed
[2025-07-22 19:42:06.480 DBG] [] Adding LogToTrace()...
[2025-07-22 19:42:06.483 DBG] [] LogToTrace() completed
[2025-07-22 19:42:06.485 DBG] [] Configuring fonts...
[2025-07-22 19:42:06.487 DBG] [] Font configuration completed
[2025-07-22 19:42:06.488 INF] [] Avalonia app builder configured successfully
[2025-07-22 19:42:06.490 DBG] [] Avalonia app builder created successfully
[2025-07-22 19:42:06.491 INF] [] Starting Avalonia with ClassicDesktopLifetime...
[2025-07-22 19:42:06.493 DBG] [] Arguments: 
[2025-07-22 19:42:06.494 DBG] [] Shutdown mode: OnExplicitShutdown
[2025-07-22 19:42:07.149 INF] [] TypeConverter protection system initialized
[2025-07-22 19:42:07.151 INF] [] String formatting protection initialized with InvariantCulture
