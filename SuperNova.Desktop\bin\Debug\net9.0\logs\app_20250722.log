[2025-07-22 17:25:37.211] [INF] [] Application framework initialization starting
[2025-07-22 17:25:37.281] [DBG] [] Creating splash screen
[2025-07-22 17:25:37.502] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:25:37.509] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0020190"
[2025-07-22 17:25:37.627] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:25:37.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017268"
[2025-07-22 17:25:38.672] [DBG] [] Checking server availability
[2025-07-22 17:25:38.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:25:38.886] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0026683"
[2025-07-22 17:25:43.068] [ERR] [] Server not available
[2025-07-22 17:25:43.073] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:25:43.080] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0053146"
[2025-07-22 17:26:33.086] [INF] [] Application framework initialization starting
[2025-07-22 17:26:33.166] [DBG] [] Creating splash screen
[2025-07-22 17:26:33.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:33.395] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0015606"
[2025-07-22 17:26:33.501] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:33.504] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007117"
[2025-07-22 17:26:34.542] [DBG] [] Checking server availability
[2025-07-22 17:26:34.669] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:34.671] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006065"
[2025-07-22 17:26:36.890] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:36.894] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004065"
[2025-07-22 17:26:39.931] [DBG] [] Splash screen closed
[2025-07-22 17:26:39.933] [DBG] [] Creating authentication window
[2025-07-22 17:26:40.113] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:40.115] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000030"
[2025-07-22 17:26:40.278] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:40.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000028"
[2025-07-22 17:26:41.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:41.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008277"
[2025-07-22 17:26:42.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:42.041] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003855"
[2025-07-22 17:26:42.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.277] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003374"
[2025-07-22 17:26:42.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.420] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0022418"
[2025-07-22 17:26:42.702] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002058"
[2025-07-22 17:26:42.862] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.864] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002059"
[2025-07-22 17:26:42.979] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:42.980] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002848"
[2025-07-22 17:26:43.599] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:43.603] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008994"
[2025-07-22 17:26:43.959] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:43.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001907"
[2025-07-22 17:26:44.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:44.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002045"
[2025-07-22 17:26:44.350] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:44.352] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001860"
[2025-07-22 17:26:44.543] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:44.545] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001573"
[2025-07-22 17:26:44.714] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:26:44.747] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0049446"
[2025-07-22 17:26:45.003] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:45.005] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001862"
[2025-07-22 17:26:45.253] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001788"
[2025-07-22 17:26:45.258] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.259] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001299"
[2025-07-22 17:26:45.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.400] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001505"
[2025-07-22 17:26:45.687] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.689] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001507"
[2025-07-22 17:26:45.813] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.815] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001633"
[2025-07-22 17:26:45.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.983] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001328"
[2025-07-22 17:26:46.127] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:46.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001761"
[2025-07-22 17:26:46.426] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:46.427] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001269"
[2025-07-22 17:26:46.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:46.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001800"
[2025-07-22 17:26:46.684] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:46.685] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001378"
[2025-07-22 17:26:47.073] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:47.077] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002546"
[2025-07-22 17:26:47.212] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:47.215] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002313"
[2025-07-22 17:26:47.362] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:47.364] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001794"
[2025-07-22 17:26:47.439] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:47.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001300"
[2025-07-22 17:26:47.680] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:47.682] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001392"
[2025-07-22 17:26:48.156] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:48.158] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002320"
[2025-07-22 17:26:48.388] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:48.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001243"
[2025-07-22 17:26:48.594] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:48.596] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001634"
[2025-07-22 17:26:49.752] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:26:49.754] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001034"
[2025-07-22 17:26:49.927] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:49.930] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009993"
[2025-07-22 17:26:52.641] [INF] [] Administrator successfully authenticated
[2025-07-22 17:26:52.657] [DBG] [] Auth window closed with result: true
[2025-07-22 17:26:52.659] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:26:52.739] [INF] [] Main window initialized
[2025-07-22 17:26:52.787] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.790] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.797] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.799] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.801] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.803] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.806] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.808] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.810] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.812] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.815] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.817] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.867] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:26:52.869] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:26:52.871] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:26:52.872] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:26:52.875] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:26:52.947] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.952] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.955] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.956] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.958] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.960] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.962] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.964] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.966] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.967] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.969] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.970] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.972] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.974] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.975] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.977] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.979] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.980] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.982] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.983] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.985] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.986] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.988] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.989] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.991] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.992] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.994] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.995] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.997] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.998] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.000] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.002] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.003] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.004] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.006] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.008] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.009] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.011] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.013] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.014] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.016] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.017] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.109] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:26:53.120] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:26:53.123] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0012321"
[2025-07-22 17:26:53.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:53.133] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017350"
[2025-07-22 17:26:53.150] [INF] [] Application framework initialization completed
[2025-07-22 17:26:53.159] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:26:53.161] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002090"
[2025-07-22 17:26:53.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:53.832] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000439"
[2025-07-22 17:26:53.834] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:26:53.835] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000419"
[2025-07-22 17:26:53.917] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:53.919] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000420"
[2025-07-22 17:26:54.935] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.937] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0010776"
[2025-07-22 17:26:54.954] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004030"
[2025-07-22 17:26:54.968] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.970] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007411"
[2025-07-22 17:26:54.988] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.990] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006588"
[2025-07-22 17:26:55.003] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.005] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008246"
[2025-07-22 17:26:55.022] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003971"
[2025-07-22 17:26:55.037] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003886"
[2025-07-22 17:26:55.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.062] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006596"
[2025-07-22 17:26:55.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005243"
[2025-07-22 17:26:56.585] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.588] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.591] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.622] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:56.624] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000273"
[2025-07-22 17:26:56.630] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:56.632] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000539"
[2025-07-22 17:26:56.835] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.837] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.840] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.843] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.847] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:56.849] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:26:56.852] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:56.854] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000600"
[2025-07-22 17:26:57.264] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.267] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.272] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.275] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.279] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 7 To arrange: 0
[2025-07-22 17:26:57.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009912"
[2025-07-22 17:26:57.286] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:57.288] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000368"
[2025-07-22 17:26:58.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:58.132] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000049"
[2025-07-22 17:26:58.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 8 To arrange: 0
[2025-07-22 17:26:58.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001788"
[2025-07-22 17:26:58.175] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 2
[2025-07-22 17:26:58.177] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002625"
[2025-07-22 17:27:01.170] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:01.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0487054"
[2025-07-22 17:27:02.137] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
[2025-07-22 17:27:02.140] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001683"
[2025-07-22 17:27:02.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:27:02.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000239"
[2025-07-22 17:27:02.761] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:02.764] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0010458"
[2025-07-22 17:27:03.240] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:03.242] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006902"
[2025-07-22 17:27:04.051] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:04.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002704"
[2025-07-22 17:27:04.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:04.707] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011686"
[2025-07-22 17:27:05.146] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:05.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003492"
[2025-07-22 17:27:05.513] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:05.514] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002543"
[2025-07-22 17:27:06.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:06.025] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002316"
[2025-07-22 17:27:06.475] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:06.476] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003539"
[2025-07-22 17:27:06.883] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:06.885] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002367"
[2025-07-22 17:27:07.310] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:07.312] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003937"
[2025-07-22 17:27:07.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:07.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005685"
[2025-07-22 17:27:07.993] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:07.995] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003095"
[2025-07-22 17:27:08.413] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:08.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003238"
[2025-07-22 17:27:08.773] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:08.774] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002997"
[2025-07-22 17:27:09.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:09.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003339"
[2025-07-22 17:27:09.665] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:09.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002327"
[2025-07-22 17:27:10.511] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:10.513] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006146"
[2025-07-22 17:27:10.772] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:10.773] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002323"
[2025-07-22 17:27:11.095] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:11.096] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002520"
[2025-07-22 17:27:12.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:12.633] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001870"
[2025-07-22 17:27:13.727] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:13.730] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004243"
[2025-07-22 17:27:26.112] [INF] [] Application framework initialization starting
[2025-07-22 17:27:26.183] [DBG] [] Creating splash screen
[2025-07-22 17:27:26.377] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:26.384] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0022666"
[2025-07-22 17:27:26.486] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:26.488] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007389"
[2025-07-22 17:27:27.526] [DBG] [] Checking server availability
[2025-07-22 17:27:27.622] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:27.624] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005929"
[2025-07-22 17:27:29.818] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:29.819] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004064"
[2025-07-22 17:27:32.854] [DBG] [] Splash screen closed
[2025-07-22 17:27:32.856] [DBG] [] Creating authentication window
[2025-07-22 17:27:33.030] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:33.031] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000046"
[2025-07-22 17:27:33.179] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:33.180] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000032"
[2025-07-22 17:27:34.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:27:34.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007818"
[2025-07-22 17:27:34.657] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:34.658] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003637"
[2025-07-22 17:27:34.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:34.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004811"
[2025-07-22 17:27:35.518] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:35.519] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002892"
[2025-07-22 17:27:35.822] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:35.824] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002823"
[2025-07-22 17:27:36.077] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:36.079] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003331"
[2025-07-22 17:27:36.345] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:36.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0044481"
[2025-07-22 17:27:36.666] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:36.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008795"
[2025-07-22 17:27:36.903] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:36.904] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001801"
[2025-07-22 17:27:37.020] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:37.022] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003457"
[2025-07-22 17:27:37.216] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:37.218] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002601"
[2025-07-22 17:27:37.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:37.399] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001837"
[2025-07-22 17:27:38.279] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:27:38.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001527"
[2025-07-22 17:27:38.416] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:27:38.418] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007414"
[2025-07-22 17:27:41.031] [INF] [] Administrator successfully authenticated
[2025-07-22 17:27:41.051] [DBG] [] Auth window closed with result: true
[2025-07-22 17:27:41.052] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:27:41.128] [INF] [] Main window initialized
[2025-07-22 17:27:41.171] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.173] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.179] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.181] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.182] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.184] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.186] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.188] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.189] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.190] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.192] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.194] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.233] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:27:41.235] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:27:41.236] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:27:41.237] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:27:41.239] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:27:41.293] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.296] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.298] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.299] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.300] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.302] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.303] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.305] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.306] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.307] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.308] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.310] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.312] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.313] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.314] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.315] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.317] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.318] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.320] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.321] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.322] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.324] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.325] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.327] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.328] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.329] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.330] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.332] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.333] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.334] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.336] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.337] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.338] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.340] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.341] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.342] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.343] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.345] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.346] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.347] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.348] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.350] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.433] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:27:41.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:27:41.443] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011649"
[2025-07-22 17:27:41.447] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:41.450] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017698"
[2025-07-22 17:27:41.465] [INF] [] Application framework initialization completed
[2025-07-22 17:27:41.471] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:27:41.473] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002080"
[2025-07-22 17:27:42.679] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:42.681] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:42.688] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:42.689] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000440"
[2025-07-22 17:27:42.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:27:42.695] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000530"
[2025-07-22 17:27:43.530] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:43.532] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000064"
[2025-07-22 17:27:43.598] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:43.602] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0023476"
[2025-07-22 17:27:43.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:27:43.633] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003442"
[2025-07-22 17:27:44.985] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:44.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002147"
[2025-07-22 17:27:45.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:45.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004450"
[2025-07-22 17:29:02.753] [INF] [] Application framework initialization starting
[2025-07-22 17:29:02.853] [DBG] [] Creating splash screen
[2025-07-22 17:29:03.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:03.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0016636"
[2025-07-22 17:29:03.162] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:03.164] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008061"
[2025-07-22 17:29:04.192] [DBG] [] Checking server availability
[2025-07-22 17:29:04.283] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:04.287] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007442"
[2025-07-22 17:29:06.463] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:06.465] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004018"
[2025-07-22 17:29:09.504] [DBG] [] Splash screen closed
[2025-07-22 17:29:09.506] [DBG] [] Creating authentication window
[2025-07-22 17:29:09.588] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:09.589] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000034"
[2025-07-22 17:29:09.739] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:09.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000031"
[2025-07-22 17:29:10.652] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:10.655] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008794"
[2025-07-22 17:29:10.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:10.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003927"
[2025-07-22 17:29:11.175] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:11.177] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002780"
[2025-07-22 17:29:11.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:11.882] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002305"
[2025-07-22 17:29:12.066] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:12.068] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002153"
[2025-07-22 17:29:12.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:12.252] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004073"
[2025-07-22 17:29:12.643] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:29:12.649] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0043675"
[2025-07-22 17:29:12.878] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:12.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003049"
[2025-07-22 17:29:13.095] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.097] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002059"
[2025-07-22 17:29:13.205] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.206] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002218"
[2025-07-22 17:29:13.388] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002619"
[2025-07-22 17:29:13.579] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.581] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001890"
[2025-07-22 17:29:13.583] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.586] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005280"
[2025-07-22 17:29:14.109] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.111] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001497"
[2025-07-22 17:29:14.303] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.305] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001448"
[2025-07-22 17:29:14.478] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.480] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002464"
[2025-07-22 17:29:14.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.639] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002342"
[2025-07-22 17:29:14.795] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.797] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001538"
[2025-07-22 17:29:14.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:14.936] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002010"
[2025-07-22 17:29:15.053] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:15.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002212"
[2025-07-22 17:29:15.257] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.262] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002531"
[2025-07-22 17:29:15.433] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.435] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001315"
[2025-07-22 17:29:15.628] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001314"
[2025-07-22 17:29:15.828] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001684"
[2025-07-22 17:29:17.202] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:29:17.204] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001420"
[2025-07-22 17:29:17.392] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:17.395] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006628"
[2025-07-22 17:29:19.924] [INF] [] Administrator successfully authenticated
[2025-07-22 17:29:19.943] [DBG] [] Auth window closed with result: true
[2025-07-22 17:29:19.945] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:29:20.021] [INF] [] Main window initialized
[2025-07-22 17:29:20.063] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.066] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.073] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.075] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.077] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.079] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.082] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.084] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.086] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.088] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.090] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.092] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.131] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:29:20.133] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:29:20.135] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:29:20.136] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:29:20.139] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:29:20.197] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.201] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.202] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.204] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.206] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.207] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.209] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.210] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.212] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.213] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.215] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.216] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.218] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.219] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.221] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.222] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.224] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.226] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.227] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.229] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.231] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.233] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.235] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.237] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.238] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.240] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.241] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.243] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.244] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.246] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.247] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.249] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.250] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.252] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.253] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.255] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.256] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.258] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.259] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.261] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.262] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.264] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.345] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:29:20.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:29:20.356] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0010943"
[2025-07-22 17:29:20.361] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:20.364] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0021804"
[2025-07-22 17:29:20.381] [INF] [] Application framework initialization completed
[2025-07-22 17:29:20.391] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:29:20.393] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002672"
[2025-07-22 17:29:21.206] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:21.207] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000409"
[2025-07-22 17:29:21.256] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:29:21.258] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000948"
[2025-07-22 17:29:21.273] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:21.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000789"
[2025-07-22 17:29:21.559] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.562] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.564] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.572] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:21.573] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000215"
[2025-07-22 17:29:21.578] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:21.580] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000529"
[2025-07-22 17:29:21.966] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.968] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.970] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.973] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:21.977] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 17:29:21.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:21.983] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000526"
[2025-07-22 17:29:22.157] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.161] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.166] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.189] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.193] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 7 To arrange: 0
[2025-07-22 17:29:22.197] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0018282"
[2025-07-22 17:29:22.201] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:22.203] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000673"
[2025-07-22 17:29:22.425] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.428] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.431] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.434] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.437] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.439] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.442] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.446] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:22.447] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000185"
[2025-07-22 17:29:22.451] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:22.453] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000674"
[2025-07-22 17:29:23.186] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:23.188] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000035"
[2025-07-22 17:29:23.211] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:29:23.213] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006481"
[2025-07-22 17:29:23.238] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:29:23.240] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002962"
[2025-07-22 17:29:25.465] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:25.490] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0229402"
[2025-07-22 17:29:26.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:26.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001194"
[2025-07-22 17:29:26.355] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:29:26.357] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000415"
[2025-07-22 17:29:27.471] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:29:27.473] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002421"
[2025-07-22 17:29:28.890] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:29:28.892] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002497"
[2025-07-22 17:29:30.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:29:30.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002805"
[2025-07-22 17:33:25.711] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 32
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:33:51.545] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 32
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:34:14.483] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 30
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:35:06.328] [INF] [] Application framework initialization starting
[2025-07-22 17:35:06.436] [DBG] [] Creating splash screen
[2025-07-22 17:35:06.609] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:06.613] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0016820"
[2025-07-22 17:35:06.693] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:06.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005816"
[2025-07-22 17:35:07.720] [DBG] [] Checking server availability
[2025-07-22 17:35:07.808] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:07.810] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006729"
[2025-07-22 17:35:10.011] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:10.015] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004238"
[2025-07-22 17:35:13.073] [DBG] [] Splash screen closed
[2025-07-22 17:35:13.075] [DBG] [] Creating authentication window
[2025-07-22 17:35:13.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:13.252] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000031"
[2025-07-22 17:35:13.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:13.399] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000029"
[2025-07-22 17:35:14.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:14.304] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011963"
[2025-07-22 17:35:14.639] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:14.641] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003332"
[2025-07-22 17:35:14.863] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:14.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002650"
[2025-07-22 17:35:15.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002375"
[2025-07-22 17:35:15.567] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.569] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002765"
[2025-07-22 17:35:15.762] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.765] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006016"
[2025-07-22 17:35:15.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.958] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002160"
[2025-07-22 17:35:16.460] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:16.467] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004318"
[2025-07-22 17:35:16.593] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:16.600] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0044975"
[2025-07-22 17:35:16.930] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:16.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002680"
[2025-07-22 17:35:17.151] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002064"
[2025-07-22 17:35:17.263] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002147"
[2025-07-22 17:35:17.460] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.461] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001458"
[2025-07-22 17:35:17.645] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.650] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001710"
[2025-07-22 17:35:19.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:35:19.157] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002072"
[2025-07-22 17:35:19.327] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:19.330] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007552"
[2025-07-22 17:35:21.979] [INF] [] Administrator successfully authenticated
[2025-07-22 17:35:21.991] [DBG] [] Auth window closed with result: true
[2025-07-22 17:35:21.992] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:35:22.106] [INF] [] Main window initialized
[2025-07-22 17:35:22.185] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.188] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.199] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.202] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.205] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.208] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.211] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.215] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.218] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.221] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.223] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.225] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.279] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:35:22.281] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:35:22.282] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:35:22.284] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:35:22.286] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:35:22.355] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.361] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.363] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.365] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.367] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.369] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.370] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.377] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.379] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.381] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.383] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.385] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.391] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.397] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.403] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.406] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.413] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.416] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.418] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.427] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.430] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.438] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.440] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.442] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.444] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.446] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.447] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.454] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.461] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.464] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.466] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.469] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.471] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.472] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.474] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.476] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.477] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.479] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.481] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.483] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.484] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.489] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.619] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:35:22.627] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:35:22.629] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008784"
[2025-07-22 17:35:22.634] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:22.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017381"
[2025-07-22 17:35:22.654] [INF] [] Application framework initialization completed
[2025-07-22 17:35:22.660] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:35:22.662] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002603"
[2025-07-22 17:35:23.186] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:23.189] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001273"
[2025-07-22 17:35:23.241] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:23.246] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000954"
[2025-07-22 17:35:23.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:23.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001269"
[2025-07-22 17:35:23.296] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:23.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000843"
[2025-07-22 17:35:23.653] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:23.655] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000602"
[2025-07-22 17:35:23.703] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:35:23.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000659"
[2025-07-22 17:35:23.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:23.725] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000502"
[2025-07-22 17:35:23.944] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:23.947] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:23.954] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:23.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000652"
[2025-07-22 17:35:23.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:23.963] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000535"
[2025-07-22 17:35:24.182] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:24.184] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:24.186] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:24.189] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:24.191] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 17:35:24.194] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:24.196] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000391"
[2025-07-22 17:35:26.242] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:35:26.243] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001963"
[2025-07-22 17:35:29.128] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:29.130] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002678"
[2025-07-22 17:35:29.264] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:29.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000925"
[2025-07-22 17:35:29.301] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:29.302] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000455"
[2025-07-22 17:35:30.109] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:30.110] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000099"
[2025-07-22 17:35:30.113] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:30.115] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000374"
[2025-07-22 17:35:30.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:30.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 17:35:30.959] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:35:30.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002827"
[2025-07-22 17:35:30.962] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:30.963] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000057"
[2025-07-22 17:35:32.556] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:32.557] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000332"
[2025-07-22 17:35:32.701] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:32.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003502"
[2025-07-22 17:35:33.213] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.215] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.216] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.218] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.220] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:33.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 17:35:33.224] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:33.225] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000367"
[2025-07-22 17:35:33.951] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:33.952] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000023"
[2025-07-22 17:35:33.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:35:33.989] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001937"
[2025-07-22 17:35:34.012] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:35:34.013] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002326"
[2025-07-22 17:35:35.974] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:35.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002162"
[2025-07-22 17:35:36.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:36.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000598"
[2025-07-22 17:35:36.082] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:36.085] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000744"
[2025-07-22 17:35:36.138] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:36.140] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000776"
[2025-07-22 17:35:36.422] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:36.424] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000585"
[2025-07-22 17:35:36.968] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:36.970] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002545"
[2025-07-22 17:35:37.730] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:37.732] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000747"
[2025-07-22 17:35:37.781] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:37.783] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000618"
