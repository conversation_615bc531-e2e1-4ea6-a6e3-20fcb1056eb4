[2025-07-22 17:25:37.211] [INF] [] Application framework initialization starting
[2025-07-22 17:25:37.281] [DBG] [] Creating splash screen
[2025-07-22 17:25:37.502] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:25:37.509] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0020190"
[2025-07-22 17:25:37.627] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:25:37.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017268"
[2025-07-22 17:25:38.672] [DBG] [] Checking server availability
[2025-07-22 17:25:38.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:25:38.886] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0026683"
[2025-07-22 17:25:43.068] [ERR] [] Server not available
[2025-07-22 17:25:43.073] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:25:43.080] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0053146"
[2025-07-22 17:26:33.086] [INF] [] Application framework initialization starting
[2025-07-22 17:26:33.166] [DBG] [] Creating splash screen
[2025-07-22 17:26:33.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:33.395] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0015606"
[2025-07-22 17:26:33.501] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:33.504] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007117"
[2025-07-22 17:26:34.542] [DBG] [] Checking server availability
[2025-07-22 17:26:34.669] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:34.671] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006065"
[2025-07-22 17:26:36.890] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:36.894] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004065"
[2025-07-22 17:26:39.931] [DBG] [] Splash screen closed
[2025-07-22 17:26:39.933] [DBG] [] Creating authentication window
[2025-07-22 17:26:40.113] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:40.115] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000030"
[2025-07-22 17:26:40.278] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:40.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000028"
[2025-07-22 17:26:41.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:41.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008277"
[2025-07-22 17:26:42.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:42.041] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003855"
[2025-07-22 17:26:42.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.277] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003374"
[2025-07-22 17:26:42.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.420] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0022418"
[2025-07-22 17:26:42.702] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002058"
[2025-07-22 17:26:42.862] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:42.864] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002059"
[2025-07-22 17:26:42.979] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:42.980] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002848"
[2025-07-22 17:26:43.599] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:43.603] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008994"
[2025-07-22 17:26:43.959] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:43.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001907"
[2025-07-22 17:26:44.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:44.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002045"
[2025-07-22 17:26:44.350] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:44.352] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001860"
[2025-07-22 17:26:44.543] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:44.545] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001573"
[2025-07-22 17:26:44.714] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:26:44.747] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0049446"
[2025-07-22 17:26:45.003] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:45.005] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001862"
[2025-07-22 17:26:45.253] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001788"
[2025-07-22 17:26:45.258] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.259] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001299"
[2025-07-22 17:26:45.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.400] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001505"
[2025-07-22 17:26:45.687] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.689] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001507"
[2025-07-22 17:26:45.813] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.815] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001633"
[2025-07-22 17:26:45.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:45.983] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001328"
[2025-07-22 17:26:46.127] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:46.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001761"
[2025-07-22 17:26:46.426] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:46.427] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001269"
[2025-07-22 17:26:46.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:46.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001800"
[2025-07-22 17:26:46.684] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:46.685] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001378"
[2025-07-22 17:26:47.073] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:47.077] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002546"
[2025-07-22 17:26:47.212] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:47.215] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002313"
[2025-07-22 17:26:47.362] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:26:47.364] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001794"
[2025-07-22 17:26:47.439] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:47.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001300"
[2025-07-22 17:26:47.680] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:47.682] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001392"
[2025-07-22 17:26:48.156] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:48.158] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002320"
[2025-07-22 17:26:48.388] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:48.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001243"
[2025-07-22 17:26:48.594] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:48.596] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001634"
[2025-07-22 17:26:49.752] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:26:49.754] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001034"
[2025-07-22 17:26:49.927] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:49.930] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009993"
[2025-07-22 17:26:52.641] [INF] [] Administrator successfully authenticated
[2025-07-22 17:26:52.657] [DBG] [] Auth window closed with result: true
[2025-07-22 17:26:52.659] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:26:52.739] [INF] [] Main window initialized
[2025-07-22 17:26:52.787] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.790] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.797] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.799] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.801] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.803] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.806] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.808] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.810] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.812] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.815] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:26:52.817] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:52.867] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:26:52.869] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:26:52.871] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:26:52.872] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:26:52.875] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:26:52.947] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.952] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.955] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.956] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.958] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.960] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.962] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.964] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.966] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.967] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.969] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.970] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.972] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.974] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.975] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.977] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.979] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.980] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.982] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.983] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.985] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.986] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.988] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.989] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.991] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.992] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.994] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.995] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:52.997] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:52.998] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.000] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.002] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.003] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.004] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.006] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.008] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.009] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.011] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.013] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.014] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:26:53.016] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.017] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:26:53.109] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:26:53.120] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:26:53.123] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0012321"
[2025-07-22 17:26:53.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:53.133] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017350"
[2025-07-22 17:26:53.150] [INF] [] Application framework initialization completed
[2025-07-22 17:26:53.159] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:26:53.161] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002090"
[2025-07-22 17:26:53.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:53.832] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000439"
[2025-07-22 17:26:53.834] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:26:53.835] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000419"
[2025-07-22 17:26:53.917] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:53.919] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000420"
[2025-07-22 17:26:54.935] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.937] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0010776"
[2025-07-22 17:26:54.954] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004030"
[2025-07-22 17:26:54.968] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.970] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007411"
[2025-07-22 17:26:54.988] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:54.990] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006588"
[2025-07-22 17:26:55.003] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.005] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008246"
[2025-07-22 17:26:55.022] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003971"
[2025-07-22 17:26:55.037] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003886"
[2025-07-22 17:26:55.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.062] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006596"
[2025-07-22 17:26:55.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:55.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005243"
[2025-07-22 17:26:56.585] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.588] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.591] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.622] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:56.624] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000273"
[2025-07-22 17:26:56.630] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:26:56.632] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000539"
[2025-07-22 17:26:56.835] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.837] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.840] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.843] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:56.847] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:26:56.849] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:26:56.852] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:56.854] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000600"
[2025-07-22 17:26:57.264] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.267] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.272] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.275] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:26:57.279] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 7 To arrange: 0
[2025-07-22 17:26:57.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009912"
[2025-07-22 17:26:57.286] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:26:57.288] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000368"
[2025-07-22 17:26:58.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:26:58.132] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000049"
[2025-07-22 17:26:58.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 8 To arrange: 0
[2025-07-22 17:26:58.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001788"
[2025-07-22 17:26:58.175] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 2
[2025-07-22 17:26:58.177] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002625"
[2025-07-22 17:27:01.170] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:01.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0487054"
[2025-07-22 17:27:02.137] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
[2025-07-22 17:27:02.140] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001683"
[2025-07-22 17:27:02.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:27:02.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000239"
[2025-07-22 17:27:02.761] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:02.764] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0010458"
[2025-07-22 17:27:03.240] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:03.242] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006902"
[2025-07-22 17:27:04.051] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:04.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002704"
[2025-07-22 17:27:04.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:04.707] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011686"
[2025-07-22 17:27:05.146] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:05.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003492"
[2025-07-22 17:27:05.513] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:05.514] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002543"
[2025-07-22 17:27:06.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:06.025] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002316"
[2025-07-22 17:27:06.475] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:06.476] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003539"
[2025-07-22 17:27:06.883] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:06.885] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002367"
[2025-07-22 17:27:07.310] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:07.312] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003937"
[2025-07-22 17:27:07.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:07.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005685"
[2025-07-22 17:27:07.993] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:07.995] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003095"
[2025-07-22 17:27:08.413] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:08.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003238"
[2025-07-22 17:27:08.773] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:08.774] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002997"
[2025-07-22 17:27:09.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:09.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003339"
[2025-07-22 17:27:09.665] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:09.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002327"
[2025-07-22 17:27:10.511] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:10.513] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006146"
[2025-07-22 17:27:10.772] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:10.773] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002323"
[2025-07-22 17:27:11.095] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:27:11.096] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002520"
[2025-07-22 17:27:12.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:12.633] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001870"
[2025-07-22 17:27:13.727] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:13.730] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004243"
[2025-07-22 17:27:26.112] [INF] [] Application framework initialization starting
[2025-07-22 17:27:26.183] [DBG] [] Creating splash screen
[2025-07-22 17:27:26.377] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:26.384] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0022666"
[2025-07-22 17:27:26.486] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:26.488] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007389"
[2025-07-22 17:27:27.526] [DBG] [] Checking server availability
[2025-07-22 17:27:27.622] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:27.624] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005929"
[2025-07-22 17:27:29.818] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:29.819] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004064"
[2025-07-22 17:27:32.854] [DBG] [] Splash screen closed
[2025-07-22 17:27:32.856] [DBG] [] Creating authentication window
[2025-07-22 17:27:33.030] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:33.031] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000046"
[2025-07-22 17:27:33.179] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:33.180] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000032"
[2025-07-22 17:27:34.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:27:34.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007818"
[2025-07-22 17:27:34.657] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:34.658] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003637"
[2025-07-22 17:27:34.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:34.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004811"
[2025-07-22 17:27:35.518] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:35.519] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002892"
[2025-07-22 17:27:35.822] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:35.824] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002823"
[2025-07-22 17:27:36.077] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:36.079] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003331"
[2025-07-22 17:27:36.345] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:36.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0044481"
[2025-07-22 17:27:36.666] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:36.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008795"
[2025-07-22 17:27:36.903] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:36.904] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001801"
[2025-07-22 17:27:37.020] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:37.022] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003457"
[2025-07-22 17:27:37.216] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:37.218] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002601"
[2025-07-22 17:27:37.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:37.399] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001837"
[2025-07-22 17:27:38.279] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:27:38.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001527"
[2025-07-22 17:27:38.416] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:27:38.418] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007414"
[2025-07-22 17:27:41.031] [INF] [] Administrator successfully authenticated
[2025-07-22 17:27:41.051] [DBG] [] Auth window closed with result: true
[2025-07-22 17:27:41.052] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:27:41.128] [INF] [] Main window initialized
[2025-07-22 17:27:41.171] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.173] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.179] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.181] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.182] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.184] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.186] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.188] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.189] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.190] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.192] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:27:41.194] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:41.233] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:27:41.235] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:27:41.236] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:27:41.237] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:27:41.239] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:27:41.293] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.296] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.298] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.299] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.300] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.302] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.303] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.305] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.306] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.307] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.308] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.310] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.312] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.313] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.314] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.315] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.317] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.318] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.320] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.321] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.322] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.324] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.325] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.327] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.328] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.329] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.330] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.332] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.333] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.334] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.336] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.337] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.338] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.340] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.341] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.342] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.343] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.345] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.346] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.347] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:27:41.348] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.350] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:27:41.433] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:27:41.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:27:41.443] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011649"
[2025-07-22 17:27:41.447] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:27:41.450] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017698"
[2025-07-22 17:27:41.465] [INF] [] Application framework initialization completed
[2025-07-22 17:27:41.471] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:27:41.473] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002080"
[2025-07-22 17:27:42.679] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:42.681] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:27:42.688] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:27:42.689] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000440"
[2025-07-22 17:27:42.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:27:42.695] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000530"
[2025-07-22 17:27:43.530] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:43.532] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000064"
[2025-07-22 17:27:43.598] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:27:43.602] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0023476"
[2025-07-22 17:27:43.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:27:43.633] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003442"
[2025-07-22 17:27:44.985] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:44.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002147"
[2025-07-22 17:27:45.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:27:45.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004450"
[2025-07-22 17:29:02.753] [INF] [] Application framework initialization starting
[2025-07-22 17:29:02.853] [DBG] [] Creating splash screen
[2025-07-22 17:29:03.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:03.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0016636"
[2025-07-22 17:29:03.162] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:03.164] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008061"
[2025-07-22 17:29:04.192] [DBG] [] Checking server availability
[2025-07-22 17:29:04.283] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:04.287] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007442"
[2025-07-22 17:29:06.463] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:06.465] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004018"
[2025-07-22 17:29:09.504] [DBG] [] Splash screen closed
[2025-07-22 17:29:09.506] [DBG] [] Creating authentication window
[2025-07-22 17:29:09.588] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:09.589] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000034"
[2025-07-22 17:29:09.739] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:09.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000031"
[2025-07-22 17:29:10.652] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:10.655] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008794"
[2025-07-22 17:29:10.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:10.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003927"
[2025-07-22 17:29:11.175] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:11.177] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002780"
[2025-07-22 17:29:11.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:11.882] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002305"
[2025-07-22 17:29:12.066] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:12.068] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002153"
[2025-07-22 17:29:12.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:12.252] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004073"
[2025-07-22 17:29:12.643] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:29:12.649] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0043675"
[2025-07-22 17:29:12.878] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:12.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003049"
[2025-07-22 17:29:13.095] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.097] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002059"
[2025-07-22 17:29:13.205] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.206] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002218"
[2025-07-22 17:29:13.388] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002619"
[2025-07-22 17:29:13.579] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.581] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001890"
[2025-07-22 17:29:13.583] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:13.586] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005280"
[2025-07-22 17:29:14.109] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.111] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001497"
[2025-07-22 17:29:14.303] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.305] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001448"
[2025-07-22 17:29:14.478] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.480] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002464"
[2025-07-22 17:29:14.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.639] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002342"
[2025-07-22 17:29:14.795] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:14.797] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001538"
[2025-07-22 17:29:14.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:14.936] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002010"
[2025-07-22 17:29:15.053] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:15.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002212"
[2025-07-22 17:29:15.257] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.262] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002531"
[2025-07-22 17:29:15.433] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.435] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001315"
[2025-07-22 17:29:15.628] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001314"
[2025-07-22 17:29:15.828] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:15.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001684"
[2025-07-22 17:29:17.202] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:29:17.204] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001420"
[2025-07-22 17:29:17.392] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:17.395] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006628"
[2025-07-22 17:29:19.924] [INF] [] Administrator successfully authenticated
[2025-07-22 17:29:19.943] [DBG] [] Auth window closed with result: true
[2025-07-22 17:29:19.945] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:29:20.021] [INF] [] Main window initialized
[2025-07-22 17:29:20.063] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.066] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.073] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.075] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.077] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.079] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.082] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.084] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.086] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.088] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.090] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:29:20.092] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:20.131] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:29:20.133] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:29:20.135] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:29:20.136] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:29:20.139] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:29:20.197] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.201] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.202] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.204] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.206] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.207] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.209] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.210] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.212] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.213] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.215] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.216] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.218] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.219] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.221] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.222] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.224] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.226] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.227] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.229] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.231] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.233] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.235] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.237] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.238] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.240] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.241] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.243] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.244] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.246] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.247] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.249] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.250] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.252] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.253] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.255] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.256] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.258] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.259] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.261] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:29:20.262] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.264] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:29:20.345] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:29:20.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:29:20.356] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0010943"
[2025-07-22 17:29:20.361] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:20.364] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0021804"
[2025-07-22 17:29:20.381] [INF] [] Application framework initialization completed
[2025-07-22 17:29:20.391] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:29:20.393] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002672"
[2025-07-22 17:29:21.206] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:21.207] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000409"
[2025-07-22 17:29:21.256] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:29:21.258] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000948"
[2025-07-22 17:29:21.273] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:21.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000789"
[2025-07-22 17:29:21.559] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.562] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.564] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.572] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:21.573] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000215"
[2025-07-22 17:29:21.578] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:29:21.580] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000529"
[2025-07-22 17:29:21.966] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.968] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.970] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.973] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:21.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:21.977] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 17:29:21.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:21.983] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000526"
[2025-07-22 17:29:22.157] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.161] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.166] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.189] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.193] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 7 To arrange: 0
[2025-07-22 17:29:22.197] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0018282"
[2025-07-22 17:29:22.201] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:22.203] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000673"
[2025-07-22 17:29:22.425] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.428] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.431] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.434] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.437] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.439] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.442] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:29:22.446] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:29:22.447] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000185"
[2025-07-22 17:29:22.451] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:22.453] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000674"
[2025-07-22 17:29:23.186] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:29:23.188] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000035"
[2025-07-22 17:29:23.211] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 10 To arrange: 0
[2025-07-22 17:29:23.213] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006481"
[2025-07-22 17:29:23.238] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:29:23.240] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002962"
[2025-07-22 17:29:25.465] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:29:25.490] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0229402"
[2025-07-22 17:29:26.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:29:26.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001194"
[2025-07-22 17:29:26.355] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:29:26.357] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000415"
[2025-07-22 17:29:27.471] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:29:27.473] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002421"
[2025-07-22 17:29:28.890] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:29:28.892] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002497"
[2025-07-22 17:29:30.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:29:30.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002805"
[2025-07-22 17:33:25.711] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 32
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:33:51.545] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 32
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:34:14.483] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 30
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:35:06.328] [INF] [] Application framework initialization starting
[2025-07-22 17:35:06.436] [DBG] [] Creating splash screen
[2025-07-22 17:35:06.609] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:06.613] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0016820"
[2025-07-22 17:35:06.693] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:06.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005816"
[2025-07-22 17:35:07.720] [DBG] [] Checking server availability
[2025-07-22 17:35:07.808] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:07.810] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006729"
[2025-07-22 17:35:10.011] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:10.015] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004238"
[2025-07-22 17:35:13.073] [DBG] [] Splash screen closed
[2025-07-22 17:35:13.075] [DBG] [] Creating authentication window
[2025-07-22 17:35:13.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:13.252] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000031"
[2025-07-22 17:35:13.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:13.399] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000029"
[2025-07-22 17:35:14.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:14.304] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011963"
[2025-07-22 17:35:14.639] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:14.641] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003332"
[2025-07-22 17:35:14.863] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:14.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002650"
[2025-07-22 17:35:15.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002375"
[2025-07-22 17:35:15.567] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.569] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002765"
[2025-07-22 17:35:15.762] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.765] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006016"
[2025-07-22 17:35:15.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:15.958] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002160"
[2025-07-22 17:35:16.460] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:16.467] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004318"
[2025-07-22 17:35:16.593] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:16.600] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0044975"
[2025-07-22 17:35:16.930] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:16.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002680"
[2025-07-22 17:35:17.151] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002064"
[2025-07-22 17:35:17.263] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002147"
[2025-07-22 17:35:17.460] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.461] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001458"
[2025-07-22 17:35:17.645] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:17.650] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001710"
[2025-07-22 17:35:19.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:35:19.157] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002072"
[2025-07-22 17:35:19.327] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:19.330] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007552"
[2025-07-22 17:35:21.979] [INF] [] Administrator successfully authenticated
[2025-07-22 17:35:21.991] [DBG] [] Auth window closed with result: true
[2025-07-22 17:35:21.992] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:35:22.106] [INF] [] Main window initialized
[2025-07-22 17:35:22.185] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.188] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.199] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.202] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.205] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.208] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.211] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.215] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.218] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.221] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.223] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:35:22.225] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:22.279] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:35:22.281] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:35:22.282] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:35:22.284] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:35:22.286] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:35:22.355] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.361] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.363] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.365] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.367] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.369] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.370] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.377] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.379] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.381] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.383] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.385] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.391] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.397] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.403] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.406] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.413] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.416] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.418] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.427] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.430] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.438] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.440] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.442] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.444] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.446] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.447] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.454] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.461] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.464] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.466] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.469] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.471] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.472] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.474] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.476] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.477] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.479] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.481] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.483] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:35:22.484] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.489] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:35:22.619] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:35:22.627] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:35:22.629] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008784"
[2025-07-22 17:35:22.634] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:22.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017381"
[2025-07-22 17:35:22.654] [INF] [] Application framework initialization completed
[2025-07-22 17:35:22.660] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:35:22.662] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002603"
[2025-07-22 17:35:23.186] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:23.189] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001273"
[2025-07-22 17:35:23.241] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:23.246] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000954"
[2025-07-22 17:35:23.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:23.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001269"
[2025-07-22 17:35:23.296] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:23.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000843"
[2025-07-22 17:35:23.653] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:23.655] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000602"
[2025-07-22 17:35:23.703] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:35:23.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000659"
[2025-07-22 17:35:23.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:23.725] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000502"
[2025-07-22 17:35:23.944] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:23.947] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:23.954] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:35:23.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000652"
[2025-07-22 17:35:23.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:23.963] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000535"
[2025-07-22 17:35:24.182] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:24.184] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:24.186] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:24.189] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:24.191] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 17:35:24.194] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:24.196] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000391"
[2025-07-22 17:35:26.242] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:35:26.243] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001963"
[2025-07-22 17:35:29.128] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:29.130] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002678"
[2025-07-22 17:35:29.264] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:29.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000925"
[2025-07-22 17:35:29.301] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:29.302] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000455"
[2025-07-22 17:35:30.109] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:30.110] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000099"
[2025-07-22 17:35:30.113] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:30.115] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000374"
[2025-07-22 17:35:30.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:30.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 17:35:30.959] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:35:30.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002827"
[2025-07-22 17:35:30.962] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:30.963] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000057"
[2025-07-22 17:35:32.556] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:35:32.557] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000332"
[2025-07-22 17:35:32.701] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:32.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003502"
[2025-07-22 17:35:33.213] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.215] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.216] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.218] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:35:33.220] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:33.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 17:35:33.224] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:33.225] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000367"
[2025-07-22 17:35:33.951] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:35:33.952] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000023"
[2025-07-22 17:35:33.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:35:33.989] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001937"
[2025-07-22 17:35:34.012] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:35:34.013] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002326"
[2025-07-22 17:35:35.974] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:35.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002162"
[2025-07-22 17:35:36.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:36.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000598"
[2025-07-22 17:35:36.082] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:36.085] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000744"
[2025-07-22 17:35:36.138] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:36.140] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000776"
[2025-07-22 17:35:36.422] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:36.424] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000585"
[2025-07-22 17:35:36.968] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:35:36.970] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002545"
[2025-07-22 17:35:37.730] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:35:37.732] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000747"
[2025-07-22 17:35:37.781] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:35:37.783] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000618"
[2025-07-22 17:36:22.482] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:36:22.489] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002745"
[2025-07-22 17:36:22.722] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:36:22.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000681"
[2025-07-22 17:36:22.763] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:36:22.765] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000572"
[2025-07-22 17:36:22.848] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:22.850] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000443"
[2025-07-22 17:36:23.021] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:36:23.025] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000650"
[2025-07-22 17:36:23.124] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:23.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000670"
[2025-07-22 17:36:23.131] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:36:23.136] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000643"
[2025-07-22 17:36:23.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:23.145] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000549"
[2025-07-22 17:36:23.696] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:23.703] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000372"
[2025-07-22 17:36:23.707] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:36:23.709] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000405"
[2025-07-22 17:36:24.033] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:24.037] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000505"
[2025-07-22 17:36:24.041] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:36:24.043] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000564"
[2025-07-22 17:36:24.947] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:24.950] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000191"
[2025-07-22 17:36:24.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:36:24.957] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000529"
[2025-07-22 17:36:25.721] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:36:25.723] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000019"
[2025-07-22 17:36:25.729] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:36:25.730] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000019"
[2025-07-22 17:36:25.746] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:36:25.748] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002391"
[2025-07-22 17:36:28.006] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:36:28.008] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002737"
[2025-07-22 17:36:28.142] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:36:28.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000434"
[2025-07-22 17:36:28.144] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:36:28.146] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000309"
[2025-07-22 17:36:28.147] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:36:28.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000282"
[2025-07-22 17:36:28.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:36:28.151] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000273"
[2025-07-22 17:36:28.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:36:28.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000312"
[2025-07-22 17:36:28.396] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:36:28.397] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000410"
[2025-07-22 17:36:28.413] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 1
[2025-07-22 17:36:28.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000566"
[2025-07-22 17:36:28.447] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:36:28.448] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000426"
[2025-07-22 17:36:28.988] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:36:28.992] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003084"
[2025-07-22 17:39:26.442] [INF] [] Application framework initialization starting
[2025-07-22 17:39:26.449] [INF] [] Forced light theme variant
[2025-07-22 17:39:26.507] [DBG] [] Creating splash screen
[2025-07-22 17:39:26.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:39:26.682] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017746"
[2025-07-22 17:39:26.769] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:26.775] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0018880"
[2025-07-22 17:39:27.821] [DBG] [] Checking server availability
[2025-07-22 17:39:27.884] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:27.886] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004992"
[2025-07-22 17:39:30.160] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:30.166] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005823"
[2025-07-22 17:39:33.206] [DBG] [] Splash screen closed
[2025-07-22 17:39:33.208] [DBG] [] Creating authentication window
[2025-07-22 17:39:33.392] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:39:33.393] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000033"
[2025-07-22 17:39:33.533] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:39:33.535] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000039"
[2025-07-22 17:39:35.427] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:39:35.430] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009482"
[2025-07-22 17:39:35.815] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:35.820] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005031"
[2025-07-22 17:39:36.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:36.062] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002735"
[2025-07-22 17:39:36.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:36.708] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006451"
[2025-07-22 17:39:36.929] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:36.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002106"
[2025-07-22 17:39:37.119] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:37.120] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002383"
[2025-07-22 17:39:37.297] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:39:37.308] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0056529"
[2025-07-22 17:39:37.559] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:37.561] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003310"
[2025-07-22 17:39:37.785] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:37.787] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002871"
[2025-07-22 17:39:37.904] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:37.906] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002045"
[2025-07-22 17:39:38.100] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:38.102] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002863"
[2025-07-22 17:39:38.310] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:38.312] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002330"
[2025-07-22 17:39:39.607] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:39:39.611] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002557"
[2025-07-22 17:39:39.902] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:39:39.910] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0013295"
[2025-07-22 17:39:42.656] [INF] [] Administrator successfully authenticated
[2025-07-22 17:39:42.678] [DBG] [] Auth window closed with result: true
[2025-07-22 17:39:42.680] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:39:42.744] [INF] [] Main window initialized
[2025-07-22 17:39:42.780] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:39:42.783] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:42.791] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:39:42.794] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:42.796] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:39:42.799] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:42.802] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:39:42.804] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:42.807] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:39:42.809] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:42.811] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:39:42.813] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:42.862] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:39:42.864] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:39:42.866] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:39:42.868] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:39:42.871] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:39:42.937] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.940] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.941] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.943] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.944] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.946] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.947] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.948] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.949] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.951] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.952] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.954] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.955] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.956] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.958] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.959] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.960] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.962] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.963] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.965] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.966] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.968] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.969] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.970] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.972] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.974] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.975] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.976] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.978] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.979] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.980] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.982] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.983] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.984] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.986] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.987] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.988] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.990] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.991] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.992] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:39:42.993] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:42.995] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:39:43.097] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:39:43.117] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:39:43.119] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009810"
[2025-07-22 17:39:43.130] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:43.136] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0031501"
[2025-07-22 17:39:43.161] [INF] [] Application framework initialization completed
[2025-07-22 17:39:43.167] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:39:43.170] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003424"
[2025-07-22 17:39:43.811] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:39:43.813] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000585"
[2025-07-22 17:39:43.840] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:39:43.841] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000392"
[2025-07-22 17:39:46.525] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:46.528] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:46.534] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:46.536] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000643"
[2025-07-22 17:39:46.541] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:39:46.543] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000686"
[2025-07-22 17:39:47.406] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:39:47.408] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000051"
[2025-07-22 17:39:47.412] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:39:47.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000034"
[2025-07-22 17:39:47.444] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:39:47.446] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003119"
[2025-07-22 17:39:48.692] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:39:48.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002368"
[2025-07-22 17:39:49.114] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:39:49.121] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001053"
[2025-07-22 17:39:49.190] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:39:49.192] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000658"
[2025-07-22 17:39:49.362] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:49.365] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:49.369] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:49.372] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:49.374] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 17:39:49.377] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:39:49.379] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000415"
[2025-07-22 17:39:50.443] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:50.449] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000469"
[2025-07-22 17:39:50.490] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:39:50.493] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003187"
[2025-07-22 17:39:50.495] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:50.496] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000067"
[2025-07-22 17:39:51.407] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:51.409] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000841"
[2025-07-22 17:39:51.410] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:39:51.411] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000278"
[2025-07-22 17:39:51.554] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:39:51.559] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006754"
[2025-07-22 17:39:52.329] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:52.332] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:52.334] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:52.336] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:39:52.339] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:52.340] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 17:39:52.344] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:39:52.346] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000538"
[2025-07-22 17:39:53.266] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:39:53.267] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000025"
[2025-07-22 17:39:53.298] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:39:53.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001862"
[2025-07-22 17:39:53.317] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:39:53.319] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002657"
[2025-07-22 17:39:56.591] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:56.594] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002497"
[2025-07-22 17:39:56.754] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 74 To arrange: 0
[2025-07-22 17:39:56.755] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000885"
[2025-07-22 17:39:56.760] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:39:56.761] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001195"
[2025-07-22 17:39:56.773] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.775] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000177"
[2025-07-22 17:39:56.776] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.778] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000208"
[2025-07-22 17:39:56.779] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.780] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:39:56.782] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.783] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000202"
[2025-07-22 17:39:56.784] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.786] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000133"
[2025-07-22 17:39:56.787] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.788] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000116"
[2025-07-22 17:39:56.790] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.791] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000115"
[2025-07-22 17:39:56.820] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.821] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000190"
[2025-07-22 17:39:56.823] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.824] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000179"
[2025-07-22 17:39:56.826] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.827] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000173"
[2025-07-22 17:39:56.828] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.830] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000164"
[2025-07-22 17:39:56.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.832] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000149"
[2025-07-22 17:39:56.833] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.835] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000172"
[2025-07-22 17:39:56.836] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:39:56.838] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 17:39:56.839] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:56.842] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0015966"
[2025-07-22 17:39:57.160] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:57.162] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000294"
[2025-07-22 17:39:57.176] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:57.178] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003938"
[2025-07-22 17:39:57.374] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:57.380] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000208"
[2025-07-22 17:39:57.385] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:57.394] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000291"
[2025-07-22 17:39:57.527] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:57.528] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 17:39:57.563] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:57.565] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005083"
[2025-07-22 17:39:57.605] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:57.608] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004974"
[2025-07-22 17:39:57.610] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.612] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 17:39:57.613] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.615] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000138"
[2025-07-22 17:39:57.616] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.617] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 17:39:57.653] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.654] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000189"
[2025-07-22 17:39:57.656] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.658] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000189"
[2025-07-22 17:39:57.659] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.661] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000164"
[2025-07-22 17:39:57.662] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.664] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000133"
[2025-07-22 17:39:57.665] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.666] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000129"
[2025-07-22 17:39:57.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.669] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000174"
[2025-07-22 17:39:57.671] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.672] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000186"
[2025-07-22 17:39:57.674] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.675] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000126"
[2025-07-22 17:39:57.676] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 17:39:57.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.680] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 17:39:57.682] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.683] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000126"
[2025-07-22 17:39:57.685] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.686] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 17:39:57.688] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:57.690] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005329"
[2025-07-22 17:39:57.692] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.693] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 17:39:57.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.695] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 17:39:57.697] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.698] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000078"
[2025-07-22 17:39:57.699] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.701] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 17:39:57.702] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.703] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000167"
[2025-07-22 17:39:57.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.706] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 17:39:57.707] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.708] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000155"
[2025-07-22 17:39:57.710] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.711] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 17:39:57.712] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.714] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000097"
[2025-07-22 17:39:57.715] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.717] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 17:39:57.718] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.719] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000149"
[2025-07-22 17:39:57.721] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.722] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:39:57.723] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.725] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 17:39:57.726] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.727] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000090"
[2025-07-22 17:39:57.728] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.761] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:39:57.849] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:57.850] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002385"
[2025-07-22 17:39:57.852] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.853] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 17:39:57.854] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.856] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000092"
[2025-07-22 17:39:57.857] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.858] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000089"
[2025-07-22 17:39:57.859] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.860] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000086"
[2025-07-22 17:39:57.862] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.863] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 17:39:57.864] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000090"
[2025-07-22 17:39:57.867] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.868] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000129"
[2025-07-22 17:39:57.869] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.874] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000201"
[2025-07-22 17:39:57.875] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 17:39:57.878] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 17:39:57.881] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.885] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000170"
[2025-07-22 17:39:57.886] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.887] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 17:39:57.889] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.890] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 17:39:57.891] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.893] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000103"
[2025-07-22 17:39:57.894] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.896] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000131"
[2025-07-22 17:39:57.897] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:57.899] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003991"
[2025-07-22 17:39:57.901] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.902] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000135"
[2025-07-22 17:39:57.903] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.904] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 17:39:57.906] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.907] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000121"
[2025-07-22 17:39:57.909] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.910] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 17:39:57.911] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.912] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000109"
[2025-07-22 17:39:57.913] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.915] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000110"
[2025-07-22 17:39:57.916] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.917] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 17:39:57.919] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.920] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000101"
[2025-07-22 17:39:57.921] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.922] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 17:39:57.923] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.946] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000166"
[2025-07-22 17:39:57.948] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.949] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 17:39:57.950] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.951] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000075"
[2025-07-22 17:39:57.952] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.954] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000076"
[2025-07-22 17:39:57.955] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000074"
[2025-07-22 17:39:57.957] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:57.958] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000082"
[2025-07-22 17:39:58.138] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.140] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005248"
[2025-07-22 17:39:58.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.142] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000105"
[2025-07-22 17:39:58.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.145] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000076"
[2025-07-22 17:39:58.146] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.147] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000067"
[2025-07-22 17:39:58.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000277"
[2025-07-22 17:39:58.179] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.210] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 17:39:58.211] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.213] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000110"
[2025-07-22 17:39:58.214] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.215] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000097"
[2025-07-22 17:39:58.217] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.218] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 17:39:58.219] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000151"
[2025-07-22 17:39:58.222] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.224] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000093"
[2025-07-22 17:39:58.225] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.227] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 17:39:58.228] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.229] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000109"
[2025-07-22 17:39:58.256] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.257] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000175"
[2025-07-22 17:39:58.259] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.260] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 17:39:58.262] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.263] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000097"
[2025-07-22 17:39:58.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.267] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004387"
[2025-07-22 17:39:58.268] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.269] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000089"
[2025-07-22 17:39:58.271] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.273] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 17:39:58.274] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000099"
[2025-07-22 17:39:58.277] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.278] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000088"
[2025-07-22 17:39:58.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.281] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000094"
[2025-07-22 17:39:58.282] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.283] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000082"
[2025-07-22 17:39:58.285] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.286] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 17:39:58.288] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.291] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000230"
[2025-07-22 17:39:58.293] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.294] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 17:39:58.296] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.297] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000130"
[2025-07-22 17:39:58.299] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 17:39:58.302] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.303] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000250"
[2025-07-22 17:39:58.305] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.307] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000181"
[2025-07-22 17:39:58.308] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.309] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000152"
[2025-07-22 17:39:58.311] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.312] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 17:39:58.479] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.481] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002488"
[2025-07-22 17:39:58.482] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.483] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000090"
[2025-07-22 17:39:58.484] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.486] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 17:39:58.524] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.528] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000234"
[2025-07-22 17:39:58.541] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.552] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000222"
[2025-07-22 17:39:58.555] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.556] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000170"
[2025-07-22 17:39:58.563] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.565] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000168"
[2025-07-22 17:39:58.567] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.576] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000220"
[2025-07-22 17:39:58.582] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.589] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000185"
[2025-07-22 17:39:58.593] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.600] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000215"
[2025-07-22 17:39:58.603] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.605] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000161"
[2025-07-22 17:39:58.633] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.635] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 17:39:58.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.638] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 17:39:58.639] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.641] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000081"
[2025-07-22 17:39:58.664] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.666] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 17:39:58.667] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000140"
[2025-07-22 17:39:58.670] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.672] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002824"
[2025-07-22 17:39:58.673] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.674] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000103"
[2025-07-22 17:39:58.675] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 17:39:58.678] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.680] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000159"
[2025-07-22 17:39:58.708] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.709] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 17:39:58.710] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.712] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000083"
[2025-07-22 17:39:58.713] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.714] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000077"
[2025-07-22 17:39:58.715] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.716] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000061"
[2025-07-22 17:39:58.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.746] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000182"
[2025-07-22 17:39:58.748] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.749] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 17:39:58.751] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.752] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000110"
[2025-07-22 17:39:58.753] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.755] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000094"
[2025-07-22 17:39:58.756] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.757] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000123"
[2025-07-22 17:39:58.785] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.786] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000143"
[2025-07-22 17:39:58.788] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.789] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 17:39:58.791] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.792] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 17:39:58.801] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.803] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002654"
[2025-07-22 17:39:58.804] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.806] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 17:39:58.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.832] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000168"
[2025-07-22 17:39:58.833] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.834] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 17:39:58.836] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.837] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 17:39:58.838] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.839] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 17:39:58.840] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.842] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 17:39:58.843] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.844] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000092"
[2025-07-22 17:39:58.846] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.847] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 17:39:58.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000182"
[2025-07-22 17:39:58.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.881] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 17:39:58.883] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.884] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000098"
[2025-07-22 17:39:58.885] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.886] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000088"
[2025-07-22 17:39:58.888] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.889] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000146"
[2025-07-22 17:39:58.891] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.892] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 17:39:58.894] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.895] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000119"
[2025-07-22 17:39:58.897] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.899] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002364"
[2025-07-22 17:39:58.900] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.901] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 17:39:58.902] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.924] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000139"
[2025-07-22 17:39:58.925] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.927] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 17:39:58.928] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.930] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000087"
[2025-07-22 17:39:58.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.933] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000081"
[2025-07-22 17:39:58.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.935] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000081"
[2025-07-22 17:39:58.936] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.938] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000071"
[2025-07-22 17:39:58.939] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.941] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000121"
[2025-07-22 17:39:58.942] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.944] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 17:39:58.945] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.946] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000100"
[2025-07-22 17:39:58.948] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.949] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000089"
[2025-07-22 17:39:58.950] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.951] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000091"
[2025-07-22 17:39:58.953] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.985] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000161"
[2025-07-22 17:39:58.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.988] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 17:39:58.989] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:58.991] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000086"
[2025-07-22 17:39:58.994] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:39:58.996] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004316"
[2025-07-22 17:39:59.000] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.002] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 17:39:59.003] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.004] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000088"
[2025-07-22 17:39:59.005] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.007] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000140"
[2025-07-22 17:39:59.008] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.009] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 17:39:59.011] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.012] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 17:39:59.013] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.014] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 17:39:59.015] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.017] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000135"
[2025-07-22 17:39:59.018] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.019] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 17:39:59.021] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.048] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000200"
[2025-07-22 17:39:59.049] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.050] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000121"
[2025-07-22 17:39:59.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.053] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 17:39:59.054] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.055] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 17:39:59.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.058] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 17:39:59.059] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 17:39:59.062] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:39:59.063] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000123"
[2025-07-22 17:39:59.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:59.256] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000149"
[2025-07-22 17:39:59.258] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:59.260] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 17:39:59.339] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:59.344] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000201"
[2025-07-22 17:39:59.374] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:39:59.376] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000208"
[2025-07-22 17:39:59.381] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:59.383] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000132"
[2025-07-22 17:39:59.619] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:39:59.621] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000177"
[2025-07-22 17:39:59.772] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:39:59.774] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001028"
[2025-07-22 17:40:00.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
[2025-07-22 17:47:59.865] [INF] [] Application framework initialization starting
[2025-07-22 17:47:59.872] [INF] [] Forced light theme variant
[2025-07-22 17:47:59.977] [DBG] [] Creating splash screen
[2025-07-22 17:48:00.225] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:48:00.231] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0020281"
[2025-07-22 17:48:00.324] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:00.327] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007622"
[2025-07-22 17:48:01.359] [DBG] [] Checking server availability
[2025-07-22 17:48:01.521] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:01.523] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005284"
[2025-07-22 17:48:03.736] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:03.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005272"
[2025-07-22 17:48:06.788] [DBG] [] Splash screen closed
[2025-07-22 17:48:06.789] [DBG] [] Creating authentication window
[2025-07-22 17:48:06.992] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:48:06.994] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000032"
[2025-07-22 17:48:07.135] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:48:07.137] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000035"
[2025-07-22 17:48:08.101] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:48:08.104] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008895"
[2025-07-22 17:48:09.586] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:09.588] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002656"
[2025-07-22 17:48:09.850] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:09.853] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004068"
[2025-07-22 17:48:10.294] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:10.296] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002824"
[2025-07-22 17:48:10.501] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:10.502] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002117"
[2025-07-22 17:48:10.706] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:10.713] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004792"
[2025-07-22 17:48:11.048] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:48:11.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0062646"
[2025-07-22 17:48:11.311] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:11.314] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003008"
[2025-07-22 17:48:11.545] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:11.546] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002229"
[2025-07-22 17:48:11.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:11.670] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001887"
[2025-07-22 17:48:11.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:11.867] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002346"
[2025-07-22 17:48:12.075] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:12.091] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0016082"
[2025-07-22 17:48:13.169] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:48:13.171] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001708"
[2025-07-22 17:48:13.348] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:48:13.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0007056"
[2025-07-22 17:48:16.099] [INF] [] Administrator successfully authenticated
[2025-07-22 17:48:16.116] [DBG] [] Auth window closed with result: true
[2025-07-22 17:48:16.118] [INF] [] Authentication successful, initializing main window
[2025-07-22 17:48:16.197] [INF] [] Main window initialized
[2025-07-22 17:48:16.242] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:48:16.245] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:16.251] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:48:16.253] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:16.255] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:48:16.257] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:16.259] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:48:16.260] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:16.262] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:48:16.264] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:16.266] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 17:48:16.267] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:16.392] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 17:48:16.397] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 17:48:16.403] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 17:48:16.408] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 17:48:16.419] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 17:48:16.551] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.556] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.558] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.559] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.561] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.562] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.564] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.566] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.567] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.568] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.570] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.571] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.573] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.579] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.584] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.588] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.592] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.595] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.599] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.604] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.610] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.615] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.617] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.618] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.620] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.621] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.622] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.624] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.625] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.627] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.628] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.630] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.631] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.632] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.634] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.635] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.636] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.638] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.639] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.641] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 17:48:16.642] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.644] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 17:48:16.711] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 17:48:16.728] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:48:16.731] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0011188"
[2025-07-22 17:48:16.736] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:16.739] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0015137"
[2025-07-22 17:48:16.752] [INF] [] Application framework initialization completed
[2025-07-22 17:48:16.757] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 17:48:16.761] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002142"
[2025-07-22 17:48:17.106] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:48:17.107] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000450"
[2025-07-22 17:48:17.109] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 2
[2025-07-22 17:48:17.111] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000495"
[2025-07-22 17:48:17.136] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:48:17.137] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000541"
[2025-07-22 17:48:17.827] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:48:17.829] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002656"
[2025-07-22 17:48:18.438] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:48:18.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003657"
[2025-07-22 17:48:20.175] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.177] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.180] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.188] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:20.189] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000207"
[2025-07-22 17:48:20.195] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 17:48:20.197] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000610"
[2025-07-22 17:48:20.514] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.516] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.518] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.522] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.525] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:20.526] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 17:48:20.529] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:48:20.531] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000448"
[2025-07-22 17:48:20.760] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.794] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.798] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.801] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 17:48:20.804] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 7 To arrange: 0
[2025-07-22 17:48:20.806] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008292"
[2025-07-22 17:48:20.809] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:48:20.810] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000371"
[2025-07-22 17:48:21.594] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:21.596] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000131"
[2025-07-22 17:48:21.600] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:48:21.601] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000520"
[2025-07-22 17:48:21.911] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:21.912] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 17:48:21.917] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:48:21.918] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000588"
[2025-07-22 17:48:22.674] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 17:48:22.676] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000026"
[2025-07-22 17:48:22.708] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 17:48:22.710] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002618"
[2025-07-22 17:48:22.749] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 17:48:22.753] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002979"
[2025-07-22 17:48:23.814] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:23.816] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001250"
[2025-07-22 17:48:23.817] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 17:48:23.819] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000399"
[2025-07-22 17:48:23.967] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 74 To arrange: 0
[2025-07-22 17:48:23.969] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000549"
[2025-07-22 17:48:23.972] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 17:48:23.974] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001134"
[2025-07-22 17:48:23.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:23.984] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000264"
[2025-07-22 17:48:23.985] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:23.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000198"
[2025-07-22 17:48:23.989] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:23.990] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000192"
[2025-07-22 17:48:23.991] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:23.992] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000137"
[2025-07-22 17:48:23.994] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:23.995] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000119"
[2025-07-22 17:48:23.996] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:23.997] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 17:48:23.999] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.000] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000151"
[2025-07-22 17:48:24.033] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.035] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000194"
[2025-07-22 17:48:24.036] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000143"
[2025-07-22 17:48:24.040] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.042] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 17:48:24.043] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.044] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000121"
[2025-07-22 17:48:24.045] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.084] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002197"
[2025-07-22 17:48:24.092] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.096] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000179"
[2025-07-22 17:48:24.097] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 17:48:24.099] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000592"
[2025-07-22 17:48:24.102] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:24.105] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017682"
[2025-07-22 17:48:24.655] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:24.657] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000257"
[2025-07-22 17:48:24.661] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:24.663] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004562"
[2025-07-22 17:48:24.667] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:24.673] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004116"
[2025-07-22 17:48:24.675] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.684] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000319"
[2025-07-22 17:48:24.686] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.688] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000251"
[2025-07-22 17:48:24.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.729] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000220"
[2025-07-22 17:48:24.737] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.789] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000336"
[2025-07-22 17:48:24.822] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.838] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000265"
[2025-07-22 17:48:24.852] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000229"
[2025-07-22 17:48:24.869] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000263"
[2025-07-22 17:48:24.880] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.885] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000242"
[2025-07-22 17:48:24.920] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.922] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000244"
[2025-07-22 17:48:24.924] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.926] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000194"
[2025-07-22 17:48:24.928] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.930] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 17:48:24.932] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000165"
[2025-07-22 17:48:24.937] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.938] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000176"
[2025-07-22 17:48:24.940] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.941] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000194"
[2025-07-22 17:48:24.942] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.945] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000191"
[2025-07-22 17:48:24.947] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:24.951] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004030"
[2025-07-22 17:48:24.953] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.954] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000184"
[2025-07-22 17:48:24.956] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.958] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 17:48:24.961] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.962] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000239"
[2025-07-22 17:48:24.965] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.967] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000192"
[2025-07-22 17:48:24.969] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.971] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:48:24.973] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.975] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 17:48:24.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.979] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000220"
[2025-07-22 17:48:24.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.984] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000173"
[2025-07-22 17:48:24.986] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.988] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000135"
[2025-07-22 17:48:24.989] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.990] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000119"
[2025-07-22 17:48:24.992] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.994] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000151"
[2025-07-22 17:48:24.995] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:24.998] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 17:48:24.999] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.002] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000215"
[2025-07-22 17:48:25.004] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.006] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000204"
[2025-07-22 17:48:25.009] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.010] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000161"
[2025-07-22 17:48:25.013] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.016] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002609"
[2025-07-22 17:48:25.017] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.019] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000238"
[2025-07-22 17:48:25.021] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000198"
[2025-07-22 17:48:25.026] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.028] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000158"
[2025-07-22 17:48:25.030] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.033] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000238"
[2025-07-22 17:48:25.035] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.040] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000225"
[2025-07-22 17:48:25.042] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.045] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000225"
[2025-07-22 17:48:25.046] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.049] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000170"
[2025-07-22 17:48:25.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.053] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 17:48:25.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.058] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 17:48:25.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.062] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000213"
[2025-07-22 17:48:25.065] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.067] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000172"
[2025-07-22 17:48:25.069] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.070] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 17:48:25.073] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.075] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000139"
[2025-07-22 17:48:25.077] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.078] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000135"
[2025-07-22 17:48:25.081] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.083] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000155"
[2025-07-22 17:48:25.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003968"
[2025-07-22 17:48:25.156] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.158] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000130"
[2025-07-22 17:48:25.159] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.180] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000209"
[2025-07-22 17:48:25.182] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.183] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 17:48:25.186] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.188] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000137"
[2025-07-22 17:48:25.190] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.191] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000144"
[2025-07-22 17:48:25.193] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.195] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000193"
[2025-07-22 17:48:25.197] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.199] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000179"
[2025-07-22 17:48:25.201] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.202] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 17:48:25.204] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.205] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000126"
[2025-07-22 17:48:25.206] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.208] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000115"
[2025-07-22 17:48:25.209] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.210] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000098"
[2025-07-22 17:48:25.212] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.213] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 17:48:25.215] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.216] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 17:48:25.217] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.219] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 17:48:25.220] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000091"
[2025-07-22 17:48:25.223] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.225] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004893"
[2025-07-22 17:48:25.227] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.228] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 17:48:25.259] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.263] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000190"
[2025-07-22 17:48:25.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.268] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000177"
[2025-07-22 17:48:25.270] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.272] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000187"
[2025-07-22 17:48:25.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.277] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000207"
[2025-07-22 17:48:25.301] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.302] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000198"
[2025-07-22 17:48:25.305] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.307] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 17:48:25.308] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.311] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000202"
[2025-07-22 17:48:25.313] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.316] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 17:48:25.321] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.323] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000159"
[2025-07-22 17:48:25.326] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.328] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000159"
[2025-07-22 17:48:25.331] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.334] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000181"
[2025-07-22 17:48:25.335] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.338] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000132"
[2025-07-22 17:48:25.340] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.342] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000159"
[2025-07-22 17:48:25.343] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.344] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 17:48:25.420] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.422] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002727"
[2025-07-22 17:48:25.424] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.425] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 17:48:25.426] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.428] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000100"
[2025-07-22 17:48:25.429] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.430] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 17:48:25.432] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.433] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000087"
[2025-07-22 17:48:25.434] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.435] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000084"
[2025-07-22 17:48:25.437] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.439] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 17:48:25.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.442] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 17:48:25.443] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.446] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000155"
[2025-07-22 17:48:25.447] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.449] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000139"
[2025-07-22 17:48:25.450] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.453] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000288"
[2025-07-22 17:48:25.455] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.457] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000209"
[2025-07-22 17:48:25.458] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.460] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000183"
[2025-07-22 17:48:25.461] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.462] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000177"
[2025-07-22 17:48:25.464] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.465] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000138"
[2025-07-22 17:48:25.466] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.467] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 17:48:25.713] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.715] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003820"
[2025-07-22 17:48:25.717] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.718] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000115"
[2025-07-22 17:48:25.720] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.721] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000115"
[2025-07-22 17:48:25.722] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 17:48:25.725] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.727] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000181"
[2025-07-22 17:48:25.728] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.729] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 17:48:25.731] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.732] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 17:48:25.733] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.734] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 17:48:25.735] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.736] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000129"
[2025-07-22 17:48:25.737] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.739] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000179"
[2025-07-22 17:48:25.740] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 17:48:25.742] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.743] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000172"
[2025-07-22 17:48:25.744] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.746] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000120"
[2025-07-22 17:48:25.746] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.748] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000083"
[2025-07-22 17:48:25.749] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.750] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 17:48:25.777] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.778] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000193"
[2025-07-22 17:48:25.780] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.782] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003789"
[2025-07-22 17:48:25.784] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.785] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000169"
[2025-07-22 17:48:25.787] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.788] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000200"
[2025-07-22 17:48:25.790] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.792] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000212"
[2025-07-22 17:48:25.794] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.796] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000255"
[2025-07-22 17:48:25.798] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.800] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000158"
[2025-07-22 17:48:25.801] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.803] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 17:48:25.805] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.807] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 17:48:25.808] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.810] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000152"
[2025-07-22 17:48:25.811] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.813] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000173"
[2025-07-22 17:48:25.814] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.816] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000146"
[2025-07-22 17:48:25.817] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.818] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000129"
[2025-07-22 17:48:25.819] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.821] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000109"
[2025-07-22 17:48:25.822] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.823] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000175"
[2025-07-22 17:48:25.825] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.827] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000176"
[2025-07-22 17:48:25.856] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.857] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 17:48:25.859] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 17:48:25.861] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002783"
[2025-07-22 17:48:25.862] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.864] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000093"
[2025-07-22 17:48:25.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.866] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 17:48:25.868] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.869] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 17:48:25.870] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.872] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000078"
[2025-07-22 17:48:25.873] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.874] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000087"
[2025-07-22 17:48:25.876] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.878] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000183"
[2025-07-22 17:48:25.881] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.890] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000197"
[2025-07-22 17:48:25.892] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.894] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000167"
[2025-07-22 17:48:25.895] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.896] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 17:48:25.898] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.900] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000140"
[2025-07-22 17:48:25.901] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.903] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000160"
[2025-07-22 17:48:25.904] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.931] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000203"
[2025-07-22 17:48:25.934] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.936] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000182"
[2025-07-22 17:48:25.938] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.940] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 17:48:25.943] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 17:48:25.945] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000195"
[2025-07-22 17:48:26.117] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:26.118] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000149"
[2025-07-22 17:48:26.122] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:26.124] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000247"
[2025-07-22 17:48:26.190] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:26.191] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 17:48:26.193] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 17:48:26.195] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005880"
[2025-07-22 17:48:26.263] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:26.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000203"
[2025-07-22 17:48:26.556] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 17:48:26.557] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000174"
[2025-07-22 17:48:26.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 17:48:26.681] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000944"
[2025-07-22 17:48:27.333] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
[2025-07-22 18:24:29.269] [INF] [] Application framework initialization starting
[2025-07-22 18:24:29.283] [INF] [] Forced light theme variant
[2025-07-22 18:24:29.359] [DBG] [] Creating splash screen
[2025-07-22 18:24:29.747] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:24:29.751] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0019874"
[2025-07-22 18:24:29.858] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:24:29.861] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006568"
[2025-07-22 18:24:30.896] [DBG] [] Checking server availability
[2025-07-22 18:24:31.046] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:31.081] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005293"
[2025-07-22 18:24:33.315] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:33.317] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003792"
[2025-07-22 18:24:36.362] [DBG] [] Splash screen closed
[2025-07-22 18:24:36.364] [DBG] [] Creating authentication window
[2025-07-22 18:24:36.550] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:24:36.552] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000025"
[2025-07-22 18:24:36.709] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:24:36.711] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000037"
[2025-07-22 18:24:38.738] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 18:24:38.740] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008048"
[2025-07-22 18:24:38.977] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:24:38.979] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002910"
[2025-07-22 18:24:39.251] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:39.253] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002677"
[2025-07-22 18:24:39.593] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:39.596] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002983"
[2025-07-22 18:24:39.744] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:24:39.747] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003664"
[2025-07-22 18:24:40.370] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:24:40.372] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002280"
[2025-07-22 18:24:40.575] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:40.577] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002995"
[2025-07-22 18:24:40.714] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:40.717] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004049"
[2025-07-22 18:24:40.955] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:40.957] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001920"
[2025-07-22 18:24:41.156] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:41.158] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002854"
[2025-07-22 18:24:41.449] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 18:24:41.455] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0049904"
[2025-07-22 18:24:41.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:41.710] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000317"
[2025-07-22 18:24:42.031] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:24:42.035] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002913"
[2025-07-22 18:24:42.479] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:24:42.481] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002086"
[2025-07-22 18:24:42.642] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:24:42.644] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001560"
[2025-07-22 18:24:42.981] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:42.984] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002272"
[2025-07-22 18:24:43.416] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:43.418] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002492"
[2025-07-22 18:24:43.665] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:43.670] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005299"
[2025-07-22 18:24:43.871] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:43.873] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003177"
[2025-07-22 18:24:45.004] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 18:24:45.008] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001468"
[2025-07-22 18:24:45.318] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:24:45.320] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008712"
[2025-07-22 18:24:48.275] [INF] [] Administrator successfully authenticated
[2025-07-22 18:24:48.295] [DBG] [] Auth window closed with result: true
[2025-07-22 18:24:48.296] [INF] [] Authentication successful, initializing main window
[2025-07-22 18:24:48.357] [INF] [] Main window initialized
[2025-07-22 18:24:48.393] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:24:48.395] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:48.400] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:24:48.402] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:48.405] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:24:48.406] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:48.407] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:24:48.408] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:48.410] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:24:48.411] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:48.413] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:24:48.414] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:48.456] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 18:24:48.457] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 18:24:48.459] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 18:24:48.460] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 18:24:48.462] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 18:24:48.529] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.535] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.537] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.538] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.539] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.540] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.542] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.543] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.544] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.545] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.546] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.547] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.548] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.550] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.551] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.553] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.556] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.557] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.558] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.561] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.562] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.564] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.565] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.566] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.568] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.569] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.570] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.571] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.573] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.574] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.575] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.577] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.578] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.580] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.581] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.583] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.584] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.585] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.587] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.588] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:24:48.589] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.591] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:24:48.749] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 18:24:48.772] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 18:24:48.778] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0021123"
[2025-07-22 18:24:48.784] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:48.789] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0030121"
[2025-07-22 18:24:48.808] [INF] [] Application framework initialization completed
[2025-07-22 18:24:48.815] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 18:24:48.816] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002550"
[2025-07-22 18:24:50.058] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.060] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.062] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.064] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.066] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.068] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.070] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.076] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:50.077] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000282"
[2025-07-22 18:24:50.083] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 18:24:50.084] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000574"
[2025-07-22 18:24:50.377] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.380] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.384] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.390] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:24:50.404] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 7 To arrange: 0
[2025-07-22 18:24:50.410] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0022151"
[2025-07-22 18:24:50.418] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:24:50.420] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000560"
[2025-07-22 18:24:52.985] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:24:52.986] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000019"
[2025-07-22 18:24:53.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 8 To arrange: 0
[2025-07-22 18:24:53.026] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001068"
[2025-07-22 18:24:53.051] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 18:24:53.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002887"
[2025-07-22 18:24:55.896] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 18:24:55.897] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000451"
[2025-07-22 18:24:56.071] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:24:56.113] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0407512"
[2025-07-22 18:24:57.016] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
[2025-07-22 18:24:57.017] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001501"
[2025-07-22 18:25:00.260] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 18:25:00.262] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002437"
[2025-07-22 18:25:01.055] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:25:01.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000185"
[2025-07-22 18:25:01.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:25:01.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000820"
[2025-07-22 18:25:01.575] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:25:01.577] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:25:01.579] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:25:01.593] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:25:01.619] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:25:01.647] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000343"
[2025-07-22 18:25:01.682] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:25:01.683] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000424"
[2025-07-22 18:25:03.033] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:25:03.035] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000025"
[2025-07-22 18:25:03.051] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 18:25:03.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001796"
[2025-07-22 18:25:03.079] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 18:25:03.080] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002153"
[2025-07-22 18:25:04.480] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:25:04.481] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001562"
[2025-07-22 18:25:04.604] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 74 To arrange: 0
[2025-07-22 18:25:04.606] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000784"
[2025-07-22 18:25:04.610] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 18:25:04.611] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001389"
[2025-07-22 18:25:04.621] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.622] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000213"
[2025-07-22 18:25:04.623] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.625] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000232"
[2025-07-22 18:25:04.626] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.627] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000228"
[2025-07-22 18:25:04.628] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.629] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 18:25:04.634] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.635] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000251"
[2025-07-22 18:25:04.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.638] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000273"
[2025-07-22 18:25:04.659] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.660] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000191"
[2025-07-22 18:25:04.662] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.662] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 18:25:04.664] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.665] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 18:25:04.666] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.667] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000162"
[2025-07-22 18:25:04.668] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.669] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 18:25:04.670] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.671] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000133"
[2025-07-22 18:25:04.672] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.673] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000271"
[2025-07-22 18:25:04.675] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:25:04.676] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000210"
[2025-07-22 18:25:04.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:25:04.680] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0016018"
[2025-07-22 18:25:05.210] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.212] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002563"
[2025-07-22 18:25:05.213] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.214] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 18:25:05.216] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.217] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 18:25:05.218] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.220] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 18:25:05.222] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.224] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000259"
[2025-07-22 18:25:05.225] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.227] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000176"
[2025-07-22 18:25:05.228] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.231] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 18:25:05.232] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.234] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000169"
[2025-07-22 18:25:05.235] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.236] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000132"
[2025-07-22 18:25:05.237] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.238] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000144"
[2025-07-22 18:25:05.239] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.240] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 18:25:05.242] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.243] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000155"
[2025-07-22 18:25:05.244] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.245] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 18:25:05.246] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.247] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 18:25:05.248] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.250] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 18:25:05.251] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.252] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000146"
[2025-07-22 18:25:05.253] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 0
[2025-07-22 18:25:05.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003848"
[2025-07-22 18:25:05.345] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.346] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002601"
[2025-07-22 18:25:05.348] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.349] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000081"
[2025-07-22 18:25:05.350] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000067"
[2025-07-22 18:25:05.352] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000056"
[2025-07-22 18:25:05.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.371] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 18:25:05.377] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.381] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000169"
[2025-07-22 18:25:05.382] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.387] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000372"
[2025-07-22 18:25:05.390] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.391] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 18:25:05.392] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.394] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 18:25:05.395] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.396] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000069"
[2025-07-22 18:25:05.397] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000059"
[2025-07-22 18:25:05.399] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.400] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000165"
[2025-07-22 18:25:05.401] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.403] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000162"
[2025-07-22 18:25:05.404] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.405] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 18:25:05.406] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.407] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000111"
[2025-07-22 18:25:05.409] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.410] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:25:05.412] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.413] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002663"
[2025-07-22 18:25:05.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.415] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000088"
[2025-07-22 18:25:05.416] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.418] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 18:25:05.419] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.420] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000139"
[2025-07-22 18:25:05.421] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.422] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000116"
[2025-07-22 18:25:05.423] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.424] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000098"
[2025-07-22 18:25:05.425] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.426] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000094"
[2025-07-22 18:25:05.428] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.429] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000090"
[2025-07-22 18:25:05.430] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.431] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 18:25:05.432] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.433] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000078"
[2025-07-22 18:25:05.434] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.435] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000069"
[2025-07-22 18:25:05.436] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.437] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000105"
[2025-07-22 18:25:05.467] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.468] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000158"
[2025-07-22 18:25:05.469] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.472] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000194"
[2025-07-22 18:25:05.473] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.474] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000174"
[2025-07-22 18:25:05.475] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.476] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000222"
[2025-07-22 18:25:05.581] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.583] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005042"
[2025-07-22 18:25:05.585] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.586] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:25:05.587] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.588] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000111"
[2025-07-22 18:25:05.590] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.617] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000172"
[2025-07-22 18:25:05.619] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.620] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000240"
[2025-07-22 18:25:05.621] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.623] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 18:25:05.624] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.625] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 18:25:05.626] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.627] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 18:25:05.628] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.629] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 18:25:05.631] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.632] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 18:25:05.633] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.634] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 18:25:05.635] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.636] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 18:25:05.637] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.638] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 18:25:05.639] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.640] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000146"
[2025-07-22 18:25:05.641] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.642] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000089"
[2025-07-22 18:25:05.643] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.644] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000130"
[2025-07-22 18:25:05.646] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.650] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003056"
[2025-07-22 18:25:05.651] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.653] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 18:25:05.654] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.656] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 18:25:05.657] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.658] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 18:25:05.659] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.660] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 18:25:05.665] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.666] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000144"
[2025-07-22 18:25:05.667] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.669] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 18:25:05.670] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.671] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000232"
[2025-07-22 18:25:05.672] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.673] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000128"
[2025-07-22 18:25:05.674] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.676] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 18:25:05.677] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.678] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000101"
[2025-07-22 18:25:05.679] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.680] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 18:25:05.703] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000184"
[2025-07-22 18:25:05.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.706] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000158"
[2025-07-22 18:25:05.708] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.709] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 18:25:05.710] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.711] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000099"
[2025-07-22 18:25:05.712] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.714] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002596"
[2025-07-22 18:25:05.715] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.716] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000085"
[2025-07-22 18:25:05.717] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.718] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000073"
[2025-07-22 18:25:05.719] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.720] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000085"
[2025-07-22 18:25:05.721] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.722] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000121"
[2025-07-22 18:25:05.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.725] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000186"
[2025-07-22 18:25:05.726] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.727] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 18:25:05.728] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.729] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000084"
[2025-07-22 18:25:05.730] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.731] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000116"
[2025-07-22 18:25:05.732] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.733] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000081"
[2025-07-22 18:25:05.734] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.735] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000074"
[2025-07-22 18:25:05.736] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.737] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 18:25:05.738] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.739] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000100"
[2025-07-22 18:25:05.740] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 18:25:05.742] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.743] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000110"
[2025-07-22 18:25:05.744] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.745] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000078"
[2025-07-22 18:25:05.856] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.858] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002762"
[2025-07-22 18:25:05.859] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.860] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000109"
[2025-07-22 18:25:05.861] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.894] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000185"
[2025-07-22 18:25:05.897] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.898] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 18:25:05.899] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.900] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000120"
[2025-07-22 18:25:05.901] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.902] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000168"
[2025-07-22 18:25:05.903] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.904] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 18:25:05.905] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.906] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000097"
[2025-07-22 18:25:05.907] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.908] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000088"
[2025-07-22 18:25:05.909] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.910] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000111"
[2025-07-22 18:25:05.911] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.912] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000080"
[2025-07-22 18:25:05.913] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.914] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000079"
[2025-07-22 18:25:05.915] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.916] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 18:25:05.941] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.943] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000189"
[2025-07-22 18:25:05.944] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.945] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000119"
[2025-07-22 18:25:05.974] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000161"
[2025-07-22 18:25:05.980] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:05.981] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004630"
[2025-07-22 18:25:05.983] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.984] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000063"
[2025-07-22 18:25:05.985] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.986] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000063"
[2025-07-22 18:25:05.986] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.987] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000059"
[2025-07-22 18:25:05.988] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.990] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000070"
[2025-07-22 18:25:05.990] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.991] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000065"
[2025-07-22 18:25:05.992] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.993] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000074"
[2025-07-22 18:25:05.994] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.995] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000061"
[2025-07-22 18:25:05.996] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:05.997] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000053"
[2025-07-22 18:25:06.021] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.025] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000234"
[2025-07-22 18:25:06.028] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.030] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000166"
[2025-07-22 18:25:06.031] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.032] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000099"
[2025-07-22 18:25:06.056] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 18:25:06.058] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.059] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 18:25:06.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 18:25:06.063] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.064] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000105"
[2025-07-22 18:25:06.166] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:06.179] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0022891"
[2025-07-22 18:25:06.189] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.196] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000413"
[2025-07-22 18:25:06.219] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.221] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 18:25:06.251] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.253] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000257"
[2025-07-22 18:25:06.255] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.257] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000187"
[2025-07-22 18:25:06.259] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.260] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000135"
[2025-07-22 18:25:06.262] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.263] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000116"
[2025-07-22 18:25:06.265] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.266] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000159"
[2025-07-22 18:25:06.267] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.268] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000180"
[2025-07-22 18:25:06.269] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.270] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000093"
[2025-07-22 18:25:06.272] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.274] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 18:25:06.275] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.276] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000048"
[2025-07-22 18:25:06.277] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.278] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000076"
[2025-07-22 18:25:06.279] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.280] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000078"
[2025-07-22 18:25:06.281] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.284] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 18:25:06.287] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.288] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 18:25:06.289] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:25:06.291] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002727"
[2025-07-22 18:25:06.292] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.293] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 18:25:06.294] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.295] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000089"
[2025-07-22 18:25:06.296] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.297] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000075"
[2025-07-22 18:25:06.298] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.298] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000075"
[2025-07-22 18:25:06.320] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.321] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000173"
[2025-07-22 18:25:06.323] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.324] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000137"
[2025-07-22 18:25:06.325] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.326] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 18:25:06.327] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.351] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000306"
[2025-07-22 18:25:06.354] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.355] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000132"
[2025-07-22 18:25:06.357] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.358] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000096"
[2025-07-22 18:25:06.359] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.361] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000094"
[2025-07-22 18:25:06.362] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.363] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000067"
[2025-07-22 18:25:06.364] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.365] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000071"
[2025-07-22 18:25:06.366] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.373] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 18:25:06.399] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:25:06.401] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000191"
[2025-07-22 18:25:06.406] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:25:06.407] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000209"
[2025-07-22 18:25:06.409] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:25:06.411] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 18:25:06.493] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:25:06.494] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000120"
[2025-07-22 18:25:06.979] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:25:06.981] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000217"
[2025-07-22 18:25:07.100] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:25:07.101] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003282"
[2025-07-22 18:25:07.602] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
[2025-07-22 18:30:01.722] [INF] [] Application framework initialization starting
[2025-07-22 18:30:01.731] [INF] [] Forced light theme variant
[2025-07-22 18:30:01.829] [DBG] [] Creating splash screen
[2025-07-22 18:30:02.022] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:30:02.027] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0017665"
[2025-07-22 18:30:02.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:02.149] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0013089"
[2025-07-22 18:30:03.227] [DBG] [] Checking server availability
[2025-07-22 18:30:03.317] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:03.319] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0006233"
[2025-07-22 18:30:05.592] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:05.595] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004242"
[2025-07-22 18:30:08.636] [DBG] [] Splash screen closed
[2025-07-22 18:30:08.638] [DBG] [] Creating authentication window
[2025-07-22 18:30:08.724] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:30:08.726] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000044"
[2025-07-22 18:30:08.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:30:08.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000046"
[2025-07-22 18:30:09.723] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 18:30:09.725] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008145"
[2025-07-22 18:30:10.009] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:10.011] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005792"
[2025-07-22 18:30:10.417] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:10.419] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004003"
[2025-07-22 18:30:11.021] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:11.023] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003683"
[2025-07-22 18:30:11.295] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:11.297] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002148"
[2025-07-22 18:30:11.560] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:11.562] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002504"
[2025-07-22 18:30:11.793] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 18:30:11.799] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0046427"
[2025-07-22 18:30:12.047] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:12.048] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003157"
[2025-07-22 18:30:12.300] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:12.302] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002718"
[2025-07-22 18:30:12.459] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:12.461] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005357"
[2025-07-22 18:30:12.674] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:12.676] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002562"
[2025-07-22 18:30:12.881] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:12.883] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003579"
[2025-07-22 18:30:14.235] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 18:30:14.236] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002621"
[2025-07-22 18:30:14.443] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 2 To arrange: 0
[2025-07-22 18:30:14.446] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0008234"
[2025-07-22 18:30:17.145] [INF] [] Administrator successfully authenticated
[2025-07-22 18:30:17.161] [DBG] [] Auth window closed with result: true
[2025-07-22 18:30:17.163] [INF] [] Authentication successful, initializing main window
[2025-07-22 18:30:17.242] [INF] [] Main window initialized
[2025-07-22 18:30:17.280] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:30:17.283] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:17.289] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:30:17.291] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:17.293] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:30:17.295] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:17.297] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:30:17.299] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:17.301] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:30:17.303] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:17.305] [WRN] [] [Binding] [Avalonia.Controls.Primitives.Popup] An error occurred binding MinWidth to $parent[TextBox].Bounds.Width at $parent[TextBox]: Ancestor not found.
[2025-07-22 18:30:17.306] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:17.346] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!LeftPinnedDockables.Count at LeftPinnedDockables: Value is null.
[2025-07-22 18:30:17.347] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!RightPinnedDockables.Count at RightPinnedDockables: Value is null.
[2025-07-22 18:30:17.349] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!TopPinnedDockables.Count at TopPinnedDockables: Value is null.
[2025-07-22 18:30:17.350] [WRN] [] [Binding] [Dock.Avalonia.Controls.ToolPinnedControl] An error occurred binding IsVisible to !!BottomPinnedDockables.Count at BottomPinnedDockables: Value is null.
[2025-07-22 18:30:17.353] [WRN] [] [Binding] [Dock.Avalonia.Controls.PinnedDockControl] An error occurred binding PinnedDockAlignment to PinnedDock.Alignment at PinnedDock: Value is null.
[2025-07-22 18:30:17.412] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.416] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.417] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.419] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.420] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.422] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.423] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.424] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.426] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.427] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.429] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.430] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.431] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.433] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.434] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.436] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.437] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.438] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.440] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.442] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.443] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.445] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.446] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.448] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.449] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.450] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.452] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.453] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.455] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.456] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.458] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.459] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.460] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.462] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.464] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.465] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.466] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.468] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.469] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.471] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding Proportion to Proportion at Proportion: Could not find a matching property accessor for 'Proportion' on 'SuperNova.VisualDesigner.ComponentToolViewModel'.
[2025-07-22 18:30:17.472] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsCollapsable at IsCollapsable: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.474] [WRN] [] [Binding] [Avalonia.Controls.Presenters.ContentPresenter] An error occurred binding (unknown) to IsEmpty at IsEmpty: Unable to cast object of type 'SuperNova.VisualDesigner.ComponentToolViewModel' to type 'Dock.Model.Core.IDock'.
[2025-07-22 18:30:17.567] [WRN] [] [Binding] [Avalonia.Controls.Grid] An error occurred binding IsVisible to !PinnedDock.IsEmpty at PinnedDock: Value is null.
[2025-07-22 18:30:17.578] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 18:30:17.580] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0009677"
[2025-07-22 18:30:17.584] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:17.587] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0018597"
[2025-07-22 18:30:17.601] [INF] [] Application framework initialization completed
[2025-07-22 18:30:17.610] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 2
[2025-07-22 18:30:17.612] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003234"
[2025-07-22 18:30:18.771] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:18.774] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:18.776] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:18.779] [WRN] [] [Binding] [Avalonia.Animation.AnimatorKeyFrame] An error occurred binding Value to Bounds.Height at Bounds: Property 'Bounds not registered on 'Avalonia.Animation.AnimatorKeyFrame
[2025-07-22 18:30:18.784] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:18.785] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 18:30:18.790] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 1
[2025-07-22 18:30:18.791] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000690"
[2025-07-22 18:30:19.887] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 0
[2025-07-22 18:30:19.888] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000042"
[2025-07-22 18:30:19.914] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 24 To arrange: 0
[2025-07-22 18:30:19.915] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001751"
[2025-07-22 18:30:19.946] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 1
[2025-07-22 18:30:19.948] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002783"
[2025-07-22 18:30:20.911] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:20.916] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001744"
[2025-07-22 18:30:20.917] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 6 To arrange: 0
[2025-07-22 18:30:20.918] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000392"
[2025-07-22 18:30:21.051] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 74 To arrange: 0
[2025-07-22 18:30:21.052] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000641"
[2025-07-22 18:30:21.055] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 1
[2025-07-22 18:30:21.057] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001335"
[2025-07-22 18:30:21.060] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.061] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000197"
[2025-07-22 18:30:21.063] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.064] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 18:30:21.065] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.066] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 18:30:21.067] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.068] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 18:30:21.069] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.070] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000149"
[2025-07-22 18:30:21.106] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.107] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000335"
[2025-07-22 18:30:21.109] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.110] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000254"
[2025-07-22 18:30:21.112] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.137] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000274"
[2025-07-22 18:30:21.139] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000206"
[2025-07-22 18:30:21.142] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.144] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 18:30:21.145] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.146] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 18:30:21.147] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 18:30:21.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.151] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 18:30:21.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 3
[2025-07-22 18:30:21.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000174"
[2025-07-22 18:30:21.190] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:21.196] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0036157"
[2025-07-22 18:30:21.762] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:21.764] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002764"
[2025-07-22 18:30:21.765] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.770] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 18:30:21.805] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.811] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000342"
[2025-07-22 18:30:21.817] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.821] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000175"
[2025-07-22 18:30:21.823] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.825] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:30:21.826] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.827] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000089"
[2025-07-22 18:30:21.828] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.830] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000086"
[2025-07-22 18:30:21.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.832] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000227"
[2025-07-22 18:30:21.834] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.835] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000133"
[2025-07-22 18:30:21.836] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.837] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:30:21.838] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.839] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:30:21.840] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.841] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 18:30:21.842] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.843] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000109"
[2025-07-22 18:30:21.844] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.845] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000138"
[2025-07-22 18:30:21.846] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.847] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000165"
[2025-07-22 18:30:21.849] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.850] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000135"
[2025-07-22 18:30:21.851] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:21.852] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002464"
[2025-07-22 18:30:21.853] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.854] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000070"
[2025-07-22 18:30:21.855] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.856] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000147"
[2025-07-22 18:30:21.857] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.858] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 18:30:21.859] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.860] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000109"
[2025-07-22 18:30:21.861] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.862] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000134"
[2025-07-22 18:30:21.863] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.865] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 18:30:21.866] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.867] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 18:30:21.868] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.869] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000112"
[2025-07-22 18:30:21.870] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.871] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000183"
[2025-07-22 18:30:21.872] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.873] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000166"
[2025-07-22 18:30:21.874] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.875] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000186"
[2025-07-22 18:30:21.876] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.877] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 18:30:21.878] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.879] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000256"
[2025-07-22 18:30:21.911] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.916] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000457"
[2025-07-22 18:30:21.919] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:21.921] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000136"
[2025-07-22 18:30:21.923] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 4 To arrange: 0
[2025-07-22 18:30:21.926] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0005449"
[2025-07-22 18:30:22.028] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.030] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002677"
[2025-07-22 18:30:22.032] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.033] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 18:30:22.035] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.037] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000224"
[2025-07-22 18:30:22.038] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.067] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000267"
[2025-07-22 18:30:22.068] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.070] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000172"
[2025-07-22 18:30:22.071] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.073] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000148"
[2025-07-22 18:30:22.075] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.076] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000248"
[2025-07-22 18:30:22.078] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.079] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 18:30:22.081] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.083] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000292"
[2025-07-22 18:30:22.084] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.086] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000152"
[2025-07-22 18:30:22.090] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.092] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000170"
[2025-07-22 18:30:22.095] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.097] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000220"
[2025-07-22 18:30:22.099] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.101] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000329"
[2025-07-22 18:30:22.129] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.130] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 18:30:22.132] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.133] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000170"
[2025-07-22 18:30:22.134] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.135] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000127"
[2025-07-22 18:30:22.137] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.138] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002487"
[2025-07-22 18:30:22.139] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.141] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:30:22.142] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.143] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000164"
[2025-07-22 18:30:22.144] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.146] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000178"
[2025-07-22 18:30:22.147] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.148] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000238"
[2025-07-22 18:30:22.149] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.150] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 18:30:22.151] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.152] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 18:30:22.153] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.154] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000124"
[2025-07-22 18:30:22.155] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.156] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 18:30:22.157] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.158] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000093"
[2025-07-22 18:30:22.159] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.160] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 18:30:22.161] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.162] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000153"
[2025-07-22 18:30:22.163] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.164] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000192"
[2025-07-22 18:30:22.165] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.166] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000157"
[2025-07-22 18:30:22.167] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.168] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 18:30:22.169] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.170] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 18:30:22.316] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.318] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002771"
[2025-07-22 18:30:22.319] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.321] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 18:30:22.322] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.324] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000132"
[2025-07-22 18:30:22.325] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.327] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 18:30:22.329] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.330] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000214"
[2025-07-22 18:30:22.332] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.334] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000220"
[2025-07-22 18:30:22.335] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.337] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000163"
[2025-07-22 18:30:22.338] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.339] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000161"
[2025-07-22 18:30:22.340] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.341] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 18:30:22.342] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.343] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000217"
[2025-07-22 18:30:22.344] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.345] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000191"
[2025-07-22 18:30:22.349] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.350] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000202"
[2025-07-22 18:30:22.352] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.353] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000182"
[2025-07-22 18:30:22.377] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.379] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 18:30:22.398] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.407] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000186"
[2025-07-22 18:30:22.408] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.410] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000144"
[2025-07-22 18:30:22.411] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.413] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003626"
[2025-07-22 18:30:22.414] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.415] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000139"
[2025-07-22 18:30:22.416] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.417] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000093"
[2025-07-22 18:30:22.418] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.419] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 18:30:22.420] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.421] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000091"
[2025-07-22 18:30:22.422] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.423] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 18:30:22.425] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.426] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 18:30:22.427] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.427] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 18:30:22.429] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.429] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000091"
[2025-07-22 18:30:22.430] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.431] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000072"
[2025-07-22 18:30:22.432] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.433] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000065"
[2025-07-22 18:30:22.434] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.435] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000083"
[2025-07-22 18:30:22.436] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.438] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000107"
[2025-07-22 18:30:22.439] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.440] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000131"
[2025-07-22 18:30:22.441] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.442] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 18:30:22.443] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.444] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000100"
[2025-07-22 18:30:22.654] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.656] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003714"
[2025-07-22 18:30:22.658] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.659] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000137"
[2025-07-22 18:30:22.660] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.662] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000110"
[2025-07-22 18:30:22.663] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.664] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000184"
[2025-07-22 18:30:22.687] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.688] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000190"
[2025-07-22 18:30:22.689] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.690] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000117"
[2025-07-22 18:30:22.692] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.693] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000118"
[2025-07-22 18:30:22.694] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.695] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 18:30:22.696] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.697] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000174"
[2025-07-22 18:30:22.698] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.699] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000164"
[2025-07-22 18:30:22.700] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.702] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000161"
[2025-07-22 18:30:22.703] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.704] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000129"
[2025-07-22 18:30:22.705] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.733] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000152"
[2025-07-22 18:30:22.734] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.735] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000139"
[2025-07-22 18:30:22.736] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.738] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000088"
[2025-07-22 18:30:22.739] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.740] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000094"
[2025-07-22 18:30:22.741] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.743] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0002612"
[2025-07-22 18:30:22.744] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.745] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000082"
[2025-07-22 18:30:22.746] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.778] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000177"
[2025-07-22 18:30:22.780] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.781] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 18:30:22.782] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.783] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 18:30:22.785] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.786] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000103"
[2025-07-22 18:30:22.787] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.788] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 18:30:22.789] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.790] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000104"
[2025-07-22 18:30:22.791] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.793] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000106"
[2025-07-22 18:30:22.794] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.795] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000102"
[2025-07-22 18:30:22.797] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.826] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000199"
[2025-07-22 18:30:22.827] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.828] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000123"
[2025-07-22 18:30:22.829] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.831] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000172"
[2025-07-22 18:30:22.832] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.833] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000142"
[2025-07-22 18:30:22.834] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.835] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000087"
[2025-07-22 18:30:22.836] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.837] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000095"
[2025-07-22 18:30:22.974] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 23 To arrange: 1
[2025-07-22 18:30:22.976] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0003400"
[2025-07-22 18:30:22.978] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.979] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000167"
[2025-07-22 18:30:22.981] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:22.982] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000184"
[2025-07-22 18:30:22.983] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.011] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000154"
[2025-07-22 18:30:23.012] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.013] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000125"
[2025-07-22 18:30:23.015] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.016] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 18:30:23.017] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.017] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000105"
[2025-07-22 18:30:23.019] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.020] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000070"
[2025-07-22 18:30:23.021] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.023] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000114"
[2025-07-22 18:30:23.024] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.025] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000113"
[2025-07-22 18:30:23.026] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.028] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000141"
[2025-07-22 18:30:23.029] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.032] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000133"
[2025-07-22 18:30:23.033] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.034] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000150"
[2025-07-22 18:30:23.036] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.037] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000122"
[2025-07-22 18:30:23.038] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000145"
[2025-07-22 18:30:23.039] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 0 To arrange: 1
[2025-07-22 18:30:23.040] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000164"
[2025-07-22 18:30:23.159] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:23.160] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000108"
[2025-07-22 18:30:23.163] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:23.164] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000156"
[2025-07-22 18:30:23.247] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:23.249] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000213"
[2025-07-22 18:30:23.251] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:23.253] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0004991"
[2025-07-22 18:30:23.375] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:23.376] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000151"
[2025-07-22 18:30:23.671] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 1 To arrange: 0
[2025-07-22 18:30:23.674] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0000349"
[2025-07-22 18:30:23.782] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 3 To arrange: 0
[2025-07-22 18:30:23.783] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Layout pass finished in "00:00:00.0001586"
[2025-07-22 18:30:24.347] [INF] [] [Layout] [Avalonia.Layout.LayoutManager] Started layout pass. To measure: 5 To arrange: 0
