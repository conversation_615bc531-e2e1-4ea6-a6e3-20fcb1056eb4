[2025-07-22 17:25:43.068] [ERR] [] Server not available
[2025-07-22 17:33:25.711] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 32
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:33:51.545] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 32
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 17:34:14.483] [FTL] [] Application terminated unexpectedly
System.Collections.Generic.KeyNotFoundException: Static resource 'SystemBaseLowColor' not found.
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider, Object resourceKey)
   at Avalonia.Markup.Xaml.MarkupExtensions.StaticResourceExtension.ProvideValue(IServiceProvider serviceProvider)
   at CompiledAvaloniaXaml.!AvaloniaResources.XamlClosure_1.Build_38(IServiceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.PointerDeferredContent`1.InvokeBuilder(IServiceProvider serviceProvider)
   at Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers.DeferredContent`1.Build(IServiceProvider serviceProvider)
   at Avalonia.Controls.ResourceDictionary.TryGetValue(Object key, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceDictionary.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Styling.Styles.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Application.TryGetResource(Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.TryFindResource(IResourceHost control, Object key, ThemeVariant theme, Object& value)
   at Avalonia.Controls.ResourceNodeExtensions.FindResource(IResourceHost control, ThemeVariant theme, Object key)
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.PublishValue()
   at Avalonia.Markup.Xaml.MarkupExtensions.DynamicResourceExpression.ResourcesChanged(Object sender, ResourcesChangedEventArgs e)
   at Avalonia.Application.Avalonia.Controls.IResourceHost.NotifyHostedResourcesChanged(ResourcesChangedEventArgs e)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceDictionary.OnAddOwner(IResourceHost owner)
   at Avalonia.Controls.ResourceProvider.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.Avalonia.Controls.IResourceProvider.AddOwner(IResourceHost owner)
   at Avalonia.Styling.Styles.InternalAdd(IList items, IResourceHost owner)
   at Avalonia.Styling.Styles.OnCollectionChanged(Object sender, NotifyCollectionChangedEventArgs e)
   at Avalonia.Collections.AvaloniaList`1.NotifyAdd(T item, Int32 index)
   at Avalonia.Collections.AvaloniaList`1.Add(T item)
   at Avalonia.Styling.Styles.Add(IStyle item)
   at SuperNova.App.!XamlIlPopulate(IServiceProvider, App) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml:line 30
   at SuperNova.App.!XamlIlPopulateTrampoline(App)
   at SuperNova.App.Initialize() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\App.axaml.cs:line 80
   at Avalonia.AppBuilder.SetupUnsafe()
   at Avalonia.AppBuilder.Setup()
   at Avalonia.AppBuilder.SetupWithLifetime(IApplicationLifetime lifetime)
   at Avalonia.ClassicDesktopStyleApplicationLifetimeExtensions.StartWithClassicDesktopLifetime(AppBuilder builder, String[] args, ShutdownMode shutdownMode)
   at SuperNova.Desktop.Program.Main(String[] args) in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Desktop\Program.cs:line 35
[2025-07-22 18:50:59.712] [ERR] [] Authentication error
System.InvalidCastException: Unable to cast object of type 'SuperNova.Converters.SafeDateTimeFormatInfo' to type 'System.Globalization.DateTimeFormatInfo'.
   at System.Globalization.DateTimeFormatInfo.get_CurrentInfo()
   at System.DateTime.TryParse(String s, DateTime& result)
   at Microsoft.IdentityModel.JsonWebTokens.JwtTokenUtilities.GetStringClaimValueType(String str)
   at System.IdentityModel.Tokens.Jwt.JwtPayload.GetClaimValueType(Object value)
   at System.IdentityModel.Tokens.Jwt.JwtPayload.get_Claims()
   at System.IdentityModel.Tokens.Jwt.JwtSecurityToken.get_Claims()
   at SuperNova.Forms.ViewModels.AuthViewModel.LoginAsync() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\Forms\ViewModels\AuthViewModel.cs:line 86
[2025-07-22 18:51:40.835] [ERR] [] Authentication error
System.InvalidCastException: Unable to cast object of type 'SuperNova.Converters.SafeDateTimeFormatInfo' to type 'System.Globalization.DateTimeFormatInfo'.
   at System.Globalization.DateTimeFormatInfo.get_CurrentInfo()
   at System.DateTime.TryParse(String s, DateTime& result)
   at Microsoft.IdentityModel.JsonWebTokens.JwtTokenUtilities.GetStringClaimValueType(String str)
   at System.IdentityModel.Tokens.Jwt.JwtPayload.GetClaimValueType(Object value)
   at System.IdentityModel.Tokens.Jwt.JwtPayload.get_Claims()
   at System.IdentityModel.Tokens.Jwt.JwtSecurityToken.get_Claims()
   at SuperNova.Forms.ViewModels.AuthViewModel.LoginAsync() in G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\Forms\ViewModels\AuthViewModel.cs:line 86
