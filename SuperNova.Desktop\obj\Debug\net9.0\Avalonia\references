C:\Users\<USER>\.nuget\packages\antlr4.runtime.standard\4.13.1\lib\netstandard2.0\Antlr4.Runtime.Standard.dll
C:\Users\<USER>\.nuget\packages\antlr4buildtasks\12.8.0\lib\netstandard2.0\Antlr4BuildTasks.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Base.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.colorpicker\11.2.3\lib\net8.0\Avalonia.Controls.ColorPicker.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.datagrid\11.2.3\lib\net8.0\Avalonia.Controls.DataGrid.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Controls.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.proportionalstackpanel\11.2.0\lib\net8.0\Avalonia.Controls.ProportionalStackPanel.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.recycling\11.2.0\lib\net8.0\Avalonia.Controls.Recycling.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.recycling.model\11.2.0\lib\net8.0\Avalonia.Controls.Recycling.Model.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.DesignerSupport.dll
C:\Users\<USER>\.nuget\packages\avalonia.desktop\11.2.3\lib\net8.0\Avalonia.Desktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.diagnostics\11.2.3\lib\net8.0\Avalonia.Diagnostics.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Dialogs.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.dll
C:\Users\<USER>\.nuget\packages\avalonia.freedesktop\11.2.3\lib\net8.0\Avalonia.FreeDesktop.dll
C:\Users\<USER>\.nuget\packages\avalonia.labs.controls\11.0.0\lib\net6.0\Avalonia.Labs.Controls.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Markup.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Markup.Xaml.dll
C:\Users\<USER>\.nuget\packages\avalonia.markupextension\11.2.0\lib\net8.0\Avalonia.MarkupExtension.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Metal.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.MicroCom.dll
C:\Users\<USER>\.nuget\packages\avalonia.native\11.2.3\lib\net8.0\Avalonia.Native.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.OpenGL.dll
C:\Users\<USER>\.nuget\packages\avalonia.reactiveui\11.2.3\lib\net8.0\Avalonia.ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\avalonia.remote.protocol\11.2.3\lib\net8.0\Avalonia.Remote.Protocol.dll
C:\Users\<USER>\.nuget\packages\avalonia.skia\11.2.3\lib\net8.0\Avalonia.Skia.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.fluent\11.0.0\lib\net6.0\Avalonia.Themes.Fluent.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.simple\11.2.3\lib\net8.0\Avalonia.Themes.Simple.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Vulkan.dll
C:\Users\<USER>\.nuget\packages\avalonia.win32\11.2.3\lib\net8.0\Avalonia.Win32.dll
C:\Users\<USER>\.nuget\packages\avalonia.x11\11.2.3\lib\net8.0\Avalonia.X11.dll
C:\Users\<USER>\.nuget\packages\avalonia.avaloniaedit\11.1.0\lib\net6.0\AvaloniaEdit.dll
C:\Users\<USER>\.nuget\packages\classic.avalonia.theme.colorpicker\11.2.0\lib\netstandard2.0\Classic.Avalonia.Theme.ColorPicker.dll
C:\Users\<USER>\.nuget\packages\classic.avalonia.theme.datagrid\11.2.0\lib\netstandard2.0\Classic.Avalonia.Theme.DataGrid.dll
C:\Users\<USER>\.nuget\packages\classic.avalonia.theme\11.2.0\lib\netstandard2.0\Classic.Avalonia.Theme.dll
C:\Users\<USER>\.nuget\packages\classic.avalonia.theme.dock\11.2.0\lib\netstandard2.0\Classic.Avalonia.Theme.Dock.dll
C:\Users\<USER>\.nuget\packages\classic.commoncontrols.avalonia\11.2.0\lib\netstandard2.0\Classic.CommonControls.Avalonia.dll
C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.3.2\lib\net8.0\CommunityToolkit.Mvvm.dll
C:\Users\<USER>\.nuget\packages\csvhelper\33.0.1\lib\net8.0\CsvHelper.dll
C:\Users\<USER>\.nuget\packages\dialoghost.avalonia\0.8.1\lib\netstandard2.0\DialogHost.Avalonia.dll
C:\Users\<USER>\.nuget\packages\dock.avalonia\11.2.0\lib\net8.0\Dock.Avalonia.dll
C:\Users\<USER>\.nuget\packages\dock.model.avalonia\11.2.0\lib\net8.0\Dock.Model.Avalonia.dll
C:\Users\<USER>\.nuget\packages\dock.model\11.2.0\lib\net8.0\Dock.Model.dll
C:\Users\<USER>\.nuget\packages\dock.model.mvvm\11.2.0\lib\net8.0\Dock.Model.Mvvm.dll
C:\Users\<USER>\.nuget\packages\dock.serializer\11.2.0\lib\net8.0\Dock.Serializer.dll
C:\Users\<USER>\.nuget\packages\dock.settings\11.2.0\lib\net8.0\Dock.Settings.dll
C:\Users\<USER>\.nuget\packages\dynamicdata\8.4.1\lib\net8.0\DynamicData.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp\*******\lib\net6.0\HarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\languageext.core\4.4.9\lib\netstandard2.0\LanguageExt.Core.dll
C:\Users\<USER>\.nuget\packages\livechartscore\2.0.0-rc2\lib\net6.0\LiveChartsCore.dll
C:\Users\<USER>\.nuget\packages\livechartscore.skiasharpview.avalonia\2.0.0-rc2\lib\net6.0\LiveChartsCore.SkiaSharpView.Avalonia.dll
C:\Users\<USER>\.nuget\packages\livechartscore.skiasharpview\2.0.0-rc2\lib\net6.0\LiveChartsCore.SkiaSharpView.dll
C:\Users\<USER>\.nuget\packages\material.icons.avalonia\2.1.0\lib\netstandard2.0\Material.Icons.Avalonia.dll
C:\Users\<USER>\.nuget\packages\material.icons\2.1.0\lib\netstandard2.0\Material.Icons.dll
C:\Users\<USER>\.nuget\packages\microcom.runtime\0.11.0\lib\net5.0\MicroCom.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.http.abstractions\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.http.features\2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll
C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\7.0.0\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\.nuget\packages\microsoft.build.framework\17.8.3\ref\net8.0\Microsoft.Build.Framework.dll
C:\Users\<USER>\.nuget\packages\microsoft.build.utilities.core\17.8.3\ref\net8.0\Microsoft.Build.Utilities.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.data.sqlite.core\9.0.1\lib\net8.0\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.abstractions\9.0.1\lib\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore\9.0.1\lib\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.relational\9.0.1\lib\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\.nuget\packages\microsoft.entityframeworkcore.sqlite.core\9.0.1\lib\net8.0\Microsoft.EntityFrameworkCore.Sqlite.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.caching.abstractions\9.0.1\lib\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.caching.memory\9.0.1\lib\net9.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\9.0.1\lib\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\9.0.0\lib\net9.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\9.0.1\lib\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\9.0.1\lib\net9.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencymodel\9.0.1\lib\net9.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.1\lib\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\9.0.1\lib\net9.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.1\lib\net9.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\9.0.1\lib\net9.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.identitymodel.abstractions\7.4.0\lib\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.identitymodel.jsonwebtokens\7.4.0\lib\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\.nuget\packages\microsoft.identitymodel.logging\7.4.0\lib\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\.nuget\packages\microsoft.identitymodel.tokens\7.4.0\lib\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\.nuget\packages\microsoft.net.stringtools\17.8.3\ref\net8.0\Microsoft.NET.StringTools.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.VisualBasic.dll
C:\Users\<USER>\.nuget\packages\microsoft.visualstudio.setup.configuration.interop\3.2.2146\lib\netstandard2.1\Microsoft.VisualStudio.Setup.Configuration.Interop.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.Win32.Registry.dll
C:\Users\<USER>\.nuget\packages\microsoft.win32.systemevents\7.0.0\lib\net7.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\.nuget\packages\messagebox.avalonia\3.2.0\lib\netstandard2.0\MsBox.Avalonia.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\mscorlib.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\netstandard.dll
C:\Users\<USER>\.nuget\packages\newtonsoft.json\13.0.1\lib\netstandard2.0\Newtonsoft.Json.dll
C:\Users\<USER>\.nuget\packages\pleasantui\4.0.1\lib\netstandard2.0\PleasantUI.dll
C:\Users\<USER>\.nuget\packages\qrcoder\1.6.0\lib\net6.0\QRCoder.dll
C:\Users\<USER>\.nuget\packages\r3\1.2.9\lib\net8.0\R3.dll
C:\Users\<USER>\.nuget\packages\reactiveui\20.1.1\lib\net8.0\ReactiveUI.dll
C:\Users\<USER>\.nuget\packages\semi.avalonia.datagrid\11.0.7\lib\net6.0\Semi.Avalonia.DataGrid.dll
C:\Users\<USER>\.nuget\packages\semi.avalonia\11.0.7\lib\net6.0\Semi.Avalonia.dll
C:\Users\<USER>\.nuget\packages\serilog\4.2.1-dev-02337\lib\net9.0\Serilog.dll
C:\Users\<USER>\.nuget\packages\serilog.settings.configuration\9.0.1-dev-02317\lib\net9.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.console\6.0.0\lib\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.debug\3.0.0\lib\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.file\6.0.0\lib\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\.nuget\packages\skiasharp\2.88.9\lib\net6.0\SkiaSharp.dll
C:\Users\<USER>\.nuget\packages\skiasharp.harfbuzz\2.88.6\lib\net6.0\SkiaSharp.HarfBuzz.dll
C:\Users\<USER>\.nuget\packages\splat\15.1.1\lib\net8.0\Splat.dll
C:\Users\<USER>\.nuget\packages\sqlitepclraw.bundle_e_sqlite3\2.1.10\lib\netstandard2.0\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\.nuget\packages\sqlitepclraw.core\2.1.10\lib\netstandard2.0\SQLitePCLRaw.core.dll
C:\Users\<USER>\.nuget\packages\sqlitepclraw.provider.e_sqlite3\2.1.10\lib\net6.0\SQLitePCLRaw.provider.e_sqlite3.dll
G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\obj\Debug\net9.0\ref\SuperNova.dll
G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Runtime\obj\Debug\net9.0\ref\SuperNova.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.AppContext.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Buffers.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.Concurrent.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.Immutable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.NonGeneric.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.Specialized.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.Annotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\.nuget\packages\system.configuration.configurationmanager\7.0.0\lib\net7.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Console.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Data.Common.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Data.DataSetExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Data.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Contracts.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Debug.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\.nuget\packages\system.diagnostics.eventlog\7.0.0\lib\net7.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Process.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Tools.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Tracing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.dll
C:\Users\<USER>\.nuget\packages\system.drawing.common\7.0.0\lib\net7.0\System.Drawing.Common.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Drawing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Drawing.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Dynamic.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Formats.Asn1.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Formats.Tar.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Globalization.Calendars.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Globalization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Globalization.Extensions.dll
C:\Users\<USER>\.nuget\packages\system.identitymodel.tokens.jwt\7.4.0\lib\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.Brotli.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.IsolatedStorage.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Pipes.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.Expressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.Queryable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Memory.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Http.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Http.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.HttpListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Mail.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.NameResolution.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.NetworkInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Ping.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Quic.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Requests.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Security.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.ServicePoint.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Sockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebClient.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebSockets.Client.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebSockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Numerics.Vectors.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ObjectModel.dll
C:\Users\<USER>\.nuget\packages\system.reactive\6.0.1\lib\net6.0\System.Reactive.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Emit.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Metadata.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Resources.Reader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Resources.ResourceManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Resources.Writer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Handles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.InteropServices.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Intrinsics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Loader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Claims.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.protecteddata\7.0.0\lib\net7.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.dll
C:\Users\<USER>\.nuget\packages\system.security.permissions\7.0.0\lib\net7.0\System.Security.Permissions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Principal.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Principal.Windows.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.SecureString.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ServiceModel.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ServiceProcess.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.RegularExpressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Channels.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Overlapped.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Thread.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.ThreadPool.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Timer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Transactions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Transactions.Local.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ValueTuple.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Web.HttpUtility.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Windows.dll
C:\Users\<USER>\.nuget\packages\system.windows.extensions\7.0.0\lib\net7.0\System.Windows.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.ReaderWriter.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XmlDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XmlSerializer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XPath.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XPath.XDocument.dll
G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.Core\obj\Debug\net9.0\ref\TicketSalesApp.Core.dll
G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.Services\obj\Debug\net9.0\ref\TicketSalesApp.Services.dll
C:\Users\<USER>\.nuget\packages\tmds.dbus.protocol\0.20.0\lib\net8.0\Tmds.DBus.Protocol.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\WindowsBase.dll
