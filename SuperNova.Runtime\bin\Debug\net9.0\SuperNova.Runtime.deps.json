{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"SuperNova.Runtime/1.0.0": {"dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Antlr4BuildTasks": "12.8.0", "Avalonia": "11.2.3", "Avalonia.Diagnostics": "11.2.3", "Classic.Avalonia.Theme": "11.2.0", "LanguageExt.Core": "4.4.9", "PropertyChanged.SourceGenerator": "1.0.8"}, "runtime": {"SuperNova.Runtime.dll": {}}}, "Antlr4.Runtime.Standard/4.13.1": {"runtime": {"lib/netstandard2.0/Antlr4.Runtime.Standard.dll": {"assemblyVersion": "4.13.1.0", "fileVersion": "4.13.1.0"}}}, "Antlr4BuildTasks/12.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Utilities.Core": "17.8.3"}, "runtime": {"lib/netstandard2.0/Antlr4BuildTasks.dll": {"assemblyVersion": "12.8.0.0", "fileVersion": "12.8.0.0"}}}, "Avalonia/11.2.3": {"dependencies": {"Avalonia.BuildServices": "0.0.29", "Avalonia.Remote.Protocol": "11.2.3", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Controls.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "0.7.0.0", "fileVersion": "0.7.0.0"}, "lib/net8.0/Avalonia.Dialogs.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Markup.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Metal.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.MicroCom.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.OpenGL.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Vulkan.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.BuildServices/0.0.29": {}, "Avalonia.Controls.ColorPicker/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Remote.Protocol": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Controls.DataGrid/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Remote.Protocol": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Controls.DataGrid.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Diagnostics/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.ColorPicker": "11.2.3", "Avalonia.Controls.DataGrid": "11.2.3", "Avalonia.Themes.Simple": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Remote.Protocol/11.2.3": {"runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Skia/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "HarfBuzzSharp": "7.3.0.2", "HarfBuzzSharp.NativeAssets.Linux": "7.3.0.2", "HarfBuzzSharp.NativeAssets.WebAssembly": "7.3.0.3-preview.2.2", "SkiaSharp": "2.88.8", "SkiaSharp.NativeAssets.Linux": "2.88.8", "SkiaSharp.NativeAssets.WebAssembly": "2.88.8"}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"assemblyVersion": "11.2.0.0", "fileVersion": "11.2.0.0"}}}, "Avalonia.Themes.Simple/11.2.3": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Classic.Avalonia.Theme/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Classic.CommonControls.Avalonia": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.Avalonia.Theme.dll": {"assemblyVersion": "11.2.0.0", "fileVersion": "11.2.0.0"}}}, "Classic.CommonControls.Avalonia/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Skia": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.CommonControls.Avalonia.dll": {"assemblyVersion": "11.2.0.0", "fileVersion": "11.2.0.0"}}}, "HarfBuzzSharp/7.3.0.2": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.2", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.2"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "7.3.0.2"}}}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.2": {"dependencies": {"HarfBuzzSharp": "7.3.0.2"}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0.3-preview.2.2": {}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "LanguageExt.Core/4.4.9": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0"}, "runtime": {"lib/netstandard2.0/LanguageExt.Core.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "0.11.0.0", "fileVersion": "0.11.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {"runtime": {"lib/net8.0/Microsoft.Build.Framework.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.8.3.51904"}}}, "Microsoft.Build.Utilities.Core/17.8.3": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.NET.StringTools": "17.8.3", "Microsoft.VisualStudio.Setup.Configuration.Interop": "3.2.2146", "System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.Build.Utilities.Core.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.8.3.51904"}}}, "Microsoft.NET.StringTools/17.8.3": {"runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "17.8.3.51904"}}}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"runtime": {"lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "3.2.2146.50370"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "PropertyChanged.SourceGenerator/1.0.8": {}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"dependencies": {"SkiaSharp": "2.88.8"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.EventLog/7.0.0": {"runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}}}, "libraries": {"SuperNova.Runtime/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Antlr4.Runtime.Standard/4.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-Da5+i4kFHUseJRZGcBG5fmZGpA/Ns180ibrQMxgZzjpQOnENVvSL5gi5HZ8Ncz8/AR2WsKbOg2lMBzjz0HUQcA==", "path": "antlr4.runtime.standard/4.13.1", "hashPath": "antlr4.runtime.standard.4.13.1.nupkg.sha512"}, "Antlr4BuildTasks/12.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZIcmPs7ykc6WDtuHSBvV5wZ1BkuR6DHbZnCrUWVAx3F5Lg4LcxHlzQEE0hB7NdyXhOoB3ykcyQ7EY88EzSflVQ==", "path": "antlr4buildtasks/12.8.0", "hashPath": "antlr4buildtasks.12.8.0.nupkg.sha512"}, "Avalonia/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-pD6woFAUfGcyEvMmrpctntU4jv4fT8752pfx1J5iRORVX3Ob0oQi8PWo0TXVaAJZiSfH0cdKTeKx0w0DzD0/mg==", "path": "avalonia/11.2.3", "hashPath": "avalonia.11.2.3.nupkg.sha512"}, "Avalonia.BuildServices/0.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-U4eJLQdoDNHXtEba7MZUCwrBErBTxFp6sUewXBOdAhU0Kwzwaa/EKFcYm8kpcysjzKtfB4S0S9n0uxKZFz/ikw==", "path": "avalonia.buildservices/0.0.29", "hashPath": "avalonia.buildservices.0.0.29.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-JpK84GN+CkS5H0L/mDyYa4aMAJw93dukedKK1xC4g/J5NmwQ23qFPQNqZlTs+6f9nULwJPL+jiffJpSyGMt0gg==", "path": "avalonia.controls.colorpicker/11.2.3", "hashPath": "avalonia.controls.colorpicker.11.2.3.nupkg.sha512"}, "Avalonia.Controls.DataGrid/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Ul6oWEoqs4eTQZIb4Hzf0+ajhgrCX9ypOj8TahKbkKoIznJkUoka3iV90Vpj/AuoT5AZsN6f+1+62SVPpeMApA==", "path": "avalonia.controls.datagrid/11.2.3", "hashPath": "avalonia.controls.datagrid.11.2.3.nupkg.sha512"}, "Avalonia.Diagnostics/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Qld0UvkSLfIuZD7/gS8RIO6ww3jP+xJvsMyqU8evdphPoic7h6LAeY/ppT9NtI0r1KUDT2BpFcVDqPyQH6eSiw==", "path": "avalonia.diagnostics/11.2.3", "hashPath": "avalonia.diagnostics.11.2.3.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-6V0aNtld48WmO8tAlWwlRlUmXYcOWv+1eJUSl1ETF+1blUe5yhcSmuWarPprO0hDk8Ta6wGfdfcrnVl2gITYcA==", "path": "avalonia.remote.protocol/11.2.3", "hashPath": "avalonia.remote.protocol.11.2.3.nupkg.sha512"}, "Avalonia.Skia/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-UZEwEgEV8XzkzT0XxoLx3HglqeS1J6ieaui5Kcvtni4va6XJUhUDDwaCv6FagrUU9hF0y/VkbYbVMylEN8z2Gg==", "path": "avalonia.skia/11.2.0", "hashPath": "avalonia.skia.11.2.0.nupkg.sha512"}, "Avalonia.Themes.Simple/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Okio7RYUHUk1m/1n6VGrkoq0C3Y3J6RwtrTGEfXPkYMJsL6yPqstJZMYkMFQPISUr8TYUNVKv82hL1qLRw7hwA==", "path": "avalonia.themes.simple/11.2.3", "hashPath": "avalonia.themes.simple.11.2.3.nupkg.sha512"}, "Classic.Avalonia.Theme/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-NQLDiq6YWRZpeSgjFtNrYPq0xbRQaWp3tjyqPKrvN+QNfvQ3D6hrMmtQItKNhi3Vmqk1jLYej2cVA6UdzWI/5Q==", "path": "classic.avalonia.theme/11.2.0", "hashPath": "classic.avalonia.theme.11.2.0.nupkg.sha512"}, "Classic.CommonControls.Avalonia/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-v2c6tbe0PQoLk4mwt4y/ggEMxfkGBni1qhEDJbumQ1v1J3/0D3RQEFHbFk03l+BVcyPRcDcJZc25DVnK/XE7Mw==", "path": "classic.commoncontrols.avalonia/11.2.0", "hashPath": "classic.commoncontrols.avalonia.11.2.0.nupkg.sha512"}, "HarfBuzzSharp/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-0tCd6HyCmNsX/DniCp2b00fo0xPbdNwKOs9BxxyT8oOOuMlWjcSFwzONKyeckCKVBFEsbSmsAHPDTqxoSDwZMg==", "path": "harfbuzzsharp/7.3.0.2", "hashPath": "harfbuzzsharp.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aKa5J1RqjXKAtdcZJp5wjC78klfBIzJHM6CneN76lFmQ9LLRJA9Oa0TkIDaV8lVLDKMAy5fCKHXFlXUK1YfL/g==", "path": "harfbuzzsharp.nativeassets.linux/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.linux.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nycYH/WLJ6ogm+I+QSFCdPJsdxSb5GANWYbQyp1vsd/KjXN56RVUJWPhbgP2GKb/Y7mrsHM7EProqVXlO/EMsA==", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.macos.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0.3-preview.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Dc+dolrhmkpqwT25NfNEEgceW0//KRR2WIOvxlyIIHIIMBCn0FfUeJX5RhFll8kyaZwF8tuKsxRJtQG/rzSBog==", "path": "harfbuzzsharp.nativeassets.webassembly/7.3.0.3-preview.2.2", "hashPath": "harfbuzzsharp.nativeassets.webassembly.7.3.0.3-preview.2.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-DpF9JBzwws2dupOLnjME65hxQWWbN/GD40AoTkwB4S05WANvxo3n81AnQJKxWDCnrWfWhLPB36OF27TvEqzb/A==", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.win32.7.3.0.2.nupkg.sha512"}, "LanguageExt.Core/4.4.9": {"type": "package", "serviceable": true, "sha512": "sha512-K9VGWkThJkaomifa3zcmwysw1BaSqIZZPZc6trBnJN8u9mpmA/cMMwCWEa/v7bPv/+NnG6PbyIDB7HtxBX7yCQ==", "path": "languageext.core/4.4.9", "hashPath": "languageext.core.4.4.9.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Utilities.Core/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-ex8Sjx02q0FmForLRFItB82sJx5s2JRWIpJgYDW1g7xLUoFlKLoNp67UwS5xN8YcYBkT7vRxXeYx/dQ96KaWtg==", "path": "microsoft.build.utilities.core/17.8.3", "hashPath": "microsoft.build.utilities.core.17.8.3.nupkg.sha512"}, "Microsoft.NET.StringTools/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-y6DiuacjlIfXH3XVQG5htf+4oheinZAo7sHbITB3z7yCXQec48f9ZhGSXkr+xn1bfl73Yc3ZQEW2peJ5X68AvQ==", "path": "microsoft.net.stringtools/17.8.3", "hashPath": "microsoft.net.stringtools.17.8.3.nupkg.sha512"}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"type": "package", "serviceable": true, "sha512": "sha512-gMq8uGy8zTIp0kQGTI45buZC3JOStGJyjGD8gksskk83aQISW65IESErLE/WDT7Bdy+QWbdUi7QyO1LEzUSOFA==", "path": "microsoft.visualstudio.setup.configuration.interop/3.2.2146", "hashPath": "microsoft.visualstudio.setup.configuration.interop.3.2.2146.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "PropertyChanged.SourceGenerator/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-XufLv9ReskTWjx3assV8aRAfcHfAATAkR/MugYSHkyYm7d2voQJHtQKSncVMNZiVGwXaEg5aGtWgKMweR11TAg==", "path": "propertychanged.sourcegenerator/1.0.8", "hashPath": "propertychanged.sourcegenerator.1.0.8.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-0FO6YA7paNFBMJULvEyecPmCvL9/STvOAi5VOUw2srqJ7pNTbiiZkfl7sulAzcumbWgfzaVjRXYTgMj7SoUnWQ==", "path": "skiasharp.nativeassets.linux/2.88.8", "hashPath": "skiasharp.nativeassets.linux.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-S3qRo8c+gVYOyfrdf6FYnjx/ft+gPkb4dNY2IPv5Oy5yNBhDhXhKqHFr9h4+ne6ZU+7D4dbuRQqsIqCo8u1/DA==", "path": "skiasharp.nativeassets.webassembly/2.88.8", "hashPath": "skiasharp.nativeassets.webassembly.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "path": "system.security.cryptography.protecteddata/7.0.0", "hashPath": "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}}}