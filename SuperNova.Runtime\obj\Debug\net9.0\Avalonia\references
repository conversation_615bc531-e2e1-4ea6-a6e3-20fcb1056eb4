C:\Users\<USER>\.nuget\packages\antlr4.runtime.standard\4.13.1\lib\netstandard2.0\Antlr4.Runtime.Standard.dll
C:\Users\<USER>\.nuget\packages\antlr4buildtasks\12.8.0\lib\netstandard2.0\Antlr4BuildTasks.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Base.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.colorpicker\11.2.3\lib\net8.0\Avalonia.Controls.ColorPicker.dll
C:\Users\<USER>\.nuget\packages\avalonia.controls.datagrid\11.2.3\lib\net8.0\Avalonia.Controls.DataGrid.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Controls.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.DesignerSupport.dll
C:\Users\<USER>\.nuget\packages\avalonia.diagnostics\11.2.3\lib\net8.0\Avalonia.Diagnostics.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Dialogs.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Markup.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Markup.Xaml.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Metal.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.MicroCom.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.OpenGL.dll
C:\Users\<USER>\.nuget\packages\avalonia.remote.protocol\11.2.3\lib\net8.0\Avalonia.Remote.Protocol.dll
C:\Users\<USER>\.nuget\packages\avalonia.skia\11.2.0\lib\net8.0\Avalonia.Skia.dll
C:\Users\<USER>\.nuget\packages\avalonia.themes.simple\11.2.3\lib\net8.0\Avalonia.Themes.Simple.dll
C:\Users\<USER>\.nuget\packages\avalonia\11.2.3\ref\net8.0\Avalonia.Vulkan.dll
C:\Users\<USER>\.nuget\packages\classic.avalonia.theme\11.2.0\lib\netstandard2.0\Classic.Avalonia.Theme.dll
C:\Users\<USER>\.nuget\packages\classic.commoncontrols.avalonia\11.2.0\lib\netstandard2.0\Classic.CommonControls.Avalonia.dll
C:\Users\<USER>\.nuget\packages\harfbuzzsharp\7.3.0.2\lib\net6.0\HarfBuzzSharp.dll
C:\Users\<USER>\.nuget\packages\languageext.core\4.4.9\lib\netstandard2.0\LanguageExt.Core.dll
C:\Users\<USER>\.nuget\packages\microcom.runtime\0.11.0\lib\net5.0\MicroCom.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.bcl.asyncinterfaces\7.0.0\lib\netstandard2.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\.nuget\packages\microsoft.build.framework\17.8.3\ref\net8.0\Microsoft.Build.Framework.dll
C:\Users\<USER>\.nuget\packages\microsoft.build.utilities.core\17.8.3\ref\net8.0\Microsoft.Build.Utilities.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.CSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.net.stringtools\17.8.3\ref\net8.0\Microsoft.NET.StringTools.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.VisualBasic.dll
C:\Users\<USER>\.nuget\packages\microsoft.visualstudio.setup.configuration.interop\3.2.2146\lib\netstandard2.1\Microsoft.VisualStudio.Setup.Configuration.Interop.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\Microsoft.Win32.Registry.dll
C:\Users\<USER>\.nuget\packages\microsoft.win32.systemevents\7.0.0\lib\net7.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\mscorlib.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\netstandard.dll
C:\Users\<USER>\.nuget\packages\skiasharp\2.88.8\lib\net6.0\SkiaSharp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.AppContext.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Buffers.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.Concurrent.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.Immutable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.NonGeneric.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Collections.Specialized.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.Annotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\.nuget\packages\system.configuration.configurationmanager\7.0.0\lib\net7.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Console.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Core.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Data.Common.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Data.DataSetExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Data.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Contracts.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Debug.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\.nuget\packages\system.diagnostics.eventlog\7.0.0\lib\net7.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Process.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Tools.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Diagnostics.Tracing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.dll
C:\Users\<USER>\.nuget\packages\system.drawing.common\7.0.0\lib\net7.0\System.Drawing.Common.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Drawing.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Drawing.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Dynamic.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Formats.Asn1.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Formats.Tar.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Globalization.Calendars.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Globalization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Globalization.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.Brotli.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.IsolatedStorage.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.Pipes.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.Expressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Linq.Queryable.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Memory.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Http.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Http.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.HttpListener.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Mail.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.NameResolution.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.NetworkInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Ping.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Quic.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Requests.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Security.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.ServicePoint.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.Sockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebClient.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebSockets.Client.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Net.WebSockets.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Numerics.Vectors.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ObjectModel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Emit.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Metadata.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Resources.Reader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Resources.ResourceManager.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Resources.Writer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Handles.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.InteropServices.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Intrinsics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Loader.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Numerics.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.AccessControl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Claims.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\.nuget\packages\system.security.cryptography.protecteddata\7.0.0\lib\net7.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.dll
C:\Users\<USER>\.nuget\packages\system.security.permissions\7.0.0\lib\net7.0\System.Security.Permissions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Principal.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.Principal.Windows.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Security.SecureString.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ServiceModel.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ServiceProcess.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encoding.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Text.RegularExpressions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Channels.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Overlapped.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Thread.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.ThreadPool.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Threading.Timer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Transactions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Transactions.Local.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.ValueTuple.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Web.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Web.HttpUtility.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Windows.dll
C:\Users\<USER>\.nuget\packages\system.windows.extensions\7.0.0\lib\net7.0\System.Windows.Extensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.Linq.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.ReaderWriter.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.Serialization.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XmlDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XmlSerializer.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XPath.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\.nuget\packages\microsoft.netcore.app.ref\9.0.1\ref\net9.0\WindowsBase.dll
