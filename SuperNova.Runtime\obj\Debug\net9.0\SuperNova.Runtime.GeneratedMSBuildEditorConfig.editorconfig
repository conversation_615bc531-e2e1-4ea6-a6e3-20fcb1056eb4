is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.TargetFramework = net9.0
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = SuperNova.Runtime
build_property.ProjectDir = G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova.Runtime\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/BuiltinControls/Resources.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/BuiltinControls/VBLabel.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/BuiltinControls/VBTimer.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
