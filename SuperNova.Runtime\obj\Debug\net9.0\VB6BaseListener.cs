//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/Interpreter/Grammar/VB6.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419


using Antlr4.Runtime.Misc;
using IErrorNode = Antlr4.Runtime.Tree.IErrorNode;
using ITerminalNode = Antlr4.Runtime.Tree.ITerminalNode;
using IToken = Antlr4.Runtime.IToken;
using ParserRuleContext = Antlr4.Runtime.ParserRuleContext;

/// <summary>
/// This class provides an empty implementation of <see cref="IVB6Listener"/>,
/// which can be extended to create a listener which only needs to handle a subset
/// of the available methods.
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.Diagnostics.DebuggerNonUserCode]
[System.CLSCompliant(false)]
public partial class VB6BaseListener : IVB6Listener {
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.startRule"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterStartRule([NotNull] VB6Parser.StartRuleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.startRule"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitStartRule([NotNull] VB6Parser.StartRuleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.module"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModule([NotNull] VB6Parser.ModuleContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.module"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModule([NotNull] VB6Parser.ModuleContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReferences"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferences([NotNull] VB6Parser.ModuleReferencesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReferences"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferences([NotNull] VB6Parser.ModuleReferencesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReference"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReference([NotNull] VB6Parser.ModuleReferenceContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReference"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReference([NotNull] VB6Parser.ModuleReferenceContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReferenceValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferenceValue([NotNull] VB6Parser.ModuleReferenceValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReferenceValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferenceValue([NotNull] VB6Parser.ModuleReferenceValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReferenceComponent"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleReferenceComponent([NotNull] VB6Parser.ModuleReferenceComponentContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReferenceComponent"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleReferenceComponent([NotNull] VB6Parser.ModuleReferenceComponentContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleHeader"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleHeader([NotNull] VB6Parser.ModuleHeaderContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleHeader"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleHeader([NotNull] VB6Parser.ModuleHeaderContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleConfig"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleConfig([NotNull] VB6Parser.ModuleConfigContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleConfig"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleConfig([NotNull] VB6Parser.ModuleConfigContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleConfigElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleConfigElement([NotNull] VB6Parser.ModuleConfigElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleConfigElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleConfigElement([NotNull] VB6Parser.ModuleConfigElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleAttributes"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleAttributes([NotNull] VB6Parser.ModuleAttributesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleAttributes"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleAttributes([NotNull] VB6Parser.ModuleAttributesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleOptions"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleOptions([NotNull] VB6Parser.ModuleOptionsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleOptions"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleOptions([NotNull] VB6Parser.ModuleOptionsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionBaseStmt([NotNull] VB6Parser.OptionBaseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionBaseStmt([NotNull] VB6Parser.OptionBaseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionCompareStmt([NotNull] VB6Parser.OptionCompareStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionCompareStmt([NotNull] VB6Parser.OptionCompareStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionExplicitStmt([NotNull] VB6Parser.OptionExplicitStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionExplicitStmt([NotNull] VB6Parser.OptionExplicitStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOptionPrivateModuleStmt([NotNull] VB6Parser.OptionPrivateModuleStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOptionPrivateModuleStmt([NotNull] VB6Parser.OptionPrivateModuleStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleBody([NotNull] VB6Parser.ModuleBodyContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleBody"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleBody([NotNull] VB6Parser.ModuleBodyContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleBodyElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleBodyElement([NotNull] VB6Parser.ModuleBodyElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleBodyElement"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleBodyElement([NotNull] VB6Parser.ModuleBodyElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.controlProperties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterControlProperties([NotNull] VB6Parser.ControlPropertiesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.controlProperties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitControlProperties([NotNull] VB6Parser.ControlPropertiesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_Properties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_Properties([NotNull] VB6Parser.Cp_PropertiesContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_Properties"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_Properties([NotNull] VB6Parser.Cp_PropertiesContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_SingleProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_SingleProperty([NotNull] VB6Parser.Cp_SinglePropertyContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_SingleProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_SingleProperty([NotNull] VB6Parser.Cp_SinglePropertyContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_PropertyName"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_PropertyName([NotNull] VB6Parser.Cp_PropertyNameContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_PropertyName"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_PropertyName([NotNull] VB6Parser.Cp_PropertyNameContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_PropertyValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_PropertyValue([NotNull] VB6Parser.Cp_PropertyValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_PropertyValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_PropertyValue([NotNull] VB6Parser.Cp_PropertyValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_NestedProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_NestedProperty([NotNull] VB6Parser.Cp_NestedPropertyContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_NestedProperty"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_NestedProperty([NotNull] VB6Parser.Cp_NestedPropertyContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_ControlType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_ControlType([NotNull] VB6Parser.Cp_ControlTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_ControlType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_ControlType([NotNull] VB6Parser.Cp_ControlTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_ControlIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCp_ControlIdentifier([NotNull] VB6Parser.Cp_ControlIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_ControlIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCp_ControlIdentifier([NotNull] VB6Parser.Cp_ControlIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterModuleBlock([NotNull] VB6Parser.ModuleBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitModuleBlock([NotNull] VB6Parser.ModuleBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.attributeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAttributeStmt([NotNull] VB6Parser.AttributeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.attributeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAttributeStmt([NotNull] VB6Parser.AttributeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.block"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlock([NotNull] VB6Parser.BlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.block"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlock([NotNull] VB6Parser.BlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.blockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlockStmt([NotNull] VB6Parser.BlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.blockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlockStmt([NotNull] VB6Parser.BlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.appActivateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAppActivateStmt([NotNull] VB6Parser.AppActivateStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.appActivateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAppActivateStmt([NotNull] VB6Parser.AppActivateStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.beepStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBeepStmt([NotNull] VB6Parser.BeepStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.beepStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBeepStmt([NotNull] VB6Parser.BeepStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.chDirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterChDirStmt([NotNull] VB6Parser.ChDirStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.chDirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitChDirStmt([NotNull] VB6Parser.ChDirStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.chDriveStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterChDriveStmt([NotNull] VB6Parser.ChDriveStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.chDriveStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitChDriveStmt([NotNull] VB6Parser.ChDriveStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.closeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCloseStmt([NotNull] VB6Parser.CloseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.closeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCloseStmt([NotNull] VB6Parser.CloseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.constStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterConstStmt([NotNull] VB6Parser.ConstStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.constStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitConstStmt([NotNull] VB6Parser.ConstStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.constSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterConstSubStmt([NotNull] VB6Parser.ConstSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.constSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitConstSubStmt([NotNull] VB6Parser.ConstSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.dateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDateStmt([NotNull] VB6Parser.DateStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.dateStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDateStmt([NotNull] VB6Parser.DateStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.declareStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDeclareStmt([NotNull] VB6Parser.DeclareStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.declareStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDeclareStmt([NotNull] VB6Parser.DeclareStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.deftypeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDeftypeStmt([NotNull] VB6Parser.DeftypeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.deftypeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDeftypeStmt([NotNull] VB6Parser.DeftypeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.deleteSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDeleteSettingStmt([NotNull] VB6Parser.DeleteSettingStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.deleteSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDeleteSettingStmt([NotNull] VB6Parser.DeleteSettingStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>doBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDoBlockLoop([NotNull] VB6Parser.DoBlockLoopContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>doBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDoBlockLoop([NotNull] VB6Parser.DoBlockLoopContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>doWhileBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDoWhileBlockLoop([NotNull] VB6Parser.DoWhileBlockLoopContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>doWhileBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDoWhileBlockLoop([NotNull] VB6Parser.DoWhileBlockLoopContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>doBlockWhileLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDoBlockWhileLoop([NotNull] VB6Parser.DoBlockWhileLoopContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>doBlockWhileLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDoBlockWhileLoop([NotNull] VB6Parser.DoBlockWhileLoopContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.endStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEndStmt([NotNull] VB6Parser.EndStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.endStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEndStmt([NotNull] VB6Parser.EndStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.enumerationStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEnumerationStmt([NotNull] VB6Parser.EnumerationStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.enumerationStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEnumerationStmt([NotNull] VB6Parser.EnumerationStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.enumerationStmt_Constant"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEnumerationStmt_Constant([NotNull] VB6Parser.EnumerationStmt_ConstantContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.enumerationStmt_Constant"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEnumerationStmt_Constant([NotNull] VB6Parser.EnumerationStmt_ConstantContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eraseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEraseStmt([NotNull] VB6Parser.EraseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eraseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEraseStmt([NotNull] VB6Parser.EraseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.errorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterErrorStmt([NotNull] VB6Parser.ErrorStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.errorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitErrorStmt([NotNull] VB6Parser.ErrorStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterEventStmt([NotNull] VB6Parser.EventStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitEventStmt([NotNull] VB6Parser.EventStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.exitStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterExitStmt([NotNull] VB6Parser.ExitStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.exitStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitExitStmt([NotNull] VB6Parser.ExitStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.continueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterContinueStmt([NotNull] VB6Parser.ContinueStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.continueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitContinueStmt([NotNull] VB6Parser.ContinueStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.filecopyStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterFilecopyStmt([NotNull] VB6Parser.FilecopyStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.filecopyStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitFilecopyStmt([NotNull] VB6Parser.FilecopyStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.forEachStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterForEachStmt([NotNull] VB6Parser.ForEachStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.forEachStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitForEachStmt([NotNull] VB6Parser.ForEachStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.forNextStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterForNextStmt([NotNull] VB6Parser.ForNextStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.forNextStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitForNextStmt([NotNull] VB6Parser.ForNextStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.functionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterFunctionStmt([NotNull] VB6Parser.FunctionStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.functionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitFunctionStmt([NotNull] VB6Parser.FunctionStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.getStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterGetStmt([NotNull] VB6Parser.GetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.getStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitGetStmt([NotNull] VB6Parser.GetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.goSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterGoSubStmt([NotNull] VB6Parser.GoSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.goSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitGoSubStmt([NotNull] VB6Parser.GoSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.goToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterGoToStmt([NotNull] VB6Parser.GoToStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.goToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitGoToStmt([NotNull] VB6Parser.GoToStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInlineIfThenElse([NotNull] VB6Parser.InlineIfThenElseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInlineIfThenElse([NotNull] VB6Parser.InlineIfThenElseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBlockIfThenElse([NotNull] VB6Parser.BlockIfThenElseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBlockIfThenElse([NotNull] VB6Parser.BlockIfThenElseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfBlockStmt([NotNull] VB6Parser.IfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfBlockStmt([NotNull] VB6Parser.IfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifConditionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfConditionStmt([NotNull] VB6Parser.IfConditionStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifConditionStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfConditionStmt([NotNull] VB6Parser.IfConditionStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfElseIfBlockStmt([NotNull] VB6Parser.IfElseIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfElseIfBlockStmt([NotNull] VB6Parser.IfElseIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterIfElseBlockStmt([NotNull] VB6Parser.IfElseBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitIfElseBlockStmt([NotNull] VB6Parser.IfElseBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.implementsStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterImplementsStmt([NotNull] VB6Parser.ImplementsStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.implementsStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitImplementsStmt([NotNull] VB6Parser.ImplementsStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.inputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterInputStmt([NotNull] VB6Parser.InputStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.inputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitInputStmt([NotNull] VB6Parser.InputStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.killStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterKillStmt([NotNull] VB6Parser.KillStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.killStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitKillStmt([NotNull] VB6Parser.KillStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.letStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLetStmt([NotNull] VB6Parser.LetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.letStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLetStmt([NotNull] VB6Parser.LetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lineInputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLineInputStmt([NotNull] VB6Parser.LineInputStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lineInputStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLineInputStmt([NotNull] VB6Parser.LineInputStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.loadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLoadStmt([NotNull] VB6Parser.LoadStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.loadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLoadStmt([NotNull] VB6Parser.LoadStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLockStmt([NotNull] VB6Parser.LockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLockStmt([NotNull] VB6Parser.LockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLsetStmt([NotNull] VB6Parser.LsetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLsetStmt([NotNull] VB6Parser.LsetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfThenElseStmt([NotNull] VB6Parser.MacroIfThenElseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroIfThenElseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfThenElseStmt([NotNull] VB6Parser.MacroIfThenElseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroIfBlockStmt([NotNull] VB6Parser.MacroIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroIfBlockStmt([NotNull] VB6Parser.MacroIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroElseIfBlockStmt([NotNull] VB6Parser.MacroElseIfBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroElseIfBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroElseIfBlockStmt([NotNull] VB6Parser.MacroElseIfBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMacroElseBlockStmt([NotNull] VB6Parser.MacroElseBlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroElseBlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMacroElseBlockStmt([NotNull] VB6Parser.MacroElseBlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.midStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMidStmt([NotNull] VB6Parser.MidStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.midStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMidStmt([NotNull] VB6Parser.MidStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.mkdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterMkdirStmt([NotNull] VB6Parser.MkdirStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.mkdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitMkdirStmt([NotNull] VB6Parser.MkdirStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.nameStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterNameStmt([NotNull] VB6Parser.NameStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.nameStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitNameStmt([NotNull] VB6Parser.NameStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.onErrorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOnErrorStmt([NotNull] VB6Parser.OnErrorStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.onErrorStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOnErrorStmt([NotNull] VB6Parser.OnErrorStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.onGoToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOnGoToStmt([NotNull] VB6Parser.OnGoToStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.onGoToStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOnGoToStmt([NotNull] VB6Parser.OnGoToStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.onGoSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOnGoSubStmt([NotNull] VB6Parser.OnGoSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.onGoSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOnGoSubStmt([NotNull] VB6Parser.OnGoSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.openStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOpenStmt([NotNull] VB6Parser.OpenStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.openStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOpenStmt([NotNull] VB6Parser.OpenStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.outputList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOutputList([NotNull] VB6Parser.OutputListContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.outputList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOutputList([NotNull] VB6Parser.OutputListContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.outputList_Expression"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterOutputList_Expression([NotNull] VB6Parser.OutputList_ExpressionContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.outputList_Expression"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitOutputList_Expression([NotNull] VB6Parser.OutputList_ExpressionContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.printStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPrintStmt([NotNull] VB6Parser.PrintStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.printStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPrintStmt([NotNull] VB6Parser.PrintStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.propertyGetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPropertyGetStmt([NotNull] VB6Parser.PropertyGetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.propertyGetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPropertyGetStmt([NotNull] VB6Parser.PropertyGetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.propertySetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPropertySetStmt([NotNull] VB6Parser.PropertySetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.propertySetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPropertySetStmt([NotNull] VB6Parser.PropertySetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.propertyLetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPropertyLetStmt([NotNull] VB6Parser.PropertyLetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.propertyLetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPropertyLetStmt([NotNull] VB6Parser.PropertyLetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.putStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPutStmt([NotNull] VB6Parser.PutStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.putStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPutStmt([NotNull] VB6Parser.PutStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.raiseEventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRaiseEventStmt([NotNull] VB6Parser.RaiseEventStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.raiseEventStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRaiseEventStmt([NotNull] VB6Parser.RaiseEventStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.randomizeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRandomizeStmt([NotNull] VB6Parser.RandomizeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.randomizeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRandomizeStmt([NotNull] VB6Parser.RandomizeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.redimStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRedimStmt([NotNull] VB6Parser.RedimStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.redimStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRedimStmt([NotNull] VB6Parser.RedimStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.redimSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRedimSubStmt([NotNull] VB6Parser.RedimSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.redimSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRedimSubStmt([NotNull] VB6Parser.RedimSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.resetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterResetStmt([NotNull] VB6Parser.ResetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.resetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitResetStmt([NotNull] VB6Parser.ResetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.resumeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterResumeStmt([NotNull] VB6Parser.ResumeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.resumeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitResumeStmt([NotNull] VB6Parser.ResumeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.returnStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterReturnStmt([NotNull] VB6Parser.ReturnStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.returnStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitReturnStmt([NotNull] VB6Parser.ReturnStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.rmdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRmdirStmt([NotNull] VB6Parser.RmdirStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.rmdirStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRmdirStmt([NotNull] VB6Parser.RmdirStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.rsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterRsetStmt([NotNull] VB6Parser.RsetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.rsetStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitRsetStmt([NotNull] VB6Parser.RsetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.savepictureStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSavepictureStmt([NotNull] VB6Parser.SavepictureStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.savepictureStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSavepictureStmt([NotNull] VB6Parser.SavepictureStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.saveSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSaveSettingStmt([NotNull] VB6Parser.SaveSettingStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.saveSettingStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSaveSettingStmt([NotNull] VB6Parser.SaveSettingStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.seekStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSeekStmt([NotNull] VB6Parser.SeekStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.seekStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSeekStmt([NotNull] VB6Parser.SeekStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.selectCaseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSelectCaseStmt([NotNull] VB6Parser.SelectCaseStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.selectCaseStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSelectCaseStmt([NotNull] VB6Parser.SelectCaseStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.sC_Case"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSC_Case([NotNull] VB6Parser.SC_CaseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.sC_Case"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSC_Case([NotNull] VB6Parser.SC_CaseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondElse([NotNull] VB6Parser.CaseCondElseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondElse([NotNull] VB6Parser.CaseCondElseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExpr([NotNull] VB6Parser.CaseCondExprContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExpr([NotNull] VB6Parser.CaseCondExprContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExprIs([NotNull] VB6Parser.CaseCondExprIsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExprIs([NotNull] VB6Parser.CaseCondExprIsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExprValue([NotNull] VB6Parser.CaseCondExprValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExprValue([NotNull] VB6Parser.CaseCondExprValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCaseCondExprTo([NotNull] VB6Parser.CaseCondExprToContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCaseCondExprTo([NotNull] VB6Parser.CaseCondExprToContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.sendkeysStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSendkeysStmt([NotNull] VB6Parser.SendkeysStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.sendkeysStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSendkeysStmt([NotNull] VB6Parser.SendkeysStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.setattrStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSetattrStmt([NotNull] VB6Parser.SetattrStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.setattrStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSetattrStmt([NotNull] VB6Parser.SetattrStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.setStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSetStmt([NotNull] VB6Parser.SetStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.setStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSetStmt([NotNull] VB6Parser.SetStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.stopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterStopStmt([NotNull] VB6Parser.StopStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.stopStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitStopStmt([NotNull] VB6Parser.StopStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.subStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSubStmt([NotNull] VB6Parser.SubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.subStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSubStmt([NotNull] VB6Parser.SubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.timeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTimeStmt([NotNull] VB6Parser.TimeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.timeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTimeStmt([NotNull] VB6Parser.TimeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeStmt([NotNull] VB6Parser.TypeStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeStmt([NotNull] VB6Parser.TypeStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeStmt_Element"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeStmt_Element([NotNull] VB6Parser.TypeStmt_ElementContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeStmt_Element"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeStmt_Element([NotNull] VB6Parser.TypeStmt_ElementContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeOfStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeOfStmt([NotNull] VB6Parser.TypeOfStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeOfStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeOfStmt([NotNull] VB6Parser.TypeOfStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.unloadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterUnloadStmt([NotNull] VB6Parser.UnloadStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.unloadStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitUnloadStmt([NotNull] VB6Parser.UnloadStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.unlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterUnlockStmt([NotNull] VB6Parser.UnlockStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.unlockStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitUnlockStmt([NotNull] VB6Parser.UnlockStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsStruct([NotNull] VB6Parser.VsStructContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsStruct([NotNull] VB6Parser.VsStructContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAdd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAdd([NotNull] VB6Parser.VsAddContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAdd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAdd([NotNull] VB6Parser.VsAddContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsLt([NotNull] VB6Parser.VsLtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsLt([NotNull] VB6Parser.VsLtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAddressOf([NotNull] VB6Parser.VsAddressOfContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAddressOf([NotNull] VB6Parser.VsAddressOfContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsNew([NotNull] VB6Parser.VsNewContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsNew([NotNull] VB6Parser.VsNewContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMult</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsMult([NotNull] VB6Parser.VsMultContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMult</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsMult([NotNull] VB6Parser.VsMultContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNegation</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsNegation([NotNull] VB6Parser.VsNegationContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNegation</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsNegation([NotNull] VB6Parser.VsNegationContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAssign([NotNull] VB6Parser.VsAssignContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAssign([NotNull] VB6Parser.VsAssignContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsDiv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsDiv([NotNull] VB6Parser.VsDivContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsDiv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsDiv([NotNull] VB6Parser.VsDivContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLike</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsLike([NotNull] VB6Parser.VsLikeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLike</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsLike([NotNull] VB6Parser.VsLikeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsPlus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsPlus([NotNull] VB6Parser.VsPlusContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsPlus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsPlus([NotNull] VB6Parser.VsPlusContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNot</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsNot([NotNull] VB6Parser.VsNotContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNot</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsNot([NotNull] VB6Parser.VsNotContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsGeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsGeq([NotNull] VB6Parser.VsGeqContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsGeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsGeq([NotNull] VB6Parser.VsGeqContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsTypeOf([NotNull] VB6Parser.VsTypeOfContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsTypeOf([NotNull] VB6Parser.VsTypeOfContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsICS([NotNull] VB6Parser.VsICSContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsICS([NotNull] VB6Parser.VsICSContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsNeq([NotNull] VB6Parser.VsNeqContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsNeq([NotNull] VB6Parser.VsNeqContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsXor</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsXor([NotNull] VB6Parser.VsXorContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsXor</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsXor([NotNull] VB6Parser.VsXorContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAnd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAnd([NotNull] VB6Parser.VsAndContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAnd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAnd([NotNull] VB6Parser.VsAndContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsPow</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsPow([NotNull] VB6Parser.VsPowContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsPow</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsPow([NotNull] VB6Parser.VsPowContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsLeq([NotNull] VB6Parser.VsLeqContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsLeq([NotNull] VB6Parser.VsLeqContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsIs</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsIs([NotNull] VB6Parser.VsIsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsIs</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsIs([NotNull] VB6Parser.VsIsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMod</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsMod([NotNull] VB6Parser.VsModContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMod</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsMod([NotNull] VB6Parser.VsModContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAmp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsAmp([NotNull] VB6Parser.VsAmpContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAmp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsAmp([NotNull] VB6Parser.VsAmpContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsOr</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsOr([NotNull] VB6Parser.VsOrContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsOr</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsOr([NotNull] VB6Parser.VsOrContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMinus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsMinus([NotNull] VB6Parser.VsMinusContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMinus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsMinus([NotNull] VB6Parser.VsMinusContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsLiteral([NotNull] VB6Parser.VsLiteralContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsLiteral([NotNull] VB6Parser.VsLiteralContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsEqv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsEqv([NotNull] VB6Parser.VsEqvContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsEqv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsEqv([NotNull] VB6Parser.VsEqvContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsImp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsImp([NotNull] VB6Parser.VsImpContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsImp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsImp([NotNull] VB6Parser.VsImpContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsGt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsGt([NotNull] VB6Parser.VsGtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsGt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsGt([NotNull] VB6Parser.VsGtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsEq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsEq([NotNull] VB6Parser.VsEqContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsEq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsEq([NotNull] VB6Parser.VsEqContext context) { }
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVsMid([NotNull] VB6Parser.VsMidContext context) { }
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVsMid([NotNull] VB6Parser.VsMidContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.variableStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVariableStmt([NotNull] VB6Parser.VariableStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.variableStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVariableStmt([NotNull] VB6Parser.VariableStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.variableListStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVariableListStmt([NotNull] VB6Parser.VariableListStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.variableListStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVariableListStmt([NotNull] VB6Parser.VariableListStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.variableSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVariableSubStmt([NotNull] VB6Parser.VariableSubStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.variableSubStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVariableSubStmt([NotNull] VB6Parser.VariableSubStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.whileWendStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWhileWendStmt([NotNull] VB6Parser.WhileWendStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.whileWendStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWhileWendStmt([NotNull] VB6Parser.WhileWendStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.widthStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWidthStmt([NotNull] VB6Parser.WidthStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.widthStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWidthStmt([NotNull] VB6Parser.WidthStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.withStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWithStmt([NotNull] VB6Parser.WithStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.withStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWithStmt([NotNull] VB6Parser.WithStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.writeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterWriteStmt([NotNull] VB6Parser.WriteStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.writeStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitWriteStmt([NotNull] VB6Parser.WriteStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.explicitCallStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterExplicitCallStmt([NotNull] VB6Parser.ExplicitCallStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.explicitCallStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitExplicitCallStmt([NotNull] VB6Parser.ExplicitCallStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eCS_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterECS_ProcedureCall([NotNull] VB6Parser.ECS_ProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eCS_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitECS_ProcedureCall([NotNull] VB6Parser.ECS_ProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eCS_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterECS_MemberProcedureCall([NotNull] VB6Parser.ECS_MemberProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eCS_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitECS_MemberProcedureCall([NotNull] VB6Parser.ECS_MemberProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterImplicitCallStmt_InBlock([NotNull] VB6Parser.ImplicitCallStmt_InBlockContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InBlock"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitImplicitCallStmt_InBlock([NotNull] VB6Parser.ImplicitCallStmt_InBlockContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_B_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_B_ProcedureCall([NotNull] VB6Parser.ICS_B_ProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_B_ProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_B_ProcedureCall([NotNull] VB6Parser.ICS_B_ProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_B_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_B_MemberProcedureCall([NotNull] VB6Parser.ICS_B_MemberProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_B_MemberProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_B_MemberProcedureCall([NotNull] VB6Parser.ICS_B_MemberProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterImplicitCallStmt_InStmt([NotNull] VB6Parser.ImplicitCallStmt_InStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitImplicitCallStmt_InStmt([NotNull] VB6Parser.ImplicitCallStmt_InStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_VariableOrProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_VariableOrProcedureCall([NotNull] VB6Parser.ICS_S_VariableOrProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_VariableOrProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_VariableOrProcedureCall([NotNull] VB6Parser.ICS_S_VariableOrProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_ProcedureOrArrayCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_ProcedureOrArrayCall([NotNull] VB6Parser.ICS_S_ProcedureOrArrayCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_ProcedureOrArrayCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_ProcedureOrArrayCall([NotNull] VB6Parser.ICS_S_ProcedureOrArrayCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_NestedProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_NestedProcedureCall([NotNull] VB6Parser.ICS_S_NestedProcedureCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_NestedProcedureCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_NestedProcedureCall([NotNull] VB6Parser.ICS_S_NestedProcedureCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_MembersCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_MembersCall([NotNull] VB6Parser.ICS_S_MembersCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_MembersCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_MembersCall([NotNull] VB6Parser.ICS_S_MembersCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_MemberCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_MemberCall([NotNull] VB6Parser.ICS_S_MemberCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_MemberCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_MemberCall([NotNull] VB6Parser.ICS_S_MemberCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_DictionaryCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterICS_S_DictionaryCall([NotNull] VB6Parser.ICS_S_DictionaryCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_DictionaryCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitICS_S_DictionaryCall([NotNull] VB6Parser.ICS_S_DictionaryCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argsCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgsCall([NotNull] VB6Parser.ArgsCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argsCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgsCall([NotNull] VB6Parser.ArgsCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgCall([NotNull] VB6Parser.ArgCallContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argCall"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgCall([NotNull] VB6Parser.ArgCallContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.dictionaryCallStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterDictionaryCallStmt([NotNull] VB6Parser.DictionaryCallStmtContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.dictionaryCallStmt"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitDictionaryCallStmt([NotNull] VB6Parser.DictionaryCallStmtContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgList([NotNull] VB6Parser.ArgListContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argList"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgList([NotNull] VB6Parser.ArgListContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.arg"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArg([NotNull] VB6Parser.ArgContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.arg"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArg([NotNull] VB6Parser.ArgContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argDefaultValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterArgDefaultValue([NotNull] VB6Parser.ArgDefaultValueContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argDefaultValue"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitArgDefaultValue([NotNull] VB6Parser.ArgDefaultValueContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.subscripts"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSubscripts([NotNull] VB6Parser.SubscriptsContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.subscripts"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSubscripts([NotNull] VB6Parser.SubscriptsContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.subscript"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterSubscript([NotNull] VB6Parser.SubscriptContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.subscript"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitSubscript([NotNull] VB6Parser.SubscriptContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ambiguousIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAmbiguousIdentifier([NotNull] VB6Parser.AmbiguousIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ambiguousIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAmbiguousIdentifier([NotNull] VB6Parser.AmbiguousIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.asTypeClause"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAsTypeClause([NotNull] VB6Parser.AsTypeClauseContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.asTypeClause"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAsTypeClause([NotNull] VB6Parser.AsTypeClauseContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.baseType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterBaseType([NotNull] VB6Parser.BaseTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.baseType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitBaseType([NotNull] VB6Parser.BaseTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.certainIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterCertainIdentifier([NotNull] VB6Parser.CertainIdentifierContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.certainIdentifier"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitCertainIdentifier([NotNull] VB6Parser.CertainIdentifierContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.comparisonOperator"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterComparisonOperator([NotNull] VB6Parser.ComparisonOperatorContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.comparisonOperator"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitComparisonOperator([NotNull] VB6Parser.ComparisonOperatorContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.complexType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterComplexType([NotNull] VB6Parser.ComplexTypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.complexType"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitComplexType([NotNull] VB6Parser.ComplexTypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.fieldLength"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterFieldLength([NotNull] VB6Parser.FieldLengthContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.fieldLength"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitFieldLength([NotNull] VB6Parser.FieldLengthContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.letterrange"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLetterrange([NotNull] VB6Parser.LetterrangeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.letterrange"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLetterrange([NotNull] VB6Parser.LetterrangeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lineLabel"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLineLabel([NotNull] VB6Parser.LineLabelContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lineLabel"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLineLabel([NotNull] VB6Parser.LineLabelContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterLiteral([NotNull] VB6Parser.LiteralContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.literal"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitLiteral([NotNull] VB6Parser.LiteralContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.publicPrivateVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPublicPrivateVisibility([NotNull] VB6Parser.PublicPrivateVisibilityContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.publicPrivateVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPublicPrivateVisibility([NotNull] VB6Parser.PublicPrivateVisibilityContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.publicPrivateGlobalVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterPublicPrivateGlobalVisibility([NotNull] VB6Parser.PublicPrivateGlobalVisibilityContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.publicPrivateGlobalVisibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitPublicPrivateGlobalVisibility([NotNull] VB6Parser.PublicPrivateGlobalVisibilityContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.type"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterType([NotNull] VB6Parser.TypeContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.type"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitType([NotNull] VB6Parser.TypeContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeHint"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterTypeHint([NotNull] VB6Parser.TypeHintContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeHint"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitTypeHint([NotNull] VB6Parser.TypeHintContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.visibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterVisibility([NotNull] VB6Parser.VisibilityContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.visibility"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitVisibility([NotNull] VB6Parser.VisibilityContext context) { }
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ambiguousKeyword"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void EnterAmbiguousKeyword([NotNull] VB6Parser.AmbiguousKeywordContext context) { }
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ambiguousKeyword"/>.
	/// <para>The default implementation does nothing.</para>
	/// </summary>
	/// <param name="context">The parse tree.</param>
	public virtual void ExitAmbiguousKeyword([NotNull] VB6Parser.AmbiguousKeywordContext context) { }

	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void EnterEveryRule([NotNull] ParserRuleContext context) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void ExitEveryRule([NotNull] ParserRuleContext context) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void VisitTerminal([NotNull] ITerminalNode node) { }
	/// <inheritdoc/>
	/// <remarks>The default implementation does nothing.</remarks>
	public virtual void VisitErrorNode([NotNull] IErrorNode node) { }
}
