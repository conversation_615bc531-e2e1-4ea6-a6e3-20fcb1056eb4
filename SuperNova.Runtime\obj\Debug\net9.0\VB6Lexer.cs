//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/Interpreter/Grammar/VB6.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using System;
using System.IO;
using System.Text;
using Antlr4.Runtime;
using Antlr4.Runtime.Atn;
using Antlr4.Runtime.Misc;
using DFA = Antlr4.Runtime.Dfa.DFA;

[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public partial class VB6Lexer : Lexer {
	protected static DFA[] decisionToDFA;
	protected static PredictionContextCache sharedContextCache = new PredictionContextCache();
	public const int
		ACCESS=1, ADDRESSOF=2, ALIAS=3, AND=4, ATTRIBUTE=5, APPACTIVATE=6, APPEND=7, 
		AS=8, BEEP=9, BEGIN=10, BEGINPROPERTY=11, BINARY=12, BOOLEAN=13, BYVAL=14, 
		BYREF=15, BYTE=16, CALL=17, CASE=18, CHDIR=19, CHDRIVE=20, CLASS=21, CLOSE=22, 
		COLLECTION=23, CONST=24, DATE=25, DECLARE=26, DEFBOOL=27, DEFBYTE=28, 
		DEFDATE=29, DEFDBL=30, DEFDEC=31, DEFCUR=32, DEFINT=33, DEFLNG=34, DEFOBJ=35, 
		DEFSNG=36, DEFSTR=37, DEFVAR=38, DELETESETTING=39, DIM=40, DO=41, DOUBLE=42, 
		EACH=43, ELSE=44, ELSEIF=45, END_ENUM=46, END_FUNCTION=47, END_IF=48, 
		END_PROPERTY=49, END_SELECT=50, END_SUB=51, END_TYPE=52, END_WITH=53, 
		END=54, ENDPROPERTY=55, ENUM=56, EQV=57, ERASE=58, ERROR=59, EVENT=60, 
		CONTINUE_DO=61, EXIT_DO=62, EXIT_FOR=63, EXIT_FUNCTION=64, EXIT_PROPERTY=65, 
		EXIT_SUB=66, FALSE=67, FILECOPY=68, FRIEND=69, FOR=70, FUNCTION=71, GET=72, 
		GLOBAL=73, GOSUB=74, GOTO=75, IF=76, IMP=77, IMPLEMENTS=78, IN=79, INPUT=80, 
		IS=81, INTEGER=82, KILL=83, LOAD=84, LOCK=85, LONG=86, LOOP=87, LEN=88, 
		LET=89, LIB=90, LIKE=91, LINE_INPUT=92, LOCK_READ=93, LOCK_WRITE=94, LOCK_READ_WRITE=95, 
		LSET=96, MACRO_IF=97, MACRO_ELSEIF=98, MACRO_ELSE=99, MACRO_END_IF=100, 
		ME=101, MID=102, MKDIR=103, MOD=104, NAME=105, NEXT=106, NEW=107, NOT=108, 
		NOTHING=109, NULL=110, OBJECT=111, ON=112, ON_ERROR=113, ON_LOCAL_ERROR=114, 
		OPEN=115, OPTIONAL=116, OPTION_BASE=117, OPTION_EXPLICIT=118, OPTION_COMPARE=119, 
		OPTION_PRIVATE_MODULE=120, OR=121, OUTPUT=122, PARAMARRAY=123, PRESERVE=124, 
		PRINT=125, PRIVATE=126, PROPERTY_GET=127, PROPERTY_LET=128, PROPERTY_SET=129, 
		PUBLIC=130, PUT=131, RANDOM=132, RANDOMIZE=133, RAISEEVENT=134, READ=135, 
		READ_WRITE=136, REDIM=137, REM=138, RESET=139, RESUME=140, RETURN=141, 
		RMDIR=142, RSET=143, SAVEPICTURE=144, SAVESETTING=145, SEEK=146, SELECT=147, 
		SENDKEYS=148, SET=149, SETATTR=150, SHARED=151, SINGLE=152, SPC=153, STATIC=154, 
		STEP=155, STOP=156, STRING=157, SUB=158, TAB=159, TEXT=160, THEN=161, 
		TIME=162, TO=163, TRUE=164, TYPE=165, TYPEOF=166, UNLOAD=167, UNLOCK=168, 
		UNTIL=169, VARIANT=170, VERSION=171, WEND=172, WHILE=173, WIDTH=174, WITH=175, 
		WITHEVENTS=176, WRITE=177, XOR=178, AMPERSAND=179, ASSIGN=180, AT=181, 
		COLON=182, COMMA=183, DIV=184, DOLLAR=185, DOT=186, EQ=187, EXCLAMATIONMARK=188, 
		GEQ=189, GT=190, HASH=191, LEQ=192, LBRACE=193, LPAREN=194, LT=195, MINUS=196, 
		MINUS_EQ=197, MULT=198, NEQ=199, PERCENT=200, PLUS=201, PLUS_EQ=202, POW=203, 
		RBRACE=204, RPAREN=205, SEMICOLON=206, L_SQUARE_BRACKET=207, R_SQUARE_BRACKET=208, 
		STRINGLITERAL=209, DATELITERAL=210, COLORLITERAL=211, INTEGERLITERAL=212, 
		DOUBLELITERAL=213, FILENUMBER=214, OCTALLITERAL=215, FRX_OFFSET=216, GUID=217, 
		IDENTIFIER=218, LINE_CONTINUATION=219, NEWLINE=220, COMMENT=221, WS=222;
	public static string[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static string[] modeNames = {
		"DEFAULT_MODE"
	};

	public static readonly string[] ruleNames = {
		"ACCESS", "ADDRESSOF", "ALIAS", "AND", "ATTRIBUTE", "APPACTIVATE", "APPEND", 
		"AS", "BEEP", "BEGIN", "BEGINPROPERTY", "BINARY", "BOOLEAN", "BYVAL", 
		"BYREF", "BYTE", "CALL", "CASE", "CHDIR", "CHDRIVE", "CLASS", "CLOSE", 
		"COLLECTION", "CONST", "DATE", "DECLARE", "DEFBOOL", "DEFBYTE", "DEFDATE", 
		"DEFDBL", "DEFDEC", "DEFCUR", "DEFINT", "DEFLNG", "DEFOBJ", "DEFSNG", 
		"DEFSTR", "DEFVAR", "DELETESETTING", "DIM", "DO", "DOUBLE", "EACH", "ELSE", 
		"ELSEIF", "END_ENUM", "END_FUNCTION", "END_IF", "END_PROPERTY", "END_SELECT", 
		"END_SUB", "END_TYPE", "END_WITH", "END", "ENDPROPERTY", "ENUM", "EQV", 
		"ERASE", "ERROR", "EVENT", "CONTINUE_DO", "EXIT_DO", "EXIT_FOR", "EXIT_FUNCTION", 
		"EXIT_PROPERTY", "EXIT_SUB", "FALSE", "FILECOPY", "FRIEND", "FOR", "FUNCTION", 
		"GET", "GLOBAL", "GOSUB", "GOTO", "IF", "IMP", "IMPLEMENTS", "IN", "INPUT", 
		"IS", "INTEGER", "KILL", "LOAD", "LOCK", "LONG", "LOOP", "LEN", "LET", 
		"LIB", "LIKE", "LINE_INPUT", "LOCK_READ", "LOCK_WRITE", "LOCK_READ_WRITE", 
		"LSET", "MACRO_IF", "MACRO_ELSEIF", "MACRO_ELSE", "MACRO_END_IF", "ME", 
		"MID", "MKDIR", "MOD", "NAME", "NEXT", "NEW", "NOT", "NOTHING", "NULL", 
		"OBJECT", "ON", "ON_ERROR", "ON_LOCAL_ERROR", "OPEN", "OPTIONAL", "OPTION_BASE", 
		"OPTION_EXPLICIT", "OPTION_COMPARE", "OPTION_PRIVATE_MODULE", "OR", "OUTPUT", 
		"PARAMARRAY", "PRESERVE", "PRINT", "PRIVATE", "PROPERTY_GET", "PROPERTY_LET", 
		"PROPERTY_SET", "PUBLIC", "PUT", "RANDOM", "RANDOMIZE", "RAISEEVENT", 
		"READ", "READ_WRITE", "REDIM", "REM", "RESET", "RESUME", "RETURN", "RMDIR", 
		"RSET", "SAVEPICTURE", "SAVESETTING", "SEEK", "SELECT", "SENDKEYS", "SET", 
		"SETATTR", "SHARED", "SINGLE", "SPC", "STATIC", "STEP", "STOP", "STRING", 
		"SUB", "TAB", "TEXT", "THEN", "TIME", "TO", "TRUE", "TYPE", "TYPEOF", 
		"UNLOAD", "UNLOCK", "UNTIL", "VARIANT", "VERSION", "WEND", "WHILE", "WIDTH", 
		"WITH", "WITHEVENTS", "WRITE", "XOR", "AMPERSAND", "ASSIGN", "AT", "COLON", 
		"COMMA", "DIV", "DOLLAR", "DOT", "EQ", "EXCLAMATIONMARK", "GEQ", "GT", 
		"HASH", "LEQ", "LBRACE", "LPAREN", "LT", "MINUS", "MINUS_EQ", "MULT", 
		"NEQ", "PERCENT", "PLUS", "PLUS_EQ", "POW", "RBRACE", "RPAREN", "SEMICOLON", 
		"L_SQUARE_BRACKET", "R_SQUARE_BRACKET", "STRINGLITERAL", "DATELITERAL", 
		"COLORLITERAL", "INTEGERLITERAL", "DOUBLELITERAL", "FILENUMBER", "OCTALLITERAL", 
		"FRX_OFFSET", "GUID", "IDENTIFIER", "LINE_CONTINUATION", "NEWLINE", "COMMENT", 
		"WS", "LETTER", "LETTERORDIGIT", "A", "B", "C", "D", "E", "F", "G", "H", 
		"I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", 
		"W", "X", "Y", "Z"
	};


	public VB6Lexer(ICharStream input)
	: this(input, Console.Out, Console.Error) { }

	public VB6Lexer(ICharStream input, TextWriter output, TextWriter errorOutput)
	: base(input, output, errorOutput)
	{
		Interpreter = new LexerATNSimulator(this, _ATN, decisionToDFA, sharedContextCache);
	}

	private static readonly string[] _LiteralNames = {
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, "'&'", 
		"':='", "'@'", "':'", "','", null, "'$'", "'.'", "'='", "'!'", "'>='", 
		"'>'", "'#'", "'<='", "'{'", "'('", "'<'", "'-'", "'-='", "'*'", "'<>'", 
		"'%'", "'+'", "'+='", "'^'", "'}'", "')'", "';'", "'['", "']'"
	};
	private static readonly string[] _SymbolicNames = {
		null, "ACCESS", "ADDRESSOF", "ALIAS", "AND", "ATTRIBUTE", "APPACTIVATE", 
		"APPEND", "AS", "BEEP", "BEGIN", "BEGINPROPERTY", "BINARY", "BOOLEAN", 
		"BYVAL", "BYREF", "BYTE", "CALL", "CASE", "CHDIR", "CHDRIVE", "CLASS", 
		"CLOSE", "COLLECTION", "CONST", "DATE", "DECLARE", "DEFBOOL", "DEFBYTE", 
		"DEFDATE", "DEFDBL", "DEFDEC", "DEFCUR", "DEFINT", "DEFLNG", "DEFOBJ", 
		"DEFSNG", "DEFSTR", "DEFVAR", "DELETESETTING", "DIM", "DO", "DOUBLE", 
		"EACH", "ELSE", "ELSEIF", "END_ENUM", "END_FUNCTION", "END_IF", "END_PROPERTY", 
		"END_SELECT", "END_SUB", "END_TYPE", "END_WITH", "END", "ENDPROPERTY", 
		"ENUM", "EQV", "ERASE", "ERROR", "EVENT", "CONTINUE_DO", "EXIT_DO", "EXIT_FOR", 
		"EXIT_FUNCTION", "EXIT_PROPERTY", "EXIT_SUB", "FALSE", "FILECOPY", "FRIEND", 
		"FOR", "FUNCTION", "GET", "GLOBAL", "GOSUB", "GOTO", "IF", "IMP", "IMPLEMENTS", 
		"IN", "INPUT", "IS", "INTEGER", "KILL", "LOAD", "LOCK", "LONG", "LOOP", 
		"LEN", "LET", "LIB", "LIKE", "LINE_INPUT", "LOCK_READ", "LOCK_WRITE", 
		"LOCK_READ_WRITE", "LSET", "MACRO_IF", "MACRO_ELSEIF", "MACRO_ELSE", "MACRO_END_IF", 
		"ME", "MID", "MKDIR", "MOD", "NAME", "NEXT", "NEW", "NOT", "NOTHING", 
		"NULL", "OBJECT", "ON", "ON_ERROR", "ON_LOCAL_ERROR", "OPEN", "OPTIONAL", 
		"OPTION_BASE", "OPTION_EXPLICIT", "OPTION_COMPARE", "OPTION_PRIVATE_MODULE", 
		"OR", "OUTPUT", "PARAMARRAY", "PRESERVE", "PRINT", "PRIVATE", "PROPERTY_GET", 
		"PROPERTY_LET", "PROPERTY_SET", "PUBLIC", "PUT", "RANDOM", "RANDOMIZE", 
		"RAISEEVENT", "READ", "READ_WRITE", "REDIM", "REM", "RESET", "RESUME", 
		"RETURN", "RMDIR", "RSET", "SAVEPICTURE", "SAVESETTING", "SEEK", "SELECT", 
		"SENDKEYS", "SET", "SETATTR", "SHARED", "SINGLE", "SPC", "STATIC", "STEP", 
		"STOP", "STRING", "SUB", "TAB", "TEXT", "THEN", "TIME", "TO", "TRUE", 
		"TYPE", "TYPEOF", "UNLOAD", "UNLOCK", "UNTIL", "VARIANT", "VERSION", "WEND", 
		"WHILE", "WIDTH", "WITH", "WITHEVENTS", "WRITE", "XOR", "AMPERSAND", "ASSIGN", 
		"AT", "COLON", "COMMA", "DIV", "DOLLAR", "DOT", "EQ", "EXCLAMATIONMARK", 
		"GEQ", "GT", "HASH", "LEQ", "LBRACE", "LPAREN", "LT", "MINUS", "MINUS_EQ", 
		"MULT", "NEQ", "PERCENT", "PLUS", "PLUS_EQ", "POW", "RBRACE", "RPAREN", 
		"SEMICOLON", "L_SQUARE_BRACKET", "R_SQUARE_BRACKET", "STRINGLITERAL", 
		"DATELITERAL", "COLORLITERAL", "INTEGERLITERAL", "DOUBLELITERAL", "FILENUMBER", 
		"OCTALLITERAL", "FRX_OFFSET", "GUID", "IDENTIFIER", "LINE_CONTINUATION", 
		"NEWLINE", "COMMENT", "WS"
	};
	public static readonly IVocabulary DefaultVocabulary = new Vocabulary(_LiteralNames, _SymbolicNames);

	[NotNull]
	public override IVocabulary Vocabulary
	{
		get
		{
			return DefaultVocabulary;
		}
	}

	public override string GrammarFileName { get { return "VB6.g4"; } }

	public override string[] RuleNames { get { return ruleNames; } }

	public override string[] ChannelNames { get { return channelNames; } }

	public override string[] ModeNames { get { return modeNames; } }

	public override int[] SerializedAtn { get { return _serializedATN; } }

	static VB6Lexer() {
		decisionToDFA = new DFA[_ATN.NumberOfDecisions];
		for (int i = 0; i < _ATN.NumberOfDecisions; i++) {
			decisionToDFA[i] = new DFA(_ATN.GetDecisionState(i), i);
		}
	}
	private static int[] _serializedATN = {
		4,0,222,2108,6,-1,2,0,7,0,2,1,7,1,2,2,7,2,2,3,7,3,2,4,7,4,2,5,7,5,2,6,
		7,6,2,7,7,7,2,8,7,8,2,9,7,9,2,10,7,10,2,11,7,11,2,12,7,12,2,13,7,13,2,
		14,7,14,2,15,7,15,2,16,7,16,2,17,7,17,2,18,7,18,2,19,7,19,2,20,7,20,2,
		21,7,21,2,22,7,22,2,23,7,23,2,24,7,24,2,25,7,25,2,26,7,26,2,27,7,27,2,
		28,7,28,2,29,7,29,2,30,7,30,2,31,7,31,2,32,7,32,2,33,7,33,2,34,7,34,2,
		35,7,35,2,36,7,36,2,37,7,37,2,38,7,38,2,39,7,39,2,40,7,40,2,41,7,41,2,
		42,7,42,2,43,7,43,2,44,7,44,2,45,7,45,2,46,7,46,2,47,7,47,2,48,7,48,2,
		49,7,49,2,50,7,50,2,51,7,51,2,52,7,52,2,53,7,53,2,54,7,54,2,55,7,55,2,
		56,7,56,2,57,7,57,2,58,7,58,2,59,7,59,2,60,7,60,2,61,7,61,2,62,7,62,2,
		63,7,63,2,64,7,64,2,65,7,65,2,66,7,66,2,67,7,67,2,68,7,68,2,69,7,69,2,
		70,7,70,2,71,7,71,2,72,7,72,2,73,7,73,2,74,7,74,2,75,7,75,2,76,7,76,2,
		77,7,77,2,78,7,78,2,79,7,79,2,80,7,80,2,81,7,81,2,82,7,82,2,83,7,83,2,
		84,7,84,2,85,7,85,2,86,7,86,2,87,7,87,2,88,7,88,2,89,7,89,2,90,7,90,2,
		91,7,91,2,92,7,92,2,93,7,93,2,94,7,94,2,95,7,95,2,96,7,96,2,97,7,97,2,
		98,7,98,2,99,7,99,2,100,7,100,2,101,7,101,2,102,7,102,2,103,7,103,2,104,
		7,104,2,105,7,105,2,106,7,106,2,107,7,107,2,108,7,108,2,109,7,109,2,110,
		7,110,2,111,7,111,2,112,7,112,2,113,7,113,2,114,7,114,2,115,7,115,2,116,
		7,116,2,117,7,117,2,118,7,118,2,119,7,119,2,120,7,120,2,121,7,121,2,122,
		7,122,2,123,7,123,2,124,7,124,2,125,7,125,2,126,7,126,2,127,7,127,2,128,
		7,128,2,129,7,129,2,130,7,130,2,131,7,131,2,132,7,132,2,133,7,133,2,134,
		7,134,2,135,7,135,2,136,7,136,2,137,7,137,2,138,7,138,2,139,7,139,2,140,
		7,140,2,141,7,141,2,142,7,142,2,143,7,143,2,144,7,144,2,145,7,145,2,146,
		7,146,2,147,7,147,2,148,7,148,2,149,7,149,2,150,7,150,2,151,7,151,2,152,
		7,152,2,153,7,153,2,154,7,154,2,155,7,155,2,156,7,156,2,157,7,157,2,158,
		7,158,2,159,7,159,2,160,7,160,2,161,7,161,2,162,7,162,2,163,7,163,2,164,
		7,164,2,165,7,165,2,166,7,166,2,167,7,167,2,168,7,168,2,169,7,169,2,170,
		7,170,2,171,7,171,2,172,7,172,2,173,7,173,2,174,7,174,2,175,7,175,2,176,
		7,176,2,177,7,177,2,178,7,178,2,179,7,179,2,180,7,180,2,181,7,181,2,182,
		7,182,2,183,7,183,2,184,7,184,2,185,7,185,2,186,7,186,2,187,7,187,2,188,
		7,188,2,189,7,189,2,190,7,190,2,191,7,191,2,192,7,192,2,193,7,193,2,194,
		7,194,2,195,7,195,2,196,7,196,2,197,7,197,2,198,7,198,2,199,7,199,2,200,
		7,200,2,201,7,201,2,202,7,202,2,203,7,203,2,204,7,204,2,205,7,205,2,206,
		7,206,2,207,7,207,2,208,7,208,2,209,7,209,2,210,7,210,2,211,7,211,2,212,
		7,212,2,213,7,213,2,214,7,214,2,215,7,215,2,216,7,216,2,217,7,217,2,218,
		7,218,2,219,7,219,2,220,7,220,2,221,7,221,2,222,7,222,2,223,7,223,2,224,
		7,224,2,225,7,225,2,226,7,226,2,227,7,227,2,228,7,228,2,229,7,229,2,230,
		7,230,2,231,7,231,2,232,7,232,2,233,7,233,2,234,7,234,2,235,7,235,2,236,
		7,236,2,237,7,237,2,238,7,238,2,239,7,239,2,240,7,240,2,241,7,241,2,242,
		7,242,2,243,7,243,2,244,7,244,2,245,7,245,2,246,7,246,2,247,7,247,2,248,
		7,248,2,249,7,249,1,0,1,0,1,0,1,0,1,0,1,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,1,2,1,2,1,2,1,2,1,2,1,2,1,3,1,3,1,3,1,3,1,4,1,4,1,4,1,
		4,1,4,1,4,1,4,1,4,1,4,1,4,1,5,1,5,1,5,1,5,1,5,1,5,1,5,1,5,1,5,1,5,1,5,
		1,5,1,6,1,6,1,6,1,6,1,6,1,6,1,6,1,7,1,7,1,7,1,8,1,8,1,8,1,8,1,8,1,9,1,
		9,1,9,1,9,1,9,1,9,1,10,1,10,1,10,1,10,1,10,1,10,1,10,1,10,1,10,1,10,1,
		10,1,10,1,10,1,10,1,11,1,11,1,11,1,11,1,11,1,11,1,11,1,12,1,12,1,12,1,
		12,1,12,1,12,1,12,1,12,1,13,1,13,1,13,1,13,1,13,1,13,1,14,1,14,1,14,1,
		14,1,14,1,14,1,15,1,15,1,15,1,15,1,15,1,16,1,16,1,16,1,16,1,16,1,17,1,
		17,1,17,1,17,1,17,1,18,1,18,1,18,1,18,1,18,1,18,1,19,1,19,1,19,1,19,1,
		19,1,19,1,19,1,19,1,20,1,20,1,20,1,20,1,20,1,20,1,21,1,21,1,21,1,21,1,
		21,1,21,1,22,1,22,1,22,1,22,1,22,1,22,1,22,1,22,1,22,1,22,1,22,1,23,1,
		23,1,23,1,23,1,23,1,23,1,24,1,24,1,24,1,24,1,24,1,25,1,25,1,25,1,25,1,
		25,1,25,1,25,1,25,1,26,1,26,1,26,1,26,1,26,1,26,1,26,1,26,1,27,1,27,1,
		27,1,27,1,27,1,27,1,27,1,27,1,28,1,28,1,28,1,28,1,28,1,28,1,28,1,28,1,
		29,1,29,1,29,1,29,1,29,1,29,1,29,1,30,1,30,1,30,1,30,1,30,1,30,1,30,1,
		31,1,31,1,31,1,31,1,31,1,31,1,31,1,32,1,32,1,32,1,32,1,32,1,32,1,32,1,
		33,1,33,1,33,1,33,1,33,1,33,1,33,1,34,1,34,1,34,1,34,1,34,1,34,1,34,1,
		35,1,35,1,35,1,35,1,35,1,35,1,35,1,36,1,36,1,36,1,36,1,36,1,36,1,36,1,
		37,1,37,1,37,1,37,1,37,1,37,1,37,1,38,1,38,1,38,1,38,1,38,1,38,1,38,1,
		38,1,38,1,38,1,38,1,38,1,38,1,38,1,39,1,39,1,39,1,39,1,40,1,40,1,40,1,
		41,1,41,1,41,1,41,1,41,1,41,1,41,1,42,1,42,1,42,1,42,1,42,1,43,1,43,1,
		43,1,43,1,43,1,44,1,44,1,44,1,44,1,44,1,44,1,44,1,45,1,45,1,45,1,45,1,
		45,1,45,1,45,1,45,1,45,1,46,1,46,1,46,1,46,1,46,1,46,1,46,1,46,1,46,1,
		46,1,46,1,46,1,46,1,47,1,47,1,47,1,47,1,47,1,47,1,47,1,48,1,48,1,48,1,
		48,1,48,1,48,1,48,1,48,1,48,1,48,1,48,1,48,1,48,1,49,1,49,1,49,1,49,1,
		49,1,49,1,49,1,49,1,49,1,49,1,49,1,50,1,50,1,50,1,50,1,50,1,50,1,50,1,
		50,1,51,1,51,1,51,1,51,1,51,1,51,1,51,1,51,1,51,1,52,1,52,1,52,1,52,1,
		52,1,52,1,52,1,52,1,52,1,53,1,53,1,53,1,53,1,54,1,54,1,54,1,54,1,54,1,
		54,1,54,1,54,1,54,1,54,1,54,1,54,1,55,1,55,1,55,1,55,1,55,1,56,1,56,1,
		56,1,56,1,57,1,57,1,57,1,57,1,57,1,57,1,58,1,58,1,58,1,58,1,58,1,58,1,
		59,1,59,1,59,1,59,1,59,1,59,1,60,1,60,1,60,1,60,1,60,1,60,1,60,1,60,1,
		60,1,60,1,60,1,60,1,61,1,61,1,61,1,61,1,61,1,61,1,61,1,61,1,62,1,62,1,
		62,1,62,1,62,1,62,1,62,1,62,1,62,1,63,1,63,1,63,1,63,1,63,1,63,1,63,1,
		63,1,63,1,63,1,63,1,63,1,63,1,63,1,64,1,64,1,64,1,64,1,64,1,64,1,64,1,
		64,1,64,1,64,1,64,1,64,1,64,1,64,1,65,1,65,1,65,1,65,1,65,1,65,1,65,1,
		65,1,65,1,66,1,66,1,66,1,66,1,66,1,66,1,67,1,67,1,67,1,67,1,67,1,67,1,
		67,1,67,1,67,1,68,1,68,1,68,1,68,1,68,1,68,1,68,1,69,1,69,1,69,1,69,1,
		70,1,70,1,70,1,70,1,70,1,70,1,70,1,70,1,70,1,71,1,71,1,71,1,71,1,72,1,
		72,1,72,1,72,1,72,1,72,1,72,1,73,1,73,1,73,1,73,1,73,1,73,1,74,1,74,1,
		74,1,74,1,74,1,75,1,75,1,75,1,76,1,76,1,76,1,76,1,77,1,77,1,77,1,77,1,
		77,1,77,1,77,1,77,1,77,1,77,1,77,1,78,1,78,1,78,1,79,1,79,1,79,1,79,1,
		79,1,79,1,80,1,80,1,80,1,81,1,81,1,81,1,81,1,81,1,81,1,81,1,81,1,82,1,
		82,1,82,1,82,1,82,1,83,1,83,1,83,1,83,1,83,1,84,1,84,1,84,1,84,1,84,1,
		85,1,85,1,85,1,85,1,85,1,86,1,86,1,86,1,86,1,86,1,87,1,87,1,87,1,87,1,
		88,1,88,1,88,1,88,1,89,1,89,1,89,1,89,1,90,1,90,1,90,1,90,1,90,1,91,1,
		91,1,91,1,91,1,91,1,91,1,91,1,91,1,91,1,91,1,91,1,92,1,92,1,92,1,92,1,
		92,1,92,1,92,1,92,1,92,1,92,1,93,1,93,1,93,1,93,1,93,1,93,1,93,1,93,1,
		93,1,93,1,93,1,94,1,94,1,94,1,94,1,94,1,94,1,94,1,94,1,94,1,94,1,94,1,
		94,1,94,1,94,1,94,1,94,1,95,1,95,1,95,1,95,1,95,1,96,1,96,1,96,1,96,1,
		97,1,97,1,97,1,97,1,97,1,97,1,97,1,97,1,98,1,98,1,98,1,98,1,98,1,98,1,
		99,1,99,1,99,1,99,1,99,1,99,1,99,1,99,1,100,1,100,1,100,1,101,1,101,1,
		101,1,101,1,102,1,102,1,102,1,102,1,102,1,102,1,103,1,103,1,103,1,103,
		1,104,1,104,1,104,1,104,1,104,1,105,1,105,1,105,1,105,1,105,1,106,1,106,
		1,106,1,106,1,107,1,107,1,107,1,107,1,108,1,108,1,108,1,108,1,108,1,108,
		1,108,1,108,1,109,1,109,1,109,1,109,1,109,1,110,1,110,1,110,1,110,1,110,
		1,110,1,110,1,111,1,111,1,111,1,112,1,112,1,112,1,112,1,112,1,112,1,112,
		1,112,1,112,1,113,1,113,1,113,1,113,1,113,1,113,1,113,1,113,1,113,1,113,
		1,113,1,113,1,113,1,113,1,113,1,114,1,114,1,114,1,114,1,114,1,115,1,115,
		1,115,1,115,1,115,1,115,1,115,1,115,1,115,1,116,1,116,1,116,1,116,1,116,
		1,116,1,116,1,116,1,116,1,116,1,116,1,116,1,117,1,117,1,117,1,117,1,117,
		1,117,1,117,1,117,1,117,1,117,1,117,1,117,1,117,1,117,1,117,1,117,1,118,
		1,118,1,118,1,118,1,118,1,118,1,118,1,118,1,118,1,118,1,118,1,118,1,118,
		1,118,1,118,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,
		1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,1,119,
		1,120,1,120,1,120,1,121,1,121,1,121,1,121,1,121,1,121,1,121,1,122,1,122,
		1,122,1,122,1,122,1,122,1,122,1,122,1,122,1,122,1,122,1,123,1,123,1,123,
		1,123,1,123,1,123,1,123,1,123,1,123,1,124,1,124,1,124,1,124,1,124,1,124,
		1,125,1,125,1,125,1,125,1,125,1,125,1,125,1,125,1,126,1,126,1,126,1,126,
		1,126,1,126,1,126,1,126,1,126,1,126,1,126,1,126,1,126,1,127,1,127,1,127,
		1,127,1,127,1,127,1,127,1,127,1,127,1,127,1,127,1,127,1,127,1,128,1,128,
		1,128,1,128,1,128,1,128,1,128,1,128,1,128,1,128,1,128,1,128,1,128,1,129,
		1,129,1,129,1,129,1,129,1,129,1,129,1,130,1,130,1,130,1,130,1,131,1,131,
		1,131,1,131,1,131,1,131,1,131,1,132,1,132,1,132,1,132,1,132,1,132,1,132,
		1,132,1,132,1,132,1,133,1,133,1,133,1,133,1,133,1,133,1,133,1,133,1,133,
		1,133,1,133,1,134,1,134,1,134,1,134,1,134,1,135,1,135,1,135,1,135,1,135,
		1,135,1,135,1,135,1,135,1,135,1,135,1,136,1,136,1,136,1,136,1,136,1,136,
		1,137,1,137,1,137,1,137,1,138,1,138,1,138,1,138,1,138,1,138,1,139,1,139,
		1,139,1,139,1,139,1,139,1,139,1,140,1,140,1,140,1,140,1,140,1,140,1,140,
		1,141,1,141,1,141,1,141,1,141,1,141,1,142,1,142,1,142,1,142,1,142,1,143,
		1,143,1,143,1,143,1,143,1,143,1,143,1,143,1,143,1,143,1,143,1,143,1,144,
		1,144,1,144,1,144,1,144,1,144,1,144,1,144,1,144,1,144,1,144,1,144,1,145,
		1,145,1,145,1,145,1,145,1,146,1,146,1,146,1,146,1,146,1,146,1,146,1,147,
		1,147,1,147,1,147,1,147,1,147,1,147,1,147,1,147,1,148,1,148,1,148,1,148,
		1,149,1,149,1,149,1,149,1,149,1,149,1,149,1,149,1,150,1,150,1,150,1,150,
		1,150,1,150,1,150,1,151,1,151,1,151,1,151,1,151,1,151,1,151,1,152,1,152,
		1,152,1,152,1,153,1,153,1,153,1,153,1,153,1,153,1,153,1,154,1,154,1,154,
		1,154,1,154,1,155,1,155,1,155,1,155,1,155,1,156,1,156,1,156,1,156,1,156,
		1,156,1,156,1,157,1,157,1,157,1,157,1,158,1,158,1,158,1,158,1,159,1,159,
		1,159,1,159,1,159,1,160,1,160,1,160,1,160,1,160,1,161,1,161,1,161,1,161,
		1,161,1,162,1,162,1,162,1,163,1,163,1,163,1,163,1,163,1,164,1,164,1,164,
		1,164,1,164,1,165,1,165,1,165,1,165,1,165,1,165,1,165,1,166,1,166,1,166,
		1,166,1,166,1,166,1,166,1,167,1,167,1,167,1,167,1,167,1,167,1,167,1,168,
		1,168,1,168,1,168,1,168,1,168,1,169,1,169,1,169,1,169,1,169,1,169,1,169,
		1,169,1,170,1,170,1,170,1,170,1,170,1,170,1,170,1,170,1,171,1,171,1,171,
		1,171,1,171,1,172,1,172,1,172,1,172,1,172,1,172,1,173,1,173,1,173,1,173,
		1,173,1,173,1,174,1,174,1,174,1,174,1,174,1,175,1,175,1,175,1,175,1,175,
		1,175,1,175,1,175,1,175,1,175,1,175,1,176,1,176,1,176,1,176,1,176,1,176,
		1,177,1,177,1,177,1,177,1,178,1,178,1,179,1,179,1,179,1,180,1,180,1,181,
		1,181,1,182,1,182,1,183,1,183,1,184,1,184,1,185,1,185,1,186,1,186,1,187,
		1,187,1,188,1,188,1,188,1,189,1,189,1,190,1,190,1,191,1,191,1,191,1,192,
		1,192,1,193,1,193,1,194,1,194,1,195,1,195,1,196,1,196,1,196,1,197,1,197,
		1,198,1,198,1,198,1,199,1,199,1,200,1,200,1,201,1,201,1,201,1,202,1,202,
		1,203,1,203,1,204,1,204,1,205,1,205,1,206,1,206,1,207,1,207,1,208,1,208,
		1,208,1,208,5,208,1851,8,208,10,208,12,208,1854,9,208,1,208,1,208,1,209,
		1,209,5,209,1860,8,209,10,209,12,209,1863,9,209,1,209,1,209,1,210,1,210,
		1,210,1,210,4,210,1871,8,210,11,210,12,210,1872,1,210,3,210,1876,8,210,
		1,211,1,211,3,211,1880,8,211,1,211,4,211,1883,8,211,11,211,12,211,1884,
		1,211,1,211,5,211,1889,8,211,10,211,12,211,1892,9,211,1,211,1,211,1,211,
		1,211,3,211,1898,8,211,1,212,1,212,3,212,1902,8,212,1,212,5,212,1905,8,
		212,10,212,12,212,1908,9,212,1,212,1,212,4,212,1912,8,212,11,212,12,212,
		1913,1,212,1,212,1,212,3,212,1919,8,212,1,212,4,212,1922,8,212,11,212,
		12,212,1923,5,212,1926,8,212,10,212,12,212,1929,9,212,1,212,1,212,1,212,
		1,212,3,212,1935,8,212,1,213,1,213,4,213,1939,8,213,11,213,12,213,1940,
		1,214,1,214,3,214,1945,8,214,1,214,1,214,1,214,1,214,4,214,1951,8,214,
		11,214,12,214,1952,1,214,3,214,1956,8,214,1,215,1,215,4,215,1960,8,215,
		11,215,12,215,1961,1,216,1,216,4,216,1966,8,216,11,216,12,216,1967,1,216,
		1,216,4,216,1972,8,216,11,216,12,216,1973,1,216,1,216,4,216,1978,8,216,
		11,216,12,216,1979,1,216,1,216,4,216,1984,8,216,11,216,12,216,1985,1,216,
		1,216,4,216,1990,8,216,11,216,12,216,1991,1,216,1,216,1,217,1,217,5,217,
		1998,8,217,10,217,12,217,2001,9,217,1,218,1,218,1,218,3,218,2006,8,218,
		1,218,1,218,1,218,1,218,1,219,3,219,2013,8,219,1,219,3,219,2016,8,219,
		1,219,1,219,1,219,1,219,3,219,2022,8,219,1,219,3,219,2025,8,219,1,220,
		3,220,2028,8,220,1,220,1,220,3,220,2032,8,220,1,220,1,220,1,220,3,220,
		2037,8,220,1,220,1,220,5,220,2041,8,220,10,220,12,220,2044,9,220,1,220,
		1,220,1,221,4,221,2049,8,221,11,221,12,221,2050,1,222,1,222,1,223,1,223,
		1,224,1,224,1,225,1,225,1,226,1,226,1,227,1,227,1,228,1,228,1,229,1,229,
		1,230,1,230,1,231,1,231,1,232,1,232,1,233,1,233,1,234,1,234,1,235,1,235,
		1,236,1,236,1,237,1,237,1,238,1,238,1,239,1,239,1,240,1,240,1,241,1,241,
		1,242,1,242,1,243,1,243,1,244,1,244,1,245,1,245,1,246,1,246,1,247,1,247,
		1,248,1,248,1,249,1,249,0,0,250,1,1,3,2,5,3,7,4,9,5,11,6,13,7,15,8,17,
		9,19,10,21,11,23,12,25,13,27,14,29,15,31,16,33,17,35,18,37,19,39,20,41,
		21,43,22,45,23,47,24,49,25,51,26,53,27,55,28,57,29,59,30,61,31,63,32,65,
		33,67,34,69,35,71,36,73,37,75,38,77,39,79,40,81,41,83,42,85,43,87,44,89,
		45,91,46,93,47,95,48,97,49,99,50,101,51,103,52,105,53,107,54,109,55,111,
		56,113,57,115,58,117,59,119,60,121,61,123,62,125,63,127,64,129,65,131,
		66,133,67,135,68,137,69,139,70,141,71,143,72,145,73,147,74,149,75,151,
		76,153,77,155,78,157,79,159,80,161,81,163,82,165,83,167,84,169,85,171,
		86,173,87,175,88,177,89,179,90,181,91,183,92,185,93,187,94,189,95,191,
		96,193,97,195,98,197,99,199,100,201,101,203,102,205,103,207,104,209,105,
		211,106,213,107,215,108,217,109,219,110,221,111,223,112,225,113,227,114,
		229,115,231,116,233,117,235,118,237,119,239,120,241,121,243,122,245,123,
		247,124,249,125,251,126,253,127,255,128,257,129,259,130,261,131,263,132,
		265,133,267,134,269,135,271,136,273,137,275,138,277,139,279,140,281,141,
		283,142,285,143,287,144,289,145,291,146,293,147,295,148,297,149,299,150,
		301,151,303,152,305,153,307,154,309,155,311,156,313,157,315,158,317,159,
		319,160,321,161,323,162,325,163,327,164,329,165,331,166,333,167,335,168,
		337,169,339,170,341,171,343,172,345,173,347,174,349,175,351,176,353,177,
		355,178,357,179,359,180,361,181,363,182,365,183,367,184,369,185,371,186,
		373,187,375,188,377,189,379,190,381,191,383,192,385,193,387,194,389,195,
		391,196,393,197,395,198,397,199,399,200,401,201,403,202,405,203,407,204,
		409,205,411,206,413,207,415,208,417,209,419,210,421,211,423,212,425,213,
		427,214,429,215,431,216,433,217,435,218,437,219,439,220,441,221,443,222,
		445,0,447,0,449,0,451,0,453,0,455,0,457,0,459,0,461,0,463,0,465,0,467,
		0,469,0,471,0,473,0,475,0,477,0,479,0,481,0,483,0,485,0,487,0,489,0,491,
		0,493,0,495,0,497,0,499,0,1,0,35,2,0,47,47,92,92,3,0,10,10,13,13,34,34,
		3,0,10,10,13,13,35,35,2,0,48,57,65,70,2,0,69,69,101,101,1,0,48,55,2,0,
		10,10,13,13,2,0,9,9,32,32,16,0,65,90,95,95,97,122,192,196,199,202,204,
		206,210,214,217,220,224,228,231,234,236,238,242,246,249,252,296,297,360,
		361,7868,7869,17,0,48,57,65,90,95,95,97,122,192,196,199,202,204,206,210,
		214,217,220,224,228,231,234,236,238,242,246,249,252,296,297,360,361,7868,
		7869,2,0,65,65,97,97,2,0,66,66,98,98,2,0,67,67,99,99,2,0,68,68,100,100,
		2,0,70,70,102,102,2,0,71,71,103,103,2,0,72,72,104,104,2,0,73,73,105,105,
		2,0,74,74,106,106,2,0,75,75,107,107,2,0,76,76,108,108,2,0,77,77,109,109,
		2,0,78,78,110,110,2,0,79,79,111,111,2,0,80,80,112,112,2,0,81,81,113,113,
		2,0,82,82,114,114,2,0,83,83,115,115,2,0,84,84,116,116,2,0,85,85,117,117,
		2,0,86,86,118,118,2,0,87,87,119,119,2,0,88,88,120,120,2,0,89,89,121,121,
		2,0,90,90,122,122,2127,0,1,1,0,0,0,0,3,1,0,0,0,0,5,1,0,0,0,0,7,1,0,0,0,
		0,9,1,0,0,0,0,11,1,0,0,0,0,13,1,0,0,0,0,15,1,0,0,0,0,17,1,0,0,0,0,19,1,
		0,0,0,0,21,1,0,0,0,0,23,1,0,0,0,0,25,1,0,0,0,0,27,1,0,0,0,0,29,1,0,0,0,
		0,31,1,0,0,0,0,33,1,0,0,0,0,35,1,0,0,0,0,37,1,0,0,0,0,39,1,0,0,0,0,41,
		1,0,0,0,0,43,1,0,0,0,0,45,1,0,0,0,0,47,1,0,0,0,0,49,1,0,0,0,0,51,1,0,0,
		0,0,53,1,0,0,0,0,55,1,0,0,0,0,57,1,0,0,0,0,59,1,0,0,0,0,61,1,0,0,0,0,63,
		1,0,0,0,0,65,1,0,0,0,0,67,1,0,0,0,0,69,1,0,0,0,0,71,1,0,0,0,0,73,1,0,0,
		0,0,75,1,0,0,0,0,77,1,0,0,0,0,79,1,0,0,0,0,81,1,0,0,0,0,83,1,0,0,0,0,85,
		1,0,0,0,0,87,1,0,0,0,0,89,1,0,0,0,0,91,1,0,0,0,0,93,1,0,0,0,0,95,1,0,0,
		0,0,97,1,0,0,0,0,99,1,0,0,0,0,101,1,0,0,0,0,103,1,0,0,0,0,105,1,0,0,0,
		0,107,1,0,0,0,0,109,1,0,0,0,0,111,1,0,0,0,0,113,1,0,0,0,0,115,1,0,0,0,
		0,117,1,0,0,0,0,119,1,0,0,0,0,121,1,0,0,0,0,123,1,0,0,0,0,125,1,0,0,0,
		0,127,1,0,0,0,0,129,1,0,0,0,0,131,1,0,0,0,0,133,1,0,0,0,0,135,1,0,0,0,
		0,137,1,0,0,0,0,139,1,0,0,0,0,141,1,0,0,0,0,143,1,0,0,0,0,145,1,0,0,0,
		0,147,1,0,0,0,0,149,1,0,0,0,0,151,1,0,0,0,0,153,1,0,0,0,0,155,1,0,0,0,
		0,157,1,0,0,0,0,159,1,0,0,0,0,161,1,0,0,0,0,163,1,0,0,0,0,165,1,0,0,0,
		0,167,1,0,0,0,0,169,1,0,0,0,0,171,1,0,0,0,0,173,1,0,0,0,0,175,1,0,0,0,
		0,177,1,0,0,0,0,179,1,0,0,0,0,181,1,0,0,0,0,183,1,0,0,0,0,185,1,0,0,0,
		0,187,1,0,0,0,0,189,1,0,0,0,0,191,1,0,0,0,0,193,1,0,0,0,0,195,1,0,0,0,
		0,197,1,0,0,0,0,199,1,0,0,0,0,201,1,0,0,0,0,203,1,0,0,0,0,205,1,0,0,0,
		0,207,1,0,0,0,0,209,1,0,0,0,0,211,1,0,0,0,0,213,1,0,0,0,0,215,1,0,0,0,
		0,217,1,0,0,0,0,219,1,0,0,0,0,221,1,0,0,0,0,223,1,0,0,0,0,225,1,0,0,0,
		0,227,1,0,0,0,0,229,1,0,0,0,0,231,1,0,0,0,0,233,1,0,0,0,0,235,1,0,0,0,
		0,237,1,0,0,0,0,239,1,0,0,0,0,241,1,0,0,0,0,243,1,0,0,0,0,245,1,0,0,0,
		0,247,1,0,0,0,0,249,1,0,0,0,0,251,1,0,0,0,0,253,1,0,0,0,0,255,1,0,0,0,
		0,257,1,0,0,0,0,259,1,0,0,0,0,261,1,0,0,0,0,263,1,0,0,0,0,265,1,0,0,0,
		0,267,1,0,0,0,0,269,1,0,0,0,0,271,1,0,0,0,0,273,1,0,0,0,0,275,1,0,0,0,
		0,277,1,0,0,0,0,279,1,0,0,0,0,281,1,0,0,0,0,283,1,0,0,0,0,285,1,0,0,0,
		0,287,1,0,0,0,0,289,1,0,0,0,0,291,1,0,0,0,0,293,1,0,0,0,0,295,1,0,0,0,
		0,297,1,0,0,0,0,299,1,0,0,0,0,301,1,0,0,0,0,303,1,0,0,0,0,305,1,0,0,0,
		0,307,1,0,0,0,0,309,1,0,0,0,0,311,1,0,0,0,0,313,1,0,0,0,0,315,1,0,0,0,
		0,317,1,0,0,0,0,319,1,0,0,0,0,321,1,0,0,0,0,323,1,0,0,0,0,325,1,0,0,0,
		0,327,1,0,0,0,0,329,1,0,0,0,0,331,1,0,0,0,0,333,1,0,0,0,0,335,1,0,0,0,
		0,337,1,0,0,0,0,339,1,0,0,0,0,341,1,0,0,0,0,343,1,0,0,0,0,345,1,0,0,0,
		0,347,1,0,0,0,0,349,1,0,0,0,0,351,1,0,0,0,0,353,1,0,0,0,0,355,1,0,0,0,
		0,357,1,0,0,0,0,359,1,0,0,0,0,361,1,0,0,0,0,363,1,0,0,0,0,365,1,0,0,0,
		0,367,1,0,0,0,0,369,1,0,0,0,0,371,1,0,0,0,0,373,1,0,0,0,0,375,1,0,0,0,
		0,377,1,0,0,0,0,379,1,0,0,0,0,381,1,0,0,0,0,383,1,0,0,0,0,385,1,0,0,0,
		0,387,1,0,0,0,0,389,1,0,0,0,0,391,1,0,0,0,0,393,1,0,0,0,0,395,1,0,0,0,
		0,397,1,0,0,0,0,399,1,0,0,0,0,401,1,0,0,0,0,403,1,0,0,0,0,405,1,0,0,0,
		0,407,1,0,0,0,0,409,1,0,0,0,0,411,1,0,0,0,0,413,1,0,0,0,0,415,1,0,0,0,
		0,417,1,0,0,0,0,419,1,0,0,0,0,421,1,0,0,0,0,423,1,0,0,0,0,425,1,0,0,0,
		0,427,1,0,0,0,0,429,1,0,0,0,0,431,1,0,0,0,0,433,1,0,0,0,0,435,1,0,0,0,
		0,437,1,0,0,0,0,439,1,0,0,0,0,441,1,0,0,0,0,443,1,0,0,0,1,501,1,0,0,0,
		3,508,1,0,0,0,5,518,1,0,0,0,7,524,1,0,0,0,9,528,1,0,0,0,11,538,1,0,0,0,
		13,550,1,0,0,0,15,557,1,0,0,0,17,560,1,0,0,0,19,565,1,0,0,0,21,571,1,0,
		0,0,23,585,1,0,0,0,25,592,1,0,0,0,27,600,1,0,0,0,29,606,1,0,0,0,31,612,
		1,0,0,0,33,617,1,0,0,0,35,622,1,0,0,0,37,627,1,0,0,0,39,633,1,0,0,0,41,
		641,1,0,0,0,43,647,1,0,0,0,45,653,1,0,0,0,47,664,1,0,0,0,49,670,1,0,0,
		0,51,675,1,0,0,0,53,683,1,0,0,0,55,691,1,0,0,0,57,699,1,0,0,0,59,707,1,
		0,0,0,61,714,1,0,0,0,63,721,1,0,0,0,65,728,1,0,0,0,67,735,1,0,0,0,69,742,
		1,0,0,0,71,749,1,0,0,0,73,756,1,0,0,0,75,763,1,0,0,0,77,770,1,0,0,0,79,
		784,1,0,0,0,81,788,1,0,0,0,83,791,1,0,0,0,85,798,1,0,0,0,87,803,1,0,0,
		0,89,808,1,0,0,0,91,815,1,0,0,0,93,824,1,0,0,0,95,837,1,0,0,0,97,844,1,
		0,0,0,99,857,1,0,0,0,101,868,1,0,0,0,103,876,1,0,0,0,105,885,1,0,0,0,107,
		894,1,0,0,0,109,898,1,0,0,0,111,910,1,0,0,0,113,915,1,0,0,0,115,919,1,
		0,0,0,117,925,1,0,0,0,119,931,1,0,0,0,121,937,1,0,0,0,123,949,1,0,0,0,
		125,957,1,0,0,0,127,966,1,0,0,0,129,980,1,0,0,0,131,994,1,0,0,0,133,1003,
		1,0,0,0,135,1009,1,0,0,0,137,1018,1,0,0,0,139,1025,1,0,0,0,141,1029,1,
		0,0,0,143,1038,1,0,0,0,145,1042,1,0,0,0,147,1049,1,0,0,0,149,1055,1,0,
		0,0,151,1060,1,0,0,0,153,1063,1,0,0,0,155,1067,1,0,0,0,157,1078,1,0,0,
		0,159,1081,1,0,0,0,161,1087,1,0,0,0,163,1090,1,0,0,0,165,1098,1,0,0,0,
		167,1103,1,0,0,0,169,1108,1,0,0,0,171,1113,1,0,0,0,173,1118,1,0,0,0,175,
		1123,1,0,0,0,177,1127,1,0,0,0,179,1131,1,0,0,0,181,1135,1,0,0,0,183,1140,
		1,0,0,0,185,1151,1,0,0,0,187,1161,1,0,0,0,189,1172,1,0,0,0,191,1188,1,
		0,0,0,193,1193,1,0,0,0,195,1197,1,0,0,0,197,1205,1,0,0,0,199,1211,1,0,
		0,0,201,1219,1,0,0,0,203,1222,1,0,0,0,205,1226,1,0,0,0,207,1232,1,0,0,
		0,209,1236,1,0,0,0,211,1241,1,0,0,0,213,1246,1,0,0,0,215,1250,1,0,0,0,
		217,1254,1,0,0,0,219,1262,1,0,0,0,221,1267,1,0,0,0,223,1274,1,0,0,0,225,
		1277,1,0,0,0,227,1286,1,0,0,0,229,1301,1,0,0,0,231,1306,1,0,0,0,233,1315,
		1,0,0,0,235,1327,1,0,0,0,237,1343,1,0,0,0,239,1358,1,0,0,0,241,1380,1,
		0,0,0,243,1383,1,0,0,0,245,1390,1,0,0,0,247,1401,1,0,0,0,249,1410,1,0,
		0,0,251,1416,1,0,0,0,253,1424,1,0,0,0,255,1437,1,0,0,0,257,1450,1,0,0,
		0,259,1463,1,0,0,0,261,1470,1,0,0,0,263,1474,1,0,0,0,265,1481,1,0,0,0,
		267,1491,1,0,0,0,269,1502,1,0,0,0,271,1507,1,0,0,0,273,1518,1,0,0,0,275,
		1524,1,0,0,0,277,1528,1,0,0,0,279,1534,1,0,0,0,281,1541,1,0,0,0,283,1548,
		1,0,0,0,285,1554,1,0,0,0,287,1559,1,0,0,0,289,1571,1,0,0,0,291,1583,1,
		0,0,0,293,1588,1,0,0,0,295,1595,1,0,0,0,297,1604,1,0,0,0,299,1608,1,0,
		0,0,301,1616,1,0,0,0,303,1623,1,0,0,0,305,1630,1,0,0,0,307,1634,1,0,0,
		0,309,1641,1,0,0,0,311,1646,1,0,0,0,313,1651,1,0,0,0,315,1658,1,0,0,0,
		317,1662,1,0,0,0,319,1666,1,0,0,0,321,1671,1,0,0,0,323,1676,1,0,0,0,325,
		1681,1,0,0,0,327,1684,1,0,0,0,329,1689,1,0,0,0,331,1694,1,0,0,0,333,1701,
		1,0,0,0,335,1708,1,0,0,0,337,1715,1,0,0,0,339,1721,1,0,0,0,341,1729,1,
		0,0,0,343,1737,1,0,0,0,345,1742,1,0,0,0,347,1748,1,0,0,0,349,1754,1,0,
		0,0,351,1759,1,0,0,0,353,1770,1,0,0,0,355,1776,1,0,0,0,357,1780,1,0,0,
		0,359,1782,1,0,0,0,361,1785,1,0,0,0,363,1787,1,0,0,0,365,1789,1,0,0,0,
		367,1791,1,0,0,0,369,1793,1,0,0,0,371,1795,1,0,0,0,373,1797,1,0,0,0,375,
		1799,1,0,0,0,377,1801,1,0,0,0,379,1804,1,0,0,0,381,1806,1,0,0,0,383,1808,
		1,0,0,0,385,1811,1,0,0,0,387,1813,1,0,0,0,389,1815,1,0,0,0,391,1817,1,
		0,0,0,393,1819,1,0,0,0,395,1822,1,0,0,0,397,1824,1,0,0,0,399,1827,1,0,
		0,0,401,1829,1,0,0,0,403,1831,1,0,0,0,405,1834,1,0,0,0,407,1836,1,0,0,
		0,409,1838,1,0,0,0,411,1840,1,0,0,0,413,1842,1,0,0,0,415,1844,1,0,0,0,
		417,1846,1,0,0,0,419,1857,1,0,0,0,421,1866,1,0,0,0,423,1879,1,0,0,0,425,
		1901,1,0,0,0,427,1936,1,0,0,0,429,1944,1,0,0,0,431,1957,1,0,0,0,433,1963,
		1,0,0,0,435,1995,1,0,0,0,437,2002,1,0,0,0,439,2012,1,0,0,0,441,2027,1,
		0,0,0,443,2048,1,0,0,0,445,2052,1,0,0,0,447,2054,1,0,0,0,449,2056,1,0,
		0,0,451,2058,1,0,0,0,453,2060,1,0,0,0,455,2062,1,0,0,0,457,2064,1,0,0,
		0,459,2066,1,0,0,0,461,2068,1,0,0,0,463,2070,1,0,0,0,465,2072,1,0,0,0,
		467,2074,1,0,0,0,469,2076,1,0,0,0,471,2078,1,0,0,0,473,2080,1,0,0,0,475,
		2082,1,0,0,0,477,2084,1,0,0,0,479,2086,1,0,0,0,481,2088,1,0,0,0,483,2090,
		1,0,0,0,485,2092,1,0,0,0,487,2094,1,0,0,0,489,2096,1,0,0,0,491,2098,1,
		0,0,0,493,2100,1,0,0,0,495,2102,1,0,0,0,497,2104,1,0,0,0,499,2106,1,0,
		0,0,501,502,3,449,224,0,502,503,3,453,226,0,503,504,3,453,226,0,504,505,
		3,457,228,0,505,506,3,485,242,0,506,507,3,485,242,0,507,2,1,0,0,0,508,
		509,3,449,224,0,509,510,3,455,227,0,510,511,3,455,227,0,511,512,3,483,
		241,0,512,513,3,457,228,0,513,514,3,485,242,0,514,515,3,485,242,0,515,
		516,3,477,238,0,516,517,3,459,229,0,517,4,1,0,0,0,518,519,3,449,224,0,
		519,520,3,471,235,0,520,521,3,465,232,0,521,522,3,449,224,0,522,523,3,
		485,242,0,523,6,1,0,0,0,524,525,3,449,224,0,525,526,3,475,237,0,526,527,
		3,455,227,0,527,8,1,0,0,0,528,529,3,449,224,0,529,530,3,487,243,0,530,
		531,3,487,243,0,531,532,3,483,241,0,532,533,3,465,232,0,533,534,3,451,
		225,0,534,535,3,489,244,0,535,536,3,487,243,0,536,537,3,457,228,0,537,
		10,1,0,0,0,538,539,3,449,224,0,539,540,3,479,239,0,540,541,3,479,239,0,
		541,542,3,449,224,0,542,543,3,453,226,0,543,544,3,487,243,0,544,545,3,
		465,232,0,545,546,3,491,245,0,546,547,3,449,224,0,547,548,3,487,243,0,
		548,549,3,457,228,0,549,12,1,0,0,0,550,551,3,449,224,0,551,552,3,479,239,
		0,552,553,3,479,239,0,553,554,3,457,228,0,554,555,3,475,237,0,555,556,
		3,455,227,0,556,14,1,0,0,0,557,558,3,449,224,0,558,559,3,485,242,0,559,
		16,1,0,0,0,560,561,3,451,225,0,561,562,3,457,228,0,562,563,3,457,228,0,
		563,564,3,479,239,0,564,18,1,0,0,0,565,566,3,451,225,0,566,567,3,457,228,
		0,567,568,3,461,230,0,568,569,3,465,232,0,569,570,3,475,237,0,570,20,1,
		0,0,0,571,572,3,451,225,0,572,573,3,457,228,0,573,574,3,461,230,0,574,
		575,3,465,232,0,575,576,3,475,237,0,576,577,3,479,239,0,577,578,3,483,
		241,0,578,579,3,477,238,0,579,580,3,479,239,0,580,581,3,457,228,0,581,
		582,3,483,241,0,582,583,3,487,243,0,583,584,3,497,248,0,584,22,1,0,0,0,
		585,586,3,451,225,0,586,587,3,465,232,0,587,588,3,475,237,0,588,589,3,
		449,224,0,589,590,3,483,241,0,590,591,3,497,248,0,591,24,1,0,0,0,592,593,
		3,451,225,0,593,594,3,477,238,0,594,595,3,477,238,0,595,596,3,471,235,
		0,596,597,3,457,228,0,597,598,3,449,224,0,598,599,3,475,237,0,599,26,1,
		0,0,0,600,601,3,451,225,0,601,602,3,497,248,0,602,603,3,491,245,0,603,
		604,3,449,224,0,604,605,3,471,235,0,605,28,1,0,0,0,606,607,3,451,225,0,
		607,608,3,497,248,0,608,609,3,483,241,0,609,610,3,457,228,0,610,611,3,
		459,229,0,611,30,1,0,0,0,612,613,3,451,225,0,613,614,3,497,248,0,614,615,
		3,487,243,0,615,616,3,457,228,0,616,32,1,0,0,0,617,618,3,453,226,0,618,
		619,3,449,224,0,619,620,3,471,235,0,620,621,3,471,235,0,621,34,1,0,0,0,
		622,623,3,453,226,0,623,624,3,449,224,0,624,625,3,485,242,0,625,626,3,
		457,228,0,626,36,1,0,0,0,627,628,3,453,226,0,628,629,3,463,231,0,629,630,
		3,455,227,0,630,631,3,465,232,0,631,632,3,483,241,0,632,38,1,0,0,0,633,
		634,3,453,226,0,634,635,3,463,231,0,635,636,3,455,227,0,636,637,3,483,
		241,0,637,638,3,465,232,0,638,639,3,491,245,0,639,640,3,457,228,0,640,
		40,1,0,0,0,641,642,3,453,226,0,642,643,3,471,235,0,643,644,3,449,224,0,
		644,645,3,485,242,0,645,646,3,485,242,0,646,42,1,0,0,0,647,648,3,453,226,
		0,648,649,3,471,235,0,649,650,3,477,238,0,650,651,3,485,242,0,651,652,
		3,457,228,0,652,44,1,0,0,0,653,654,3,453,226,0,654,655,3,477,238,0,655,
		656,3,471,235,0,656,657,3,471,235,0,657,658,3,457,228,0,658,659,3,453,
		226,0,659,660,3,487,243,0,660,661,3,465,232,0,661,662,3,477,238,0,662,
		663,3,475,237,0,663,46,1,0,0,0,664,665,3,453,226,0,665,666,3,477,238,0,
		666,667,3,475,237,0,667,668,3,485,242,0,668,669,3,487,243,0,669,48,1,0,
		0,0,670,671,3,455,227,0,671,672,3,449,224,0,672,673,3,487,243,0,673,674,
		3,457,228,0,674,50,1,0,0,0,675,676,3,455,227,0,676,677,3,457,228,0,677,
		678,3,453,226,0,678,679,3,471,235,0,679,680,3,449,224,0,680,681,3,483,
		241,0,681,682,3,457,228,0,682,52,1,0,0,0,683,684,3,455,227,0,684,685,3,
		457,228,0,685,686,3,459,229,0,686,687,3,451,225,0,687,688,3,477,238,0,
		688,689,3,477,238,0,689,690,3,471,235,0,690,54,1,0,0,0,691,692,3,455,227,
		0,692,693,3,457,228,0,693,694,3,459,229,0,694,695,3,451,225,0,695,696,
		3,497,248,0,696,697,3,487,243,0,697,698,3,457,228,0,698,56,1,0,0,0,699,
		700,3,455,227,0,700,701,3,457,228,0,701,702,3,459,229,0,702,703,3,455,
		227,0,703,704,3,449,224,0,704,705,3,487,243,0,705,706,3,457,228,0,706,
		58,1,0,0,0,707,708,3,455,227,0,708,709,3,457,228,0,709,710,3,459,229,0,
		710,711,3,455,227,0,711,712,3,451,225,0,712,713,3,471,235,0,713,60,1,0,
		0,0,714,715,3,455,227,0,715,716,3,457,228,0,716,717,3,459,229,0,717,718,
		3,455,227,0,718,719,3,457,228,0,719,720,3,453,226,0,720,62,1,0,0,0,721,
		722,3,455,227,0,722,723,3,457,228,0,723,724,3,459,229,0,724,725,3,453,
		226,0,725,726,3,489,244,0,726,727,3,483,241,0,727,64,1,0,0,0,728,729,3,
		455,227,0,729,730,3,457,228,0,730,731,3,459,229,0,731,732,3,465,232,0,
		732,733,3,475,237,0,733,734,3,487,243,0,734,66,1,0,0,0,735,736,3,455,227,
		0,736,737,3,457,228,0,737,738,3,459,229,0,738,739,3,471,235,0,739,740,
		3,475,237,0,740,741,3,461,230,0,741,68,1,0,0,0,742,743,3,455,227,0,743,
		744,3,457,228,0,744,745,3,459,229,0,745,746,3,477,238,0,746,747,3,451,
		225,0,747,748,3,467,233,0,748,70,1,0,0,0,749,750,3,455,227,0,750,751,3,
		457,228,0,751,752,3,459,229,0,752,753,3,485,242,0,753,754,3,475,237,0,
		754,755,3,461,230,0,755,72,1,0,0,0,756,757,3,455,227,0,757,758,3,457,228,
		0,758,759,3,459,229,0,759,760,3,485,242,0,760,761,3,487,243,0,761,762,
		3,483,241,0,762,74,1,0,0,0,763,764,3,455,227,0,764,765,3,457,228,0,765,
		766,3,459,229,0,766,767,3,491,245,0,767,768,3,449,224,0,768,769,3,483,
		241,0,769,76,1,0,0,0,770,771,3,455,227,0,771,772,3,457,228,0,772,773,3,
		471,235,0,773,774,3,457,228,0,774,775,3,487,243,0,775,776,3,457,228,0,
		776,777,3,485,242,0,777,778,3,457,228,0,778,779,3,487,243,0,779,780,3,
		487,243,0,780,781,3,465,232,0,781,782,3,475,237,0,782,783,3,461,230,0,
		783,78,1,0,0,0,784,785,3,455,227,0,785,786,3,465,232,0,786,787,3,473,236,
		0,787,80,1,0,0,0,788,789,3,455,227,0,789,790,3,477,238,0,790,82,1,0,0,
		0,791,792,3,455,227,0,792,793,3,477,238,0,793,794,3,489,244,0,794,795,
		3,451,225,0,795,796,3,471,235,0,796,797,3,457,228,0,797,84,1,0,0,0,798,
		799,3,457,228,0,799,800,3,449,224,0,800,801,3,453,226,0,801,802,3,463,
		231,0,802,86,1,0,0,0,803,804,3,457,228,0,804,805,3,471,235,0,805,806,3,
		485,242,0,806,807,3,457,228,0,807,88,1,0,0,0,808,809,3,457,228,0,809,810,
		3,471,235,0,810,811,3,485,242,0,811,812,3,457,228,0,812,813,3,465,232,
		0,813,814,3,459,229,0,814,90,1,0,0,0,815,816,3,457,228,0,816,817,3,475,
		237,0,817,818,3,455,227,0,818,819,5,32,0,0,819,820,3,457,228,0,820,821,
		3,475,237,0,821,822,3,489,244,0,822,823,3,473,236,0,823,92,1,0,0,0,824,
		825,3,457,228,0,825,826,3,475,237,0,826,827,3,455,227,0,827,828,5,32,0,
		0,828,829,3,459,229,0,829,830,3,489,244,0,830,831,3,475,237,0,831,832,
		3,453,226,0,832,833,3,487,243,0,833,834,3,465,232,0,834,835,3,477,238,
		0,835,836,3,475,237,0,836,94,1,0,0,0,837,838,3,457,228,0,838,839,3,475,
		237,0,839,840,3,455,227,0,840,841,5,32,0,0,841,842,3,465,232,0,842,843,
		3,459,229,0,843,96,1,0,0,0,844,845,3,457,228,0,845,846,3,475,237,0,846,
		847,3,455,227,0,847,848,5,32,0,0,848,849,3,479,239,0,849,850,3,483,241,
		0,850,851,3,477,238,0,851,852,3,479,239,0,852,853,3,457,228,0,853,854,
		3,483,241,0,854,855,3,487,243,0,855,856,3,497,248,0,856,98,1,0,0,0,857,
		858,3,457,228,0,858,859,3,475,237,0,859,860,3,455,227,0,860,861,5,32,0,
		0,861,862,3,485,242,0,862,863,3,457,228,0,863,864,3,471,235,0,864,865,
		3,457,228,0,865,866,3,453,226,0,866,867,3,487,243,0,867,100,1,0,0,0,868,
		869,3,457,228,0,869,870,3,475,237,0,870,871,3,455,227,0,871,872,5,32,0,
		0,872,873,3,485,242,0,873,874,3,489,244,0,874,875,3,451,225,0,875,102,
		1,0,0,0,876,877,3,457,228,0,877,878,3,475,237,0,878,879,3,455,227,0,879,
		880,5,32,0,0,880,881,3,487,243,0,881,882,3,497,248,0,882,883,3,479,239,
		0,883,884,3,457,228,0,884,104,1,0,0,0,885,886,3,457,228,0,886,887,3,475,
		237,0,887,888,3,455,227,0,888,889,5,32,0,0,889,890,3,493,246,0,890,891,
		3,465,232,0,891,892,3,487,243,0,892,893,3,463,231,0,893,106,1,0,0,0,894,
		895,3,457,228,0,895,896,3,475,237,0,896,897,3,455,227,0,897,108,1,0,0,
		0,898,899,3,457,228,0,899,900,3,475,237,0,900,901,3,455,227,0,901,902,
		3,479,239,0,902,903,3,483,241,0,903,904,3,477,238,0,904,905,3,479,239,
		0,905,906,3,457,228,0,906,907,3,483,241,0,907,908,3,487,243,0,908,909,
		3,497,248,0,909,110,1,0,0,0,910,911,3,457,228,0,911,912,3,475,237,0,912,
		913,3,489,244,0,913,914,3,473,236,0,914,112,1,0,0,0,915,916,3,457,228,
		0,916,917,3,481,240,0,917,918,3,491,245,0,918,114,1,0,0,0,919,920,3,457,
		228,0,920,921,3,483,241,0,921,922,3,449,224,0,922,923,3,485,242,0,923,
		924,3,457,228,0,924,116,1,0,0,0,925,926,3,457,228,0,926,927,3,483,241,
		0,927,928,3,483,241,0,928,929,3,477,238,0,929,930,3,483,241,0,930,118,
		1,0,0,0,931,932,3,457,228,0,932,933,3,491,245,0,933,934,3,457,228,0,934,
		935,3,475,237,0,935,936,3,487,243,0,936,120,1,0,0,0,937,938,3,453,226,
		0,938,939,3,477,238,0,939,940,3,475,237,0,940,941,3,487,243,0,941,942,
		3,465,232,0,942,943,3,475,237,0,943,944,3,489,244,0,944,945,3,457,228,
		0,945,946,5,32,0,0,946,947,3,455,227,0,947,948,3,477,238,0,948,122,1,0,
		0,0,949,950,3,457,228,0,950,951,3,495,247,0,951,952,3,465,232,0,952,953,
		3,487,243,0,953,954,5,32,0,0,954,955,3,455,227,0,955,956,3,477,238,0,956,
		124,1,0,0,0,957,958,3,457,228,0,958,959,3,495,247,0,959,960,3,465,232,
		0,960,961,3,487,243,0,961,962,5,32,0,0,962,963,3,459,229,0,963,964,3,477,
		238,0,964,965,3,483,241,0,965,126,1,0,0,0,966,967,3,457,228,0,967,968,
		3,495,247,0,968,969,3,465,232,0,969,970,3,487,243,0,970,971,5,32,0,0,971,
		972,3,459,229,0,972,973,3,489,244,0,973,974,3,475,237,0,974,975,3,453,
		226,0,975,976,3,487,243,0,976,977,3,465,232,0,977,978,3,477,238,0,978,
		979,3,475,237,0,979,128,1,0,0,0,980,981,3,457,228,0,981,982,3,495,247,
		0,982,983,3,465,232,0,983,984,3,487,243,0,984,985,5,32,0,0,985,986,3,479,
		239,0,986,987,3,483,241,0,987,988,3,477,238,0,988,989,3,479,239,0,989,
		990,3,457,228,0,990,991,3,483,241,0,991,992,3,487,243,0,992,993,3,497,
		248,0,993,130,1,0,0,0,994,995,3,457,228,0,995,996,3,495,247,0,996,997,
		3,465,232,0,997,998,3,487,243,0,998,999,5,32,0,0,999,1000,3,485,242,0,
		1000,1001,3,489,244,0,1001,1002,3,451,225,0,1002,132,1,0,0,0,1003,1004,
		3,459,229,0,1004,1005,3,449,224,0,1005,1006,3,471,235,0,1006,1007,3,485,
		242,0,1007,1008,3,457,228,0,1008,134,1,0,0,0,1009,1010,3,459,229,0,1010,
		1011,3,465,232,0,1011,1012,3,471,235,0,1012,1013,3,457,228,0,1013,1014,
		3,453,226,0,1014,1015,3,477,238,0,1015,1016,3,479,239,0,1016,1017,3,497,
		248,0,1017,136,1,0,0,0,1018,1019,3,459,229,0,1019,1020,3,483,241,0,1020,
		1021,3,465,232,0,1021,1022,3,457,228,0,1022,1023,3,475,237,0,1023,1024,
		3,455,227,0,1024,138,1,0,0,0,1025,1026,3,459,229,0,1026,1027,3,477,238,
		0,1027,1028,3,483,241,0,1028,140,1,0,0,0,1029,1030,3,459,229,0,1030,1031,
		3,489,244,0,1031,1032,3,475,237,0,1032,1033,3,453,226,0,1033,1034,3,487,
		243,0,1034,1035,3,465,232,0,1035,1036,3,477,238,0,1036,1037,3,475,237,
		0,1037,142,1,0,0,0,1038,1039,3,461,230,0,1039,1040,3,457,228,0,1040,1041,
		3,487,243,0,1041,144,1,0,0,0,1042,1043,3,461,230,0,1043,1044,3,471,235,
		0,1044,1045,3,477,238,0,1045,1046,3,451,225,0,1046,1047,3,449,224,0,1047,
		1048,3,471,235,0,1048,146,1,0,0,0,1049,1050,3,461,230,0,1050,1051,3,477,
		238,0,1051,1052,3,485,242,0,1052,1053,3,489,244,0,1053,1054,3,451,225,
		0,1054,148,1,0,0,0,1055,1056,3,461,230,0,1056,1057,3,477,238,0,1057,1058,
		3,487,243,0,1058,1059,3,477,238,0,1059,150,1,0,0,0,1060,1061,3,465,232,
		0,1061,1062,3,459,229,0,1062,152,1,0,0,0,1063,1064,3,465,232,0,1064,1065,
		3,473,236,0,1065,1066,3,479,239,0,1066,154,1,0,0,0,1067,1068,3,465,232,
		0,1068,1069,3,473,236,0,1069,1070,3,479,239,0,1070,1071,3,471,235,0,1071,
		1072,3,457,228,0,1072,1073,3,473,236,0,1073,1074,3,457,228,0,1074,1075,
		3,475,237,0,1075,1076,3,487,243,0,1076,1077,3,485,242,0,1077,156,1,0,0,
		0,1078,1079,3,465,232,0,1079,1080,3,475,237,0,1080,158,1,0,0,0,1081,1082,
		3,465,232,0,1082,1083,3,475,237,0,1083,1084,3,479,239,0,1084,1085,3,489,
		244,0,1085,1086,3,487,243,0,1086,160,1,0,0,0,1087,1088,3,465,232,0,1088,
		1089,3,485,242,0,1089,162,1,0,0,0,1090,1091,3,465,232,0,1091,1092,3,475,
		237,0,1092,1093,3,487,243,0,1093,1094,3,457,228,0,1094,1095,3,461,230,
		0,1095,1096,3,457,228,0,1096,1097,3,483,241,0,1097,164,1,0,0,0,1098,1099,
		3,469,234,0,1099,1100,3,465,232,0,1100,1101,3,471,235,0,1101,1102,3,471,
		235,0,1102,166,1,0,0,0,1103,1104,3,471,235,0,1104,1105,3,477,238,0,1105,
		1106,3,449,224,0,1106,1107,3,455,227,0,1107,168,1,0,0,0,1108,1109,3,471,
		235,0,1109,1110,3,477,238,0,1110,1111,3,453,226,0,1111,1112,3,469,234,
		0,1112,170,1,0,0,0,1113,1114,3,471,235,0,1114,1115,3,477,238,0,1115,1116,
		3,475,237,0,1116,1117,3,461,230,0,1117,172,1,0,0,0,1118,1119,3,471,235,
		0,1119,1120,3,477,238,0,1120,1121,3,477,238,0,1121,1122,3,479,239,0,1122,
		174,1,0,0,0,1123,1124,3,471,235,0,1124,1125,3,457,228,0,1125,1126,3,475,
		237,0,1126,176,1,0,0,0,1127,1128,3,471,235,0,1128,1129,3,457,228,0,1129,
		1130,3,487,243,0,1130,178,1,0,0,0,1131,1132,3,471,235,0,1132,1133,3,465,
		232,0,1133,1134,3,451,225,0,1134,180,1,0,0,0,1135,1136,3,471,235,0,1136,
		1137,3,465,232,0,1137,1138,3,469,234,0,1138,1139,3,457,228,0,1139,182,
		1,0,0,0,1140,1141,3,471,235,0,1141,1142,3,465,232,0,1142,1143,3,475,237,
		0,1143,1144,3,457,228,0,1144,1145,5,32,0,0,1145,1146,3,465,232,0,1146,
		1147,3,475,237,0,1147,1148,3,479,239,0,1148,1149,3,489,244,0,1149,1150,
		3,487,243,0,1150,184,1,0,0,0,1151,1152,3,471,235,0,1152,1153,3,477,238,
		0,1153,1154,3,453,226,0,1154,1155,3,469,234,0,1155,1156,5,32,0,0,1156,
		1157,3,483,241,0,1157,1158,3,457,228,0,1158,1159,3,449,224,0,1159,1160,
		3,455,227,0,1160,186,1,0,0,0,1161,1162,3,471,235,0,1162,1163,3,477,238,
		0,1163,1164,3,453,226,0,1164,1165,3,469,234,0,1165,1166,5,32,0,0,1166,
		1167,3,493,246,0,1167,1168,3,483,241,0,1168,1169,3,465,232,0,1169,1170,
		3,487,243,0,1170,1171,3,457,228,0,1171,188,1,0,0,0,1172,1173,3,471,235,
		0,1173,1174,3,477,238,0,1174,1175,3,453,226,0,1175,1176,3,469,234,0,1176,
		1177,5,32,0,0,1177,1178,3,483,241,0,1178,1179,3,457,228,0,1179,1180,3,
		449,224,0,1180,1181,3,455,227,0,1181,1182,5,32,0,0,1182,1183,3,493,246,
		0,1183,1184,3,483,241,0,1184,1185,3,465,232,0,1185,1186,3,487,243,0,1186,
		1187,3,457,228,0,1187,190,1,0,0,0,1188,1189,3,471,235,0,1189,1190,3,485,
		242,0,1190,1191,3,457,228,0,1191,1192,3,487,243,0,1192,192,1,0,0,0,1193,
		1194,3,381,190,0,1194,1195,3,465,232,0,1195,1196,3,459,229,0,1196,194,
		1,0,0,0,1197,1198,3,381,190,0,1198,1199,3,457,228,0,1199,1200,3,471,235,
		0,1200,1201,3,485,242,0,1201,1202,3,457,228,0,1202,1203,3,465,232,0,1203,
		1204,3,459,229,0,1204,196,1,0,0,0,1205,1206,3,381,190,0,1206,1207,3,457,
		228,0,1207,1208,3,471,235,0,1208,1209,3,485,242,0,1209,1210,3,457,228,
		0,1210,198,1,0,0,0,1211,1212,3,381,190,0,1212,1213,3,457,228,0,1213,1214,
		3,475,237,0,1214,1215,3,455,227,0,1215,1216,5,32,0,0,1216,1217,3,465,232,
		0,1217,1218,3,459,229,0,1218,200,1,0,0,0,1219,1220,3,473,236,0,1220,1221,
		3,457,228,0,1221,202,1,0,0,0,1222,1223,3,473,236,0,1223,1224,3,465,232,
		0,1224,1225,3,455,227,0,1225,204,1,0,0,0,1226,1227,3,473,236,0,1227,1228,
		3,469,234,0,1228,1229,3,455,227,0,1229,1230,3,465,232,0,1230,1231,3,483,
		241,0,1231,206,1,0,0,0,1232,1233,3,473,236,0,1233,1234,3,477,238,0,1234,
		1235,3,455,227,0,1235,208,1,0,0,0,1236,1237,3,475,237,0,1237,1238,3,449,
		224,0,1238,1239,3,473,236,0,1239,1240,3,457,228,0,1240,210,1,0,0,0,1241,
		1242,3,475,237,0,1242,1243,3,457,228,0,1243,1244,3,495,247,0,1244,1245,
		3,487,243,0,1245,212,1,0,0,0,1246,1247,3,475,237,0,1247,1248,3,457,228,
		0,1248,1249,3,493,246,0,1249,214,1,0,0,0,1250,1251,3,475,237,0,1251,1252,
		3,477,238,0,1252,1253,3,487,243,0,1253,216,1,0,0,0,1254,1255,3,475,237,
		0,1255,1256,3,477,238,0,1256,1257,3,487,243,0,1257,1258,3,463,231,0,1258,
		1259,3,465,232,0,1259,1260,3,475,237,0,1260,1261,3,461,230,0,1261,218,
		1,0,0,0,1262,1263,3,475,237,0,1263,1264,3,489,244,0,1264,1265,3,471,235,
		0,1265,1266,3,471,235,0,1266,220,1,0,0,0,1267,1268,3,477,238,0,1268,1269,
		3,451,225,0,1269,1270,3,467,233,0,1270,1271,3,457,228,0,1271,1272,3,453,
		226,0,1272,1273,3,487,243,0,1273,222,1,0,0,0,1274,1275,3,477,238,0,1275,
		1276,3,475,237,0,1276,224,1,0,0,0,1277,1278,3,477,238,0,1278,1279,3,475,
		237,0,1279,1280,5,32,0,0,1280,1281,3,457,228,0,1281,1282,3,483,241,0,1282,
		1283,3,483,241,0,1283,1284,3,477,238,0,1284,1285,3,483,241,0,1285,226,
		1,0,0,0,1286,1287,3,477,238,0,1287,1288,3,475,237,0,1288,1289,5,32,0,0,
		1289,1290,3,471,235,0,1290,1291,3,477,238,0,1291,1292,3,453,226,0,1292,
		1293,3,449,224,0,1293,1294,3,471,235,0,1294,1295,5,32,0,0,1295,1296,3,
		457,228,0,1296,1297,3,483,241,0,1297,1298,3,483,241,0,1298,1299,3,477,
		238,0,1299,1300,3,483,241,0,1300,228,1,0,0,0,1301,1302,3,477,238,0,1302,
		1303,3,479,239,0,1303,1304,3,457,228,0,1304,1305,3,475,237,0,1305,230,
		1,0,0,0,1306,1307,3,477,238,0,1307,1308,3,479,239,0,1308,1309,3,487,243,
		0,1309,1310,3,465,232,0,1310,1311,3,477,238,0,1311,1312,3,475,237,0,1312,
		1313,3,449,224,0,1313,1314,3,471,235,0,1314,232,1,0,0,0,1315,1316,3,477,
		238,0,1316,1317,3,479,239,0,1317,1318,3,487,243,0,1318,1319,3,465,232,
		0,1319,1320,3,477,238,0,1320,1321,3,475,237,0,1321,1322,5,32,0,0,1322,
		1323,3,451,225,0,1323,1324,3,449,224,0,1324,1325,3,485,242,0,1325,1326,
		3,457,228,0,1326,234,1,0,0,0,1327,1328,3,477,238,0,1328,1329,3,479,239,
		0,1329,1330,3,487,243,0,1330,1331,3,465,232,0,1331,1332,3,477,238,0,1332,
		1333,3,475,237,0,1333,1334,5,32,0,0,1334,1335,3,457,228,0,1335,1336,3,
		495,247,0,1336,1337,3,479,239,0,1337,1338,3,471,235,0,1338,1339,3,465,
		232,0,1339,1340,3,453,226,0,1340,1341,3,465,232,0,1341,1342,3,487,243,
		0,1342,236,1,0,0,0,1343,1344,3,477,238,0,1344,1345,3,479,239,0,1345,1346,
		3,487,243,0,1346,1347,3,465,232,0,1347,1348,3,477,238,0,1348,1349,3,475,
		237,0,1349,1350,5,32,0,0,1350,1351,3,453,226,0,1351,1352,3,477,238,0,1352,
		1353,3,473,236,0,1353,1354,3,479,239,0,1354,1355,3,449,224,0,1355,1356,
		3,483,241,0,1356,1357,3,457,228,0,1357,238,1,0,0,0,1358,1359,3,477,238,
		0,1359,1360,3,479,239,0,1360,1361,3,487,243,0,1361,1362,3,465,232,0,1362,
		1363,3,477,238,0,1363,1364,3,475,237,0,1364,1365,5,32,0,0,1365,1366,3,
		479,239,0,1366,1367,3,483,241,0,1367,1368,3,465,232,0,1368,1369,3,491,
		245,0,1369,1370,3,449,224,0,1370,1371,3,487,243,0,1371,1372,3,457,228,
		0,1372,1373,5,32,0,0,1373,1374,3,473,236,0,1374,1375,3,477,238,0,1375,
		1376,3,455,227,0,1376,1377,3,489,244,0,1377,1378,3,471,235,0,1378,1379,
		3,457,228,0,1379,240,1,0,0,0,1380,1381,3,477,238,0,1381,1382,3,483,241,
		0,1382,242,1,0,0,0,1383,1384,3,477,238,0,1384,1385,3,489,244,0,1385,1386,
		3,487,243,0,1386,1387,3,479,239,0,1387,1388,3,489,244,0,1388,1389,3,487,
		243,0,1389,244,1,0,0,0,1390,1391,3,479,239,0,1391,1392,3,449,224,0,1392,
		1393,3,483,241,0,1393,1394,3,449,224,0,1394,1395,3,473,236,0,1395,1396,
		3,449,224,0,1396,1397,3,483,241,0,1397,1398,3,483,241,0,1398,1399,3,449,
		224,0,1399,1400,3,497,248,0,1400,246,1,0,0,0,1401,1402,3,479,239,0,1402,
		1403,3,483,241,0,1403,1404,3,457,228,0,1404,1405,3,485,242,0,1405,1406,
		3,457,228,0,1406,1407,3,483,241,0,1407,1408,3,491,245,0,1408,1409,3,457,
		228,0,1409,248,1,0,0,0,1410,1411,3,479,239,0,1411,1412,3,483,241,0,1412,
		1413,3,465,232,0,1413,1414,3,475,237,0,1414,1415,3,487,243,0,1415,250,
		1,0,0,0,1416,1417,3,479,239,0,1417,1418,3,483,241,0,1418,1419,3,465,232,
		0,1419,1420,3,491,245,0,1420,1421,3,449,224,0,1421,1422,3,487,243,0,1422,
		1423,3,457,228,0,1423,252,1,0,0,0,1424,1425,3,479,239,0,1425,1426,3,483,
		241,0,1426,1427,3,477,238,0,1427,1428,3,479,239,0,1428,1429,3,457,228,
		0,1429,1430,3,483,241,0,1430,1431,3,487,243,0,1431,1432,3,497,248,0,1432,
		1433,5,32,0,0,1433,1434,3,461,230,0,1434,1435,3,457,228,0,1435,1436,3,
		487,243,0,1436,254,1,0,0,0,1437,1438,3,479,239,0,1438,1439,3,483,241,0,
		1439,1440,3,477,238,0,1440,1441,3,479,239,0,1441,1442,3,457,228,0,1442,
		1443,3,483,241,0,1443,1444,3,487,243,0,1444,1445,3,497,248,0,1445,1446,
		5,32,0,0,1446,1447,3,471,235,0,1447,1448,3,457,228,0,1448,1449,3,487,243,
		0,1449,256,1,0,0,0,1450,1451,3,479,239,0,1451,1452,3,483,241,0,1452,1453,
		3,477,238,0,1453,1454,3,479,239,0,1454,1455,3,457,228,0,1455,1456,3,483,
		241,0,1456,1457,3,487,243,0,1457,1458,3,497,248,0,1458,1459,5,32,0,0,1459,
		1460,3,485,242,0,1460,1461,3,457,228,0,1461,1462,3,487,243,0,1462,258,
		1,0,0,0,1463,1464,3,479,239,0,1464,1465,3,489,244,0,1465,1466,3,451,225,
		0,1466,1467,3,471,235,0,1467,1468,3,465,232,0,1468,1469,3,453,226,0,1469,
		260,1,0,0,0,1470,1471,3,479,239,0,1471,1472,3,489,244,0,1472,1473,3,487,
		243,0,1473,262,1,0,0,0,1474,1475,3,483,241,0,1475,1476,3,449,224,0,1476,
		1477,3,475,237,0,1477,1478,3,455,227,0,1478,1479,3,477,238,0,1479,1480,
		3,473,236,0,1480,264,1,0,0,0,1481,1482,3,483,241,0,1482,1483,3,449,224,
		0,1483,1484,3,475,237,0,1484,1485,3,455,227,0,1485,1486,3,477,238,0,1486,
		1487,3,473,236,0,1487,1488,3,465,232,0,1488,1489,3,499,249,0,1489,1490,
		3,457,228,0,1490,266,1,0,0,0,1491,1492,3,483,241,0,1492,1493,3,449,224,
		0,1493,1494,3,465,232,0,1494,1495,3,485,242,0,1495,1496,3,457,228,0,1496,
		1497,3,457,228,0,1497,1498,3,491,245,0,1498,1499,3,457,228,0,1499,1500,
		3,475,237,0,1500,1501,3,487,243,0,1501,268,1,0,0,0,1502,1503,3,483,241,
		0,1503,1504,3,457,228,0,1504,1505,3,449,224,0,1505,1506,3,455,227,0,1506,
		270,1,0,0,0,1507,1508,3,483,241,0,1508,1509,3,457,228,0,1509,1510,3,449,
		224,0,1510,1511,3,455,227,0,1511,1512,5,32,0,0,1512,1513,3,493,246,0,1513,
		1514,3,483,241,0,1514,1515,3,465,232,0,1515,1516,3,487,243,0,1516,1517,
		3,457,228,0,1517,272,1,0,0,0,1518,1519,3,483,241,0,1519,1520,3,457,228,
		0,1520,1521,3,455,227,0,1521,1522,3,465,232,0,1522,1523,3,473,236,0,1523,
		274,1,0,0,0,1524,1525,3,483,241,0,1525,1526,3,457,228,0,1526,1527,3,473,
		236,0,1527,276,1,0,0,0,1528,1529,3,483,241,0,1529,1530,3,457,228,0,1530,
		1531,3,485,242,0,1531,1532,3,457,228,0,1532,1533,3,487,243,0,1533,278,
		1,0,0,0,1534,1535,3,483,241,0,1535,1536,3,457,228,0,1536,1537,3,485,242,
		0,1537,1538,3,489,244,0,1538,1539,3,473,236,0,1539,1540,3,457,228,0,1540,
		280,1,0,0,0,1541,1542,3,483,241,0,1542,1543,3,457,228,0,1543,1544,3,487,
		243,0,1544,1545,3,489,244,0,1545,1546,3,483,241,0,1546,1547,3,475,237,
		0,1547,282,1,0,0,0,1548,1549,3,483,241,0,1549,1550,3,473,236,0,1550,1551,
		3,455,227,0,1551,1552,3,465,232,0,1552,1553,3,483,241,0,1553,284,1,0,0,
		0,1554,1555,3,483,241,0,1555,1556,3,485,242,0,1556,1557,3,457,228,0,1557,
		1558,3,487,243,0,1558,286,1,0,0,0,1559,1560,3,485,242,0,1560,1561,3,449,
		224,0,1561,1562,3,491,245,0,1562,1563,3,457,228,0,1563,1564,3,479,239,
		0,1564,1565,3,465,232,0,1565,1566,3,453,226,0,1566,1567,3,487,243,0,1567,
		1568,3,489,244,0,1568,1569,3,483,241,0,1569,1570,3,457,228,0,1570,288,
		1,0,0,0,1571,1572,3,485,242,0,1572,1573,3,449,224,0,1573,1574,3,491,245,
		0,1574,1575,3,457,228,0,1575,1576,3,485,242,0,1576,1577,3,457,228,0,1577,
		1578,3,487,243,0,1578,1579,3,487,243,0,1579,1580,3,465,232,0,1580,1581,
		3,475,237,0,1581,1582,3,461,230,0,1582,290,1,0,0,0,1583,1584,3,485,242,
		0,1584,1585,3,457,228,0,1585,1586,3,457,228,0,1586,1587,3,469,234,0,1587,
		292,1,0,0,0,1588,1589,3,485,242,0,1589,1590,3,457,228,0,1590,1591,3,471,
		235,0,1591,1592,3,457,228,0,1592,1593,3,453,226,0,1593,1594,3,487,243,
		0,1594,294,1,0,0,0,1595,1596,3,485,242,0,1596,1597,3,457,228,0,1597,1598,
		3,475,237,0,1598,1599,3,455,227,0,1599,1600,3,469,234,0,1600,1601,3,457,
		228,0,1601,1602,3,497,248,0,1602,1603,3,485,242,0,1603,296,1,0,0,0,1604,
		1605,3,485,242,0,1605,1606,3,457,228,0,1606,1607,3,487,243,0,1607,298,
		1,0,0,0,1608,1609,3,485,242,0,1609,1610,3,457,228,0,1610,1611,3,487,243,
		0,1611,1612,3,449,224,0,1612,1613,3,487,243,0,1613,1614,3,487,243,0,1614,
		1615,3,483,241,0,1615,300,1,0,0,0,1616,1617,3,485,242,0,1617,1618,3,463,
		231,0,1618,1619,3,449,224,0,1619,1620,3,483,241,0,1620,1621,3,457,228,
		0,1621,1622,3,455,227,0,1622,302,1,0,0,0,1623,1624,3,485,242,0,1624,1625,
		3,465,232,0,1625,1626,3,475,237,0,1626,1627,3,461,230,0,1627,1628,3,471,
		235,0,1628,1629,3,457,228,0,1629,304,1,0,0,0,1630,1631,3,485,242,0,1631,
		1632,3,479,239,0,1632,1633,3,453,226,0,1633,306,1,0,0,0,1634,1635,3,485,
		242,0,1635,1636,3,487,243,0,1636,1637,3,449,224,0,1637,1638,3,487,243,
		0,1638,1639,3,465,232,0,1639,1640,3,453,226,0,1640,308,1,0,0,0,1641,1642,
		3,485,242,0,1642,1643,3,487,243,0,1643,1644,3,457,228,0,1644,1645,3,479,
		239,0,1645,310,1,0,0,0,1646,1647,3,485,242,0,1647,1648,3,487,243,0,1648,
		1649,3,477,238,0,1649,1650,3,479,239,0,1650,312,1,0,0,0,1651,1652,3,485,
		242,0,1652,1653,3,487,243,0,1653,1654,3,483,241,0,1654,1655,3,465,232,
		0,1655,1656,3,475,237,0,1656,1657,3,461,230,0,1657,314,1,0,0,0,1658,1659,
		3,485,242,0,1659,1660,3,489,244,0,1660,1661,3,451,225,0,1661,316,1,0,0,
		0,1662,1663,3,487,243,0,1663,1664,3,449,224,0,1664,1665,3,451,225,0,1665,
		318,1,0,0,0,1666,1667,3,487,243,0,1667,1668,3,457,228,0,1668,1669,3,495,
		247,0,1669,1670,3,487,243,0,1670,320,1,0,0,0,1671,1672,3,487,243,0,1672,
		1673,3,463,231,0,1673,1674,3,457,228,0,1674,1675,3,475,237,0,1675,322,
		1,0,0,0,1676,1677,3,487,243,0,1677,1678,3,465,232,0,1678,1679,3,473,236,
		0,1679,1680,3,457,228,0,1680,324,1,0,0,0,1681,1682,3,487,243,0,1682,1683,
		3,477,238,0,1683,326,1,0,0,0,1684,1685,3,487,243,0,1685,1686,3,483,241,
		0,1686,1687,3,489,244,0,1687,1688,3,457,228,0,1688,328,1,0,0,0,1689,1690,
		3,487,243,0,1690,1691,3,497,248,0,1691,1692,3,479,239,0,1692,1693,3,457,
		228,0,1693,330,1,0,0,0,1694,1695,3,487,243,0,1695,1696,3,497,248,0,1696,
		1697,3,479,239,0,1697,1698,3,457,228,0,1698,1699,3,477,238,0,1699,1700,
		3,459,229,0,1700,332,1,0,0,0,1701,1702,3,489,244,0,1702,1703,3,475,237,
		0,1703,1704,3,471,235,0,1704,1705,3,477,238,0,1705,1706,3,449,224,0,1706,
		1707,3,455,227,0,1707,334,1,0,0,0,1708,1709,3,489,244,0,1709,1710,3,475,
		237,0,1710,1711,3,471,235,0,1711,1712,3,477,238,0,1712,1713,3,453,226,
		0,1713,1714,3,469,234,0,1714,336,1,0,0,0,1715,1716,3,489,244,0,1716,1717,
		3,475,237,0,1717,1718,3,487,243,0,1718,1719,3,465,232,0,1719,1720,3,471,
		235,0,1720,338,1,0,0,0,1721,1722,3,491,245,0,1722,1723,3,449,224,0,1723,
		1724,3,483,241,0,1724,1725,3,465,232,0,1725,1726,3,449,224,0,1726,1727,
		3,475,237,0,1727,1728,3,487,243,0,1728,340,1,0,0,0,1729,1730,3,491,245,
		0,1730,1731,3,457,228,0,1731,1732,3,483,241,0,1732,1733,3,485,242,0,1733,
		1734,3,465,232,0,1734,1735,3,477,238,0,1735,1736,3,475,237,0,1736,342,
		1,0,0,0,1737,1738,3,493,246,0,1738,1739,3,457,228,0,1739,1740,3,475,237,
		0,1740,1741,3,455,227,0,1741,344,1,0,0,0,1742,1743,3,493,246,0,1743,1744,
		3,463,231,0,1744,1745,3,465,232,0,1745,1746,3,471,235,0,1746,1747,3,457,
		228,0,1747,346,1,0,0,0,1748,1749,3,493,246,0,1749,1750,3,465,232,0,1750,
		1751,3,455,227,0,1751,1752,3,487,243,0,1752,1753,3,463,231,0,1753,348,
		1,0,0,0,1754,1755,3,493,246,0,1755,1756,3,465,232,0,1756,1757,3,487,243,
		0,1757,1758,3,463,231,0,1758,350,1,0,0,0,1759,1760,3,493,246,0,1760,1761,
		3,465,232,0,1761,1762,3,487,243,0,1762,1763,3,463,231,0,1763,1764,3,457,
		228,0,1764,1765,3,491,245,0,1765,1766,3,457,228,0,1766,1767,3,475,237,
		0,1767,1768,3,487,243,0,1768,1769,3,485,242,0,1769,352,1,0,0,0,1770,1771,
		3,493,246,0,1771,1772,3,483,241,0,1772,1773,3,465,232,0,1773,1774,3,487,
		243,0,1774,1775,3,457,228,0,1775,354,1,0,0,0,1776,1777,3,495,247,0,1777,
		1778,3,477,238,0,1778,1779,3,483,241,0,1779,356,1,0,0,0,1780,1781,5,38,
		0,0,1781,358,1,0,0,0,1782,1783,5,58,0,0,1783,1784,5,61,0,0,1784,360,1,
		0,0,0,1785,1786,5,64,0,0,1786,362,1,0,0,0,1787,1788,5,58,0,0,1788,364,
		1,0,0,0,1789,1790,5,44,0,0,1790,366,1,0,0,0,1791,1792,7,0,0,0,1792,368,
		1,0,0,0,1793,1794,5,36,0,0,1794,370,1,0,0,0,1795,1796,5,46,0,0,1796,372,
		1,0,0,0,1797,1798,5,61,0,0,1798,374,1,0,0,0,1799,1800,5,33,0,0,1800,376,
		1,0,0,0,1801,1802,5,62,0,0,1802,1803,5,61,0,0,1803,378,1,0,0,0,1804,1805,
		5,62,0,0,1805,380,1,0,0,0,1806,1807,5,35,0,0,1807,382,1,0,0,0,1808,1809,
		5,60,0,0,1809,1810,5,61,0,0,1810,384,1,0,0,0,1811,1812,5,123,0,0,1812,
		386,1,0,0,0,1813,1814,5,40,0,0,1814,388,1,0,0,0,1815,1816,5,60,0,0,1816,
		390,1,0,0,0,1817,1818,5,45,0,0,1818,392,1,0,0,0,1819,1820,5,45,0,0,1820,
		1821,5,61,0,0,1821,394,1,0,0,0,1822,1823,5,42,0,0,1823,396,1,0,0,0,1824,
		1825,5,60,0,0,1825,1826,5,62,0,0,1826,398,1,0,0,0,1827,1828,5,37,0,0,1828,
		400,1,0,0,0,1829,1830,5,43,0,0,1830,402,1,0,0,0,1831,1832,5,43,0,0,1832,
		1833,5,61,0,0,1833,404,1,0,0,0,1834,1835,5,94,0,0,1835,406,1,0,0,0,1836,
		1837,5,125,0,0,1837,408,1,0,0,0,1838,1839,5,41,0,0,1839,410,1,0,0,0,1840,
		1841,5,59,0,0,1841,412,1,0,0,0,1842,1843,5,91,0,0,1843,414,1,0,0,0,1844,
		1845,5,93,0,0,1845,416,1,0,0,0,1846,1852,5,34,0,0,1847,1851,8,1,0,0,1848,
		1849,5,34,0,0,1849,1851,5,34,0,0,1850,1847,1,0,0,0,1850,1848,1,0,0,0,1851,
		1854,1,0,0,0,1852,1850,1,0,0,0,1852,1853,1,0,0,0,1853,1855,1,0,0,0,1854,
		1852,1,0,0,0,1855,1856,5,34,0,0,1856,418,1,0,0,0,1857,1861,3,381,190,0,
		1858,1860,8,2,0,0,1859,1858,1,0,0,0,1860,1863,1,0,0,0,1861,1859,1,0,0,
		0,1861,1862,1,0,0,0,1862,1864,1,0,0,0,1863,1861,1,0,0,0,1864,1865,3,381,
		190,0,1865,420,1,0,0,0,1866,1867,5,38,0,0,1867,1868,5,72,0,0,1868,1870,
		1,0,0,0,1869,1871,7,3,0,0,1870,1869,1,0,0,0,1871,1872,1,0,0,0,1872,1870,
		1,0,0,0,1872,1873,1,0,0,0,1873,1875,1,0,0,0,1874,1876,3,357,178,0,1875,
		1874,1,0,0,0,1875,1876,1,0,0,0,1876,422,1,0,0,0,1877,1880,3,401,200,0,
		1878,1880,3,391,195,0,1879,1877,1,0,0,0,1879,1878,1,0,0,0,1879,1880,1,
		0,0,0,1880,1882,1,0,0,0,1881,1883,2,48,57,0,1882,1881,1,0,0,0,1883,1884,
		1,0,0,0,1884,1882,1,0,0,0,1884,1885,1,0,0,0,1885,1890,1,0,0,0,1886,1887,
		7,4,0,0,1887,1889,3,423,211,0,1888,1886,1,0,0,0,1889,1892,1,0,0,0,1890,
		1888,1,0,0,0,1890,1891,1,0,0,0,1891,1897,1,0,0,0,1892,1890,1,0,0,0,1893,
		1898,3,381,190,0,1894,1898,3,357,178,0,1895,1898,3,375,187,0,1896,1898,
		3,361,180,0,1897,1893,1,0,0,0,1897,1894,1,0,0,0,1897,1895,1,0,0,0,1897,
		1896,1,0,0,0,1897,1898,1,0,0,0,1898,424,1,0,0,0,1899,1902,3,401,200,0,
		1900,1902,3,391,195,0,1901,1899,1,0,0,0,1901,1900,1,0,0,0,1901,1902,1,
		0,0,0,1902,1906,1,0,0,0,1903,1905,2,48,57,0,1904,1903,1,0,0,0,1905,1908,
		1,0,0,0,1906,1904,1,0,0,0,1906,1907,1,0,0,0,1907,1909,1,0,0,0,1908,1906,
		1,0,0,0,1909,1911,3,371,185,0,1910,1912,2,48,57,0,1911,1910,1,0,0,0,1912,
		1913,1,0,0,0,1913,1911,1,0,0,0,1913,1914,1,0,0,0,1914,1927,1,0,0,0,1915,
		1918,7,4,0,0,1916,1919,3,401,200,0,1917,1919,3,391,195,0,1918,1916,1,0,
		0,0,1918,1917,1,0,0,0,1918,1919,1,0,0,0,1919,1921,1,0,0,0,1920,1922,2,
		48,57,0,1921,1920,1,0,0,0,1922,1923,1,0,0,0,1923,1921,1,0,0,0,1923,1924,
		1,0,0,0,1924,1926,1,0,0,0,1925,1915,1,0,0,0,1926,1929,1,0,0,0,1927,1925,
		1,0,0,0,1927,1928,1,0,0,0,1928,1934,1,0,0,0,1929,1927,1,0,0,0,1930,1935,
		3,381,190,0,1931,1935,3,357,178,0,1932,1935,3,375,187,0,1933,1935,3,361,
		180,0,1934,1930,1,0,0,0,1934,1931,1,0,0,0,1934,1932,1,0,0,0,1934,1933,
		1,0,0,0,1934,1935,1,0,0,0,1935,426,1,0,0,0,1936,1938,3,381,190,0,1937,
		1939,3,447,223,0,1938,1937,1,0,0,0,1939,1940,1,0,0,0,1940,1938,1,0,0,0,
		1940,1941,1,0,0,0,1941,428,1,0,0,0,1942,1945,3,401,200,0,1943,1945,3,391,
		195,0,1944,1942,1,0,0,0,1944,1943,1,0,0,0,1944,1945,1,0,0,0,1945,1946,
		1,0,0,0,1946,1947,5,38,0,0,1947,1948,5,79,0,0,1948,1950,1,0,0,0,1949,1951,
		7,5,0,0,1950,1949,1,0,0,0,1951,1952,1,0,0,0,1952,1950,1,0,0,0,1952,1953,
		1,0,0,0,1953,1955,1,0,0,0,1954,1956,3,357,178,0,1955,1954,1,0,0,0,1955,
		1956,1,0,0,0,1956,430,1,0,0,0,1957,1959,3,363,181,0,1958,1960,7,3,0,0,
		1959,1958,1,0,0,0,1960,1961,1,0,0,0,1961,1959,1,0,0,0,1961,1962,1,0,0,
		0,1962,432,1,0,0,0,1963,1965,3,385,192,0,1964,1966,7,3,0,0,1965,1964,1,
		0,0,0,1966,1967,1,0,0,0,1967,1965,1,0,0,0,1967,1968,1,0,0,0,1968,1969,
		1,0,0,0,1969,1971,3,391,195,0,1970,1972,7,3,0,0,1971,1970,1,0,0,0,1972,
		1973,1,0,0,0,1973,1971,1,0,0,0,1973,1974,1,0,0,0,1974,1975,1,0,0,0,1975,
		1977,3,391,195,0,1976,1978,7,3,0,0,1977,1976,1,0,0,0,1978,1979,1,0,0,0,
		1979,1977,1,0,0,0,1979,1980,1,0,0,0,1980,1981,1,0,0,0,1981,1983,3,391,
		195,0,1982,1984,7,3,0,0,1983,1982,1,0,0,0,1984,1985,1,0,0,0,1985,1983,
		1,0,0,0,1985,1986,1,0,0,0,1986,1987,1,0,0,0,1987,1989,3,391,195,0,1988,
		1990,7,3,0,0,1989,1988,1,0,0,0,1990,1991,1,0,0,0,1991,1989,1,0,0,0,1991,
		1992,1,0,0,0,1992,1993,1,0,0,0,1993,1994,3,407,203,0,1994,434,1,0,0,0,
		1995,1999,3,445,222,0,1996,1998,3,447,223,0,1997,1996,1,0,0,0,1998,2001,
		1,0,0,0,1999,1997,1,0,0,0,1999,2000,1,0,0,0,2000,436,1,0,0,0,2001,1999,
		1,0,0,0,2002,2003,5,32,0,0,2003,2005,5,95,0,0,2004,2006,5,13,0,0,2005,
		2004,1,0,0,0,2005,2006,1,0,0,0,2006,2007,1,0,0,0,2007,2008,5,10,0,0,2008,
		2009,1,0,0,0,2009,2010,6,218,0,0,2010,438,1,0,0,0,2011,2013,3,443,221,
		0,2012,2011,1,0,0,0,2012,2013,1,0,0,0,2013,2021,1,0,0,0,2014,2016,5,13,
		0,0,2015,2014,1,0,0,0,2015,2016,1,0,0,0,2016,2017,1,0,0,0,2017,2022,5,
		10,0,0,2018,2019,3,363,181,0,2019,2020,5,32,0,0,2020,2022,1,0,0,0,2021,
		2015,1,0,0,0,2021,2018,1,0,0,0,2022,2024,1,0,0,0,2023,2025,3,443,221,0,
		2024,2023,1,0,0,0,2024,2025,1,0,0,0,2025,440,1,0,0,0,2026,2028,3,443,221,
		0,2027,2026,1,0,0,0,2027,2028,1,0,0,0,2028,2036,1,0,0,0,2029,2037,5,39,
		0,0,2030,2032,3,363,181,0,2031,2030,1,0,0,0,2031,2032,1,0,0,0,2032,2033,
		1,0,0,0,2033,2034,3,275,137,0,2034,2035,5,32,0,0,2035,2037,1,0,0,0,2036,
		2029,1,0,0,0,2036,2031,1,0,0,0,2037,2042,1,0,0,0,2038,2041,3,437,218,0,
		2039,2041,8,6,0,0,2040,2038,1,0,0,0,2040,2039,1,0,0,0,2041,2044,1,0,0,
		0,2042,2040,1,0,0,0,2042,2043,1,0,0,0,2043,2045,1,0,0,0,2044,2042,1,0,
		0,0,2045,2046,6,220,0,0,2046,442,1,0,0,0,2047,2049,7,7,0,0,2048,2047,1,
		0,0,0,2049,2050,1,0,0,0,2050,2048,1,0,0,0,2050,2051,1,0,0,0,2051,444,1,
		0,0,0,2052,2053,7,8,0,0,2053,446,1,0,0,0,2054,2055,7,9,0,0,2055,448,1,
		0,0,0,2056,2057,7,10,0,0,2057,450,1,0,0,0,2058,2059,7,11,0,0,2059,452,
		1,0,0,0,2060,2061,7,12,0,0,2061,454,1,0,0,0,2062,2063,7,13,0,0,2063,456,
		1,0,0,0,2064,2065,7,4,0,0,2065,458,1,0,0,0,2066,2067,7,14,0,0,2067,460,
		1,0,0,0,2068,2069,7,15,0,0,2069,462,1,0,0,0,2070,2071,7,16,0,0,2071,464,
		1,0,0,0,2072,2073,7,17,0,0,2073,466,1,0,0,0,2074,2075,7,18,0,0,2075,468,
		1,0,0,0,2076,2077,7,19,0,0,2077,470,1,0,0,0,2078,2079,7,20,0,0,2079,472,
		1,0,0,0,2080,2081,7,21,0,0,2081,474,1,0,0,0,2082,2083,7,22,0,0,2083,476,
		1,0,0,0,2084,2085,7,23,0,0,2085,478,1,0,0,0,2086,2087,7,24,0,0,2087,480,
		1,0,0,0,2088,2089,7,25,0,0,2089,482,1,0,0,0,2090,2091,7,26,0,0,2091,484,
		1,0,0,0,2092,2093,7,27,0,0,2093,486,1,0,0,0,2094,2095,7,28,0,0,2095,488,
		1,0,0,0,2096,2097,7,29,0,0,2097,490,1,0,0,0,2098,2099,7,30,0,0,2099,492,
		1,0,0,0,2100,2101,7,31,0,0,2101,494,1,0,0,0,2102,2103,7,32,0,0,2103,496,
		1,0,0,0,2104,2105,7,33,0,0,2105,498,1,0,0,0,2106,2107,7,34,0,0,2107,500,
		1,0,0,0,39,0,1850,1852,1861,1872,1875,1879,1884,1890,1897,1901,1906,1913,
		1918,1923,1927,1934,1940,1944,1952,1955,1961,1967,1973,1979,1985,1991,
		1999,2005,2012,2015,2021,2024,2027,2031,2036,2040,2042,2050,1,0,1,0
	};

	public static readonly ATN _ATN =
		new ATNDeserializer().Deserialize(_serializedATN);


}
