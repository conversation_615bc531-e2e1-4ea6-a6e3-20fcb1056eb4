//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/Interpreter/Grammar/VB6.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using Antlr4.Runtime.Misc;
using IParseTreeListener = Antlr4.Runtime.Tree.IParseTreeListener;
using IToken = Antlr4.Runtime.IToken;

/// <summary>
/// This interface defines a complete listener for a parse tree produced by
/// <see cref="VB6Parser"/>.
/// </summary>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public interface IVB6Listener : IParseTreeListener {
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterStartRule([NotNull] VB6Parser.StartRuleContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitStartRule([NotNull] VB6Parser.StartRuleContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.module"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModule([NotNull] VB6Parser.ModuleContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.module"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModule([NotNull] VB6Parser.ModuleContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferences([NotNull] VB6Parser.ModuleReferencesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferences([NotNull] VB6Parser.ModuleReferencesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReference([NotNull] VB6Parser.ModuleReferenceContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReference([NotNull] VB6Parser.ModuleReferenceContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferenceValue([NotNull] VB6Parser.ModuleReferenceValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferenceValue([NotNull] VB6Parser.ModuleReferenceValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleReferenceComponent([NotNull] VB6Parser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleReferenceComponent([NotNull] VB6Parser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleHeader([NotNull] VB6Parser.ModuleHeaderContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleHeader([NotNull] VB6Parser.ModuleHeaderContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleConfig([NotNull] VB6Parser.ModuleConfigContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleConfig([NotNull] VB6Parser.ModuleConfigContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleConfigElement([NotNull] VB6Parser.ModuleConfigElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleConfigElement([NotNull] VB6Parser.ModuleConfigElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleAttributes([NotNull] VB6Parser.ModuleAttributesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleAttributes([NotNull] VB6Parser.ModuleAttributesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleOptions"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleOptions([NotNull] VB6Parser.ModuleOptionsContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleOptions"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleOptions([NotNull] VB6Parser.ModuleOptionsContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionBaseStmt([NotNull] VB6Parser.OptionBaseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionBaseStmt([NotNull] VB6Parser.OptionBaseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionCompareStmt([NotNull] VB6Parser.OptionCompareStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionCompareStmt([NotNull] VB6Parser.OptionCompareStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionExplicitStmt([NotNull] VB6Parser.OptionExplicitStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionExplicitStmt([NotNull] VB6Parser.OptionExplicitStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOptionPrivateModuleStmt([NotNull] VB6Parser.OptionPrivateModuleStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOptionPrivateModuleStmt([NotNull] VB6Parser.OptionPrivateModuleStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleBody([NotNull] VB6Parser.ModuleBodyContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleBody([NotNull] VB6Parser.ModuleBodyContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleBodyElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleBodyElement([NotNull] VB6Parser.ModuleBodyElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleBodyElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleBodyElement([NotNull] VB6Parser.ModuleBodyElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterControlProperties([NotNull] VB6Parser.ControlPropertiesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitControlProperties([NotNull] VB6Parser.ControlPropertiesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_Properties([NotNull] VB6Parser.Cp_PropertiesContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_Properties([NotNull] VB6Parser.Cp_PropertiesContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_SingleProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_SingleProperty([NotNull] VB6Parser.Cp_SinglePropertyContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_SingleProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_SingleProperty([NotNull] VB6Parser.Cp_SinglePropertyContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_PropertyName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_PropertyName([NotNull] VB6Parser.Cp_PropertyNameContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_PropertyName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_PropertyName([NotNull] VB6Parser.Cp_PropertyNameContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_PropertyValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_PropertyValue([NotNull] VB6Parser.Cp_PropertyValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_PropertyValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_PropertyValue([NotNull] VB6Parser.Cp_PropertyValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_NestedProperty([NotNull] VB6Parser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_NestedProperty([NotNull] VB6Parser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_ControlType([NotNull] VB6Parser.Cp_ControlTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_ControlType([NotNull] VB6Parser.Cp_ControlTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCp_ControlIdentifier([NotNull] VB6Parser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCp_ControlIdentifier([NotNull] VB6Parser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.moduleBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterModuleBlock([NotNull] VB6Parser.ModuleBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.moduleBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitModuleBlock([NotNull] VB6Parser.ModuleBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAttributeStmt([NotNull] VB6Parser.AttributeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAttributeStmt([NotNull] VB6Parser.AttributeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.block"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlock([NotNull] VB6Parser.BlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.block"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlock([NotNull] VB6Parser.BlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.blockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlockStmt([NotNull] VB6Parser.BlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.blockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlockStmt([NotNull] VB6Parser.BlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.appActivateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAppActivateStmt([NotNull] VB6Parser.AppActivateStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.appActivateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAppActivateStmt([NotNull] VB6Parser.AppActivateStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.beepStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBeepStmt([NotNull] VB6Parser.BeepStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.beepStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBeepStmt([NotNull] VB6Parser.BeepStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.chDirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterChDirStmt([NotNull] VB6Parser.ChDirStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.chDirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitChDirStmt([NotNull] VB6Parser.ChDirStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.chDriveStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterChDriveStmt([NotNull] VB6Parser.ChDriveStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.chDriveStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitChDriveStmt([NotNull] VB6Parser.ChDriveStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.closeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCloseStmt([NotNull] VB6Parser.CloseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.closeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCloseStmt([NotNull] VB6Parser.CloseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.constStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterConstStmt([NotNull] VB6Parser.ConstStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.constStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitConstStmt([NotNull] VB6Parser.ConstStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.constSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterConstSubStmt([NotNull] VB6Parser.ConstSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.constSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitConstSubStmt([NotNull] VB6Parser.ConstSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.dateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDateStmt([NotNull] VB6Parser.DateStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.dateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDateStmt([NotNull] VB6Parser.DateStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.declareStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDeclareStmt([NotNull] VB6Parser.DeclareStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.declareStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDeclareStmt([NotNull] VB6Parser.DeclareStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.deftypeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDeftypeStmt([NotNull] VB6Parser.DeftypeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.deftypeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDeftypeStmt([NotNull] VB6Parser.DeftypeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.deleteSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDeleteSettingStmt([NotNull] VB6Parser.DeleteSettingStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.deleteSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDeleteSettingStmt([NotNull] VB6Parser.DeleteSettingStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>doBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDoBlockLoop([NotNull] VB6Parser.DoBlockLoopContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>doBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDoBlockLoop([NotNull] VB6Parser.DoBlockLoopContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>doWhileBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDoWhileBlockLoop([NotNull] VB6Parser.DoWhileBlockLoopContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>doWhileBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDoWhileBlockLoop([NotNull] VB6Parser.DoWhileBlockLoopContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>doBlockWhileLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDoBlockWhileLoop([NotNull] VB6Parser.DoBlockWhileLoopContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>doBlockWhileLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDoBlockWhileLoop([NotNull] VB6Parser.DoBlockWhileLoopContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.endStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEndStmt([NotNull] VB6Parser.EndStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.endStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEndStmt([NotNull] VB6Parser.EndStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.enumerationStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEnumerationStmt([NotNull] VB6Parser.EnumerationStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.enumerationStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEnumerationStmt([NotNull] VB6Parser.EnumerationStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.enumerationStmt_Constant"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEnumerationStmt_Constant([NotNull] VB6Parser.EnumerationStmt_ConstantContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.enumerationStmt_Constant"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEnumerationStmt_Constant([NotNull] VB6Parser.EnumerationStmt_ConstantContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eraseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEraseStmt([NotNull] VB6Parser.EraseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eraseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEraseStmt([NotNull] VB6Parser.EraseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.errorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterErrorStmt([NotNull] VB6Parser.ErrorStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.errorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitErrorStmt([NotNull] VB6Parser.ErrorStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterEventStmt([NotNull] VB6Parser.EventStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitEventStmt([NotNull] VB6Parser.EventStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.exitStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterExitStmt([NotNull] VB6Parser.ExitStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.exitStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitExitStmt([NotNull] VB6Parser.ExitStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.continueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterContinueStmt([NotNull] VB6Parser.ContinueStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.continueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitContinueStmt([NotNull] VB6Parser.ContinueStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.filecopyStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterFilecopyStmt([NotNull] VB6Parser.FilecopyStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.filecopyStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitFilecopyStmt([NotNull] VB6Parser.FilecopyStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.forEachStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterForEachStmt([NotNull] VB6Parser.ForEachStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.forEachStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitForEachStmt([NotNull] VB6Parser.ForEachStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.forNextStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterForNextStmt([NotNull] VB6Parser.ForNextStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.forNextStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitForNextStmt([NotNull] VB6Parser.ForNextStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.functionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterFunctionStmt([NotNull] VB6Parser.FunctionStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.functionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitFunctionStmt([NotNull] VB6Parser.FunctionStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.getStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterGetStmt([NotNull] VB6Parser.GetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.getStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitGetStmt([NotNull] VB6Parser.GetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.goSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterGoSubStmt([NotNull] VB6Parser.GoSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.goSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitGoSubStmt([NotNull] VB6Parser.GoSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.goToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterGoToStmt([NotNull] VB6Parser.GoToStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.goToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitGoToStmt([NotNull] VB6Parser.GoToStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInlineIfThenElse([NotNull] VB6Parser.InlineIfThenElseContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInlineIfThenElse([NotNull] VB6Parser.InlineIfThenElseContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBlockIfThenElse([NotNull] VB6Parser.BlockIfThenElseContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBlockIfThenElse([NotNull] VB6Parser.BlockIfThenElseContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfBlockStmt([NotNull] VB6Parser.IfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfBlockStmt([NotNull] VB6Parser.IfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfConditionStmt([NotNull] VB6Parser.IfConditionStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfConditionStmt([NotNull] VB6Parser.IfConditionStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfElseIfBlockStmt([NotNull] VB6Parser.IfElseIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfElseIfBlockStmt([NotNull] VB6Parser.IfElseIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ifElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterIfElseBlockStmt([NotNull] VB6Parser.IfElseBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ifElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitIfElseBlockStmt([NotNull] VB6Parser.IfElseBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.implementsStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterImplementsStmt([NotNull] VB6Parser.ImplementsStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.implementsStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitImplementsStmt([NotNull] VB6Parser.ImplementsStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.inputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterInputStmt([NotNull] VB6Parser.InputStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.inputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitInputStmt([NotNull] VB6Parser.InputStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.killStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterKillStmt([NotNull] VB6Parser.KillStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.killStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitKillStmt([NotNull] VB6Parser.KillStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.letStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLetStmt([NotNull] VB6Parser.LetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.letStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLetStmt([NotNull] VB6Parser.LetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lineInputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLineInputStmt([NotNull] VB6Parser.LineInputStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lineInputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLineInputStmt([NotNull] VB6Parser.LineInputStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.loadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLoadStmt([NotNull] VB6Parser.LoadStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.loadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLoadStmt([NotNull] VB6Parser.LoadStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLockStmt([NotNull] VB6Parser.LockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLockStmt([NotNull] VB6Parser.LockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLsetStmt([NotNull] VB6Parser.LsetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLsetStmt([NotNull] VB6Parser.LsetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfThenElseStmt([NotNull] VB6Parser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfThenElseStmt([NotNull] VB6Parser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroIfBlockStmt([NotNull] VB6Parser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroIfBlockStmt([NotNull] VB6Parser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroElseIfBlockStmt([NotNull] VB6Parser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroElseIfBlockStmt([NotNull] VB6Parser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMacroElseBlockStmt([NotNull] VB6Parser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMacroElseBlockStmt([NotNull] VB6Parser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.midStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMidStmt([NotNull] VB6Parser.MidStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.midStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMidStmt([NotNull] VB6Parser.MidStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.mkdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterMkdirStmt([NotNull] VB6Parser.MkdirStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.mkdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitMkdirStmt([NotNull] VB6Parser.MkdirStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.nameStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterNameStmt([NotNull] VB6Parser.NameStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.nameStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitNameStmt([NotNull] VB6Parser.NameStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.onErrorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOnErrorStmt([NotNull] VB6Parser.OnErrorStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.onErrorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOnErrorStmt([NotNull] VB6Parser.OnErrorStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.onGoToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOnGoToStmt([NotNull] VB6Parser.OnGoToStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.onGoToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOnGoToStmt([NotNull] VB6Parser.OnGoToStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.onGoSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOnGoSubStmt([NotNull] VB6Parser.OnGoSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.onGoSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOnGoSubStmt([NotNull] VB6Parser.OnGoSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.openStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOpenStmt([NotNull] VB6Parser.OpenStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.openStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOpenStmt([NotNull] VB6Parser.OpenStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.outputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOutputList([NotNull] VB6Parser.OutputListContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.outputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOutputList([NotNull] VB6Parser.OutputListContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.outputList_Expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterOutputList_Expression([NotNull] VB6Parser.OutputList_ExpressionContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.outputList_Expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitOutputList_Expression([NotNull] VB6Parser.OutputList_ExpressionContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.printStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPrintStmt([NotNull] VB6Parser.PrintStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.printStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPrintStmt([NotNull] VB6Parser.PrintStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.propertyGetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPropertyGetStmt([NotNull] VB6Parser.PropertyGetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.propertyGetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPropertyGetStmt([NotNull] VB6Parser.PropertyGetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.propertySetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPropertySetStmt([NotNull] VB6Parser.PropertySetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.propertySetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPropertySetStmt([NotNull] VB6Parser.PropertySetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.propertyLetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPropertyLetStmt([NotNull] VB6Parser.PropertyLetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.propertyLetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPropertyLetStmt([NotNull] VB6Parser.PropertyLetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.putStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPutStmt([NotNull] VB6Parser.PutStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.putStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPutStmt([NotNull] VB6Parser.PutStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.raiseEventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRaiseEventStmt([NotNull] VB6Parser.RaiseEventStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.raiseEventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRaiseEventStmt([NotNull] VB6Parser.RaiseEventStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.randomizeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRandomizeStmt([NotNull] VB6Parser.RandomizeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.randomizeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRandomizeStmt([NotNull] VB6Parser.RandomizeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.redimStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRedimStmt([NotNull] VB6Parser.RedimStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.redimStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRedimStmt([NotNull] VB6Parser.RedimStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.redimSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRedimSubStmt([NotNull] VB6Parser.RedimSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.redimSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRedimSubStmt([NotNull] VB6Parser.RedimSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.resetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterResetStmt([NotNull] VB6Parser.ResetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.resetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitResetStmt([NotNull] VB6Parser.ResetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.resumeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterResumeStmt([NotNull] VB6Parser.ResumeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.resumeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitResumeStmt([NotNull] VB6Parser.ResumeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.returnStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterReturnStmt([NotNull] VB6Parser.ReturnStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.returnStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitReturnStmt([NotNull] VB6Parser.ReturnStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.rmdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRmdirStmt([NotNull] VB6Parser.RmdirStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.rmdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRmdirStmt([NotNull] VB6Parser.RmdirStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.rsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterRsetStmt([NotNull] VB6Parser.RsetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.rsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitRsetStmt([NotNull] VB6Parser.RsetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.savepictureStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSavepictureStmt([NotNull] VB6Parser.SavepictureStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.savepictureStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSavepictureStmt([NotNull] VB6Parser.SavepictureStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.saveSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSaveSettingStmt([NotNull] VB6Parser.SaveSettingStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.saveSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSaveSettingStmt([NotNull] VB6Parser.SaveSettingStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.seekStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSeekStmt([NotNull] VB6Parser.SeekStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.seekStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSeekStmt([NotNull] VB6Parser.SeekStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.selectCaseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSelectCaseStmt([NotNull] VB6Parser.SelectCaseStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.selectCaseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSelectCaseStmt([NotNull] VB6Parser.SelectCaseStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.sC_Case"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSC_Case([NotNull] VB6Parser.SC_CaseContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.sC_Case"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSC_Case([NotNull] VB6Parser.SC_CaseContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondElse([NotNull] VB6Parser.CaseCondElseContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondElse([NotNull] VB6Parser.CaseCondElseContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExpr([NotNull] VB6Parser.CaseCondExprContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExpr([NotNull] VB6Parser.CaseCondExprContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExprIs([NotNull] VB6Parser.CaseCondExprIsContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExprIs([NotNull] VB6Parser.CaseCondExprIsContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExprValue([NotNull] VB6Parser.CaseCondExprValueContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExprValue([NotNull] VB6Parser.CaseCondExprValueContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCaseCondExprTo([NotNull] VB6Parser.CaseCondExprToContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCaseCondExprTo([NotNull] VB6Parser.CaseCondExprToContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.sendkeysStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSendkeysStmt([NotNull] VB6Parser.SendkeysStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.sendkeysStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSendkeysStmt([NotNull] VB6Parser.SendkeysStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.setattrStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSetattrStmt([NotNull] VB6Parser.SetattrStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.setattrStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSetattrStmt([NotNull] VB6Parser.SetattrStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.setStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSetStmt([NotNull] VB6Parser.SetStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.setStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSetStmt([NotNull] VB6Parser.SetStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.stopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterStopStmt([NotNull] VB6Parser.StopStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.stopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitStopStmt([NotNull] VB6Parser.StopStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.subStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSubStmt([NotNull] VB6Parser.SubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.subStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSubStmt([NotNull] VB6Parser.SubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.timeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTimeStmt([NotNull] VB6Parser.TimeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.timeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTimeStmt([NotNull] VB6Parser.TimeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeStmt([NotNull] VB6Parser.TypeStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeStmt([NotNull] VB6Parser.TypeStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeStmt_Element"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeStmt_Element([NotNull] VB6Parser.TypeStmt_ElementContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeStmt_Element"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeStmt_Element([NotNull] VB6Parser.TypeStmt_ElementContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeOfStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeOfStmt([NotNull] VB6Parser.TypeOfStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeOfStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeOfStmt([NotNull] VB6Parser.TypeOfStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.unloadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterUnloadStmt([NotNull] VB6Parser.UnloadStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.unloadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitUnloadStmt([NotNull] VB6Parser.UnloadStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.unlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterUnlockStmt([NotNull] VB6Parser.UnlockStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.unlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitUnlockStmt([NotNull] VB6Parser.UnlockStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsStruct([NotNull] VB6Parser.VsStructContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsStruct([NotNull] VB6Parser.VsStructContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAdd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAdd([NotNull] VB6Parser.VsAddContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAdd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAdd([NotNull] VB6Parser.VsAddContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsLt([NotNull] VB6Parser.VsLtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsLt([NotNull] VB6Parser.VsLtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAddressOf([NotNull] VB6Parser.VsAddressOfContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAddressOf([NotNull] VB6Parser.VsAddressOfContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsNew([NotNull] VB6Parser.VsNewContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsNew([NotNull] VB6Parser.VsNewContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMult</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsMult([NotNull] VB6Parser.VsMultContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMult</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsMult([NotNull] VB6Parser.VsMultContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNegation</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsNegation([NotNull] VB6Parser.VsNegationContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNegation</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsNegation([NotNull] VB6Parser.VsNegationContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAssign([NotNull] VB6Parser.VsAssignContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAssign([NotNull] VB6Parser.VsAssignContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsDiv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsDiv([NotNull] VB6Parser.VsDivContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsDiv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsDiv([NotNull] VB6Parser.VsDivContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLike</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsLike([NotNull] VB6Parser.VsLikeContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLike</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsLike([NotNull] VB6Parser.VsLikeContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsPlus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsPlus([NotNull] VB6Parser.VsPlusContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsPlus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsPlus([NotNull] VB6Parser.VsPlusContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNot</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsNot([NotNull] VB6Parser.VsNotContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNot</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsNot([NotNull] VB6Parser.VsNotContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsGeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsGeq([NotNull] VB6Parser.VsGeqContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsGeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsGeq([NotNull] VB6Parser.VsGeqContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsTypeOf([NotNull] VB6Parser.VsTypeOfContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsTypeOf([NotNull] VB6Parser.VsTypeOfContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsICS([NotNull] VB6Parser.VsICSContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsICS([NotNull] VB6Parser.VsICSContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsNeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsNeq([NotNull] VB6Parser.VsNeqContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsNeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsNeq([NotNull] VB6Parser.VsNeqContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsXor</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsXor([NotNull] VB6Parser.VsXorContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsXor</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsXor([NotNull] VB6Parser.VsXorContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAnd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAnd([NotNull] VB6Parser.VsAndContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAnd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAnd([NotNull] VB6Parser.VsAndContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsPow</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsPow([NotNull] VB6Parser.VsPowContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsPow</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsPow([NotNull] VB6Parser.VsPowContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsLeq([NotNull] VB6Parser.VsLeqContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsLeq([NotNull] VB6Parser.VsLeqContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsIs</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsIs([NotNull] VB6Parser.VsIsContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsIs</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsIs([NotNull] VB6Parser.VsIsContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMod</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsMod([NotNull] VB6Parser.VsModContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMod</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsMod([NotNull] VB6Parser.VsModContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsAmp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsAmp([NotNull] VB6Parser.VsAmpContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsAmp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsAmp([NotNull] VB6Parser.VsAmpContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsOr</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsOr([NotNull] VB6Parser.VsOrContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsOr</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsOr([NotNull] VB6Parser.VsOrContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMinus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsMinus([NotNull] VB6Parser.VsMinusContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMinus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsMinus([NotNull] VB6Parser.VsMinusContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsLiteral([NotNull] VB6Parser.VsLiteralContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsLiteral([NotNull] VB6Parser.VsLiteralContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsEqv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsEqv([NotNull] VB6Parser.VsEqvContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsEqv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsEqv([NotNull] VB6Parser.VsEqvContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsImp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsImp([NotNull] VB6Parser.VsImpContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsImp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsImp([NotNull] VB6Parser.VsImpContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsGt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsGt([NotNull] VB6Parser.VsGtContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsGt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsGt([NotNull] VB6Parser.VsGtContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsEq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsEq([NotNull] VB6Parser.VsEqContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsEq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsEq([NotNull] VB6Parser.VsEqContext context);
	/// <summary>
	/// Enter a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVsMid([NotNull] VB6Parser.VsMidContext context);
	/// <summary>
	/// Exit a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVsMid([NotNull] VB6Parser.VsMidContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.variableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVariableStmt([NotNull] VB6Parser.VariableStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.variableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVariableStmt([NotNull] VB6Parser.VariableStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.variableListStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVariableListStmt([NotNull] VB6Parser.VariableListStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.variableListStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVariableListStmt([NotNull] VB6Parser.VariableListStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.variableSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVariableSubStmt([NotNull] VB6Parser.VariableSubStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.variableSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVariableSubStmt([NotNull] VB6Parser.VariableSubStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.whileWendStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWhileWendStmt([NotNull] VB6Parser.WhileWendStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.whileWendStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWhileWendStmt([NotNull] VB6Parser.WhileWendStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.widthStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWidthStmt([NotNull] VB6Parser.WidthStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.widthStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWidthStmt([NotNull] VB6Parser.WidthStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.withStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWithStmt([NotNull] VB6Parser.WithStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.withStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWithStmt([NotNull] VB6Parser.WithStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.writeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterWriteStmt([NotNull] VB6Parser.WriteStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.writeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitWriteStmt([NotNull] VB6Parser.WriteStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.explicitCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterExplicitCallStmt([NotNull] VB6Parser.ExplicitCallStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.explicitCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitExplicitCallStmt([NotNull] VB6Parser.ExplicitCallStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eCS_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterECS_ProcedureCall([NotNull] VB6Parser.ECS_ProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eCS_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitECS_ProcedureCall([NotNull] VB6Parser.ECS_ProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.eCS_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterECS_MemberProcedureCall([NotNull] VB6Parser.ECS_MemberProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.eCS_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitECS_MemberProcedureCall([NotNull] VB6Parser.ECS_MemberProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterImplicitCallStmt_InBlock([NotNull] VB6Parser.ImplicitCallStmt_InBlockContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitImplicitCallStmt_InBlock([NotNull] VB6Parser.ImplicitCallStmt_InBlockContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_B_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_B_ProcedureCall([NotNull] VB6Parser.ICS_B_ProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_B_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_B_ProcedureCall([NotNull] VB6Parser.ICS_B_ProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_B_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_B_MemberProcedureCall([NotNull] VB6Parser.ICS_B_MemberProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_B_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_B_MemberProcedureCall([NotNull] VB6Parser.ICS_B_MemberProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterImplicitCallStmt_InStmt([NotNull] VB6Parser.ImplicitCallStmt_InStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitImplicitCallStmt_InStmt([NotNull] VB6Parser.ImplicitCallStmt_InStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_VariableOrProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_VariableOrProcedureCall([NotNull] VB6Parser.ICS_S_VariableOrProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_VariableOrProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_VariableOrProcedureCall([NotNull] VB6Parser.ICS_S_VariableOrProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_ProcedureOrArrayCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_ProcedureOrArrayCall([NotNull] VB6Parser.ICS_S_ProcedureOrArrayCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_ProcedureOrArrayCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_ProcedureOrArrayCall([NotNull] VB6Parser.ICS_S_ProcedureOrArrayCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_NestedProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_NestedProcedureCall([NotNull] VB6Parser.ICS_S_NestedProcedureCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_NestedProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_NestedProcedureCall([NotNull] VB6Parser.ICS_S_NestedProcedureCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_MembersCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_MembersCall([NotNull] VB6Parser.ICS_S_MembersCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_MembersCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_MembersCall([NotNull] VB6Parser.ICS_S_MembersCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_MemberCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_MemberCall([NotNull] VB6Parser.ICS_S_MemberCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_MemberCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_MemberCall([NotNull] VB6Parser.ICS_S_MemberCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.iCS_S_DictionaryCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterICS_S_DictionaryCall([NotNull] VB6Parser.ICS_S_DictionaryCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.iCS_S_DictionaryCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitICS_S_DictionaryCall([NotNull] VB6Parser.ICS_S_DictionaryCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argsCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgsCall([NotNull] VB6Parser.ArgsCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argsCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgsCall([NotNull] VB6Parser.ArgsCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgCall([NotNull] VB6Parser.ArgCallContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgCall([NotNull] VB6Parser.ArgCallContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.dictionaryCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterDictionaryCallStmt([NotNull] VB6Parser.DictionaryCallStmtContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.dictionaryCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitDictionaryCallStmt([NotNull] VB6Parser.DictionaryCallStmtContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgList([NotNull] VB6Parser.ArgListContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgList([NotNull] VB6Parser.ArgListContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.arg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArg([NotNull] VB6Parser.ArgContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.arg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArg([NotNull] VB6Parser.ArgContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.argDefaultValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterArgDefaultValue([NotNull] VB6Parser.ArgDefaultValueContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.argDefaultValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitArgDefaultValue([NotNull] VB6Parser.ArgDefaultValueContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.subscripts"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSubscripts([NotNull] VB6Parser.SubscriptsContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.subscripts"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSubscripts([NotNull] VB6Parser.SubscriptsContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.subscript"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterSubscript([NotNull] VB6Parser.SubscriptContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.subscript"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitSubscript([NotNull] VB6Parser.SubscriptContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAmbiguousIdentifier([NotNull] VB6Parser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAmbiguousIdentifier([NotNull] VB6Parser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.asTypeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAsTypeClause([NotNull] VB6Parser.AsTypeClauseContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.asTypeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAsTypeClause([NotNull] VB6Parser.AsTypeClauseContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.baseType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterBaseType([NotNull] VB6Parser.BaseTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.baseType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitBaseType([NotNull] VB6Parser.BaseTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.certainIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterCertainIdentifier([NotNull] VB6Parser.CertainIdentifierContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.certainIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitCertainIdentifier([NotNull] VB6Parser.CertainIdentifierContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.comparisonOperator"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterComparisonOperator([NotNull] VB6Parser.ComparisonOperatorContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.comparisonOperator"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitComparisonOperator([NotNull] VB6Parser.ComparisonOperatorContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterComplexType([NotNull] VB6Parser.ComplexTypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitComplexType([NotNull] VB6Parser.ComplexTypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.fieldLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterFieldLength([NotNull] VB6Parser.FieldLengthContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.fieldLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitFieldLength([NotNull] VB6Parser.FieldLengthContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.letterrange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLetterrange([NotNull] VB6Parser.LetterrangeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.letterrange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLetterrange([NotNull] VB6Parser.LetterrangeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLineLabel([NotNull] VB6Parser.LineLabelContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLineLabel([NotNull] VB6Parser.LineLabelContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterLiteral([NotNull] VB6Parser.LiteralContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitLiteral([NotNull] VB6Parser.LiteralContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.publicPrivateVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPublicPrivateVisibility([NotNull] VB6Parser.PublicPrivateVisibilityContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.publicPrivateVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPublicPrivateVisibility([NotNull] VB6Parser.PublicPrivateVisibilityContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.publicPrivateGlobalVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterPublicPrivateGlobalVisibility([NotNull] VB6Parser.PublicPrivateGlobalVisibilityContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.publicPrivateGlobalVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitPublicPrivateGlobalVisibility([NotNull] VB6Parser.PublicPrivateGlobalVisibilityContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.type"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterType([NotNull] VB6Parser.TypeContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.type"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitType([NotNull] VB6Parser.TypeContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.typeHint"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterTypeHint([NotNull] VB6Parser.TypeHintContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.typeHint"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitTypeHint([NotNull] VB6Parser.TypeHintContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.visibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterVisibility([NotNull] VB6Parser.VisibilityContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.visibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitVisibility([NotNull] VB6Parser.VisibilityContext context);
	/// <summary>
	/// Enter a parse tree produced by <see cref="VB6Parser.ambiguousKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void EnterAmbiguousKeyword([NotNull] VB6Parser.AmbiguousKeywordContext context);
	/// <summary>
	/// Exit a parse tree produced by <see cref="VB6Parser.ambiguousKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	void ExitAmbiguousKeyword([NotNull] VB6Parser.AmbiguousKeywordContext context);
}
