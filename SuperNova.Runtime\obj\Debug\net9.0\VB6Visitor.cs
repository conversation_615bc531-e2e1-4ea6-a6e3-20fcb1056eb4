//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     ANTLR Version: 4.13.1
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// Generated from G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova.Runtime/Interpreter/Grammar/VB6.g4 by ANTLR 4.13.1

// Unreachable code detected
#pragma warning disable 0162
// The variable '...' is assigned but its value is never used
#pragma warning disable 0219
// Missing XML comment for publicly visible type or member '...'
#pragma warning disable 1591
// Ambiguous reference in cref attribute
#pragma warning disable 419

using Antlr4.Runtime.Misc;
using Antlr4.Runtime.Tree;
using IToken = Antlr4.Runtime.IToken;

/// <summary>
/// This interface defines a complete generic visitor for a parse tree produced
/// by <see cref="VB6Parser"/>.
/// </summary>
/// <typeparam name="Result">The return type of the visit operation.</typeparam>
[System.CodeDom.Compiler.GeneratedCode("ANTLR", "4.13.1")]
[System.CLSCompliant(false)]
public interface IVB6Visitor<Result> : IParseTreeVisitor<Result> {
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.startRule"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStartRule([NotNull] VB6Parser.StartRuleContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.module"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModule([NotNull] VB6Parser.ModuleContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleReferences"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleReferences([NotNull] VB6Parser.ModuleReferencesContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleReference"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleReference([NotNull] VB6Parser.ModuleReferenceContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleReferenceValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleReferenceValue([NotNull] VB6Parser.ModuleReferenceValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleReferenceComponent"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleReferenceComponent([NotNull] VB6Parser.ModuleReferenceComponentContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleHeader"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleHeader([NotNull] VB6Parser.ModuleHeaderContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleConfig"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfig([NotNull] VB6Parser.ModuleConfigContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleConfigElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleConfigElement([NotNull] VB6Parser.ModuleConfigElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleAttributes"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleAttributes([NotNull] VB6Parser.ModuleAttributesContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleOptions"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleOptions([NotNull] VB6Parser.ModuleOptionsContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionBaseStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionBaseStmt([NotNull] VB6Parser.OptionBaseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionCompareStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionCompareStmt([NotNull] VB6Parser.OptionCompareStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionExplicitStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionExplicitStmt([NotNull] VB6Parser.OptionExplicitStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>optionPrivateModuleStmt</c>
	/// labeled alternative in <see cref="VB6Parser.moduleOption"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOptionPrivateModuleStmt([NotNull] VB6Parser.OptionPrivateModuleStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleBody"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleBody([NotNull] VB6Parser.ModuleBodyContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleBodyElement"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleBodyElement([NotNull] VB6Parser.ModuleBodyElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.controlProperties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitControlProperties([NotNull] VB6Parser.ControlPropertiesContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_Properties"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_Properties([NotNull] VB6Parser.Cp_PropertiesContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_SingleProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_SingleProperty([NotNull] VB6Parser.Cp_SinglePropertyContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_PropertyName"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_PropertyName([NotNull] VB6Parser.Cp_PropertyNameContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_PropertyValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_PropertyValue([NotNull] VB6Parser.Cp_PropertyValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_NestedProperty"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_NestedProperty([NotNull] VB6Parser.Cp_NestedPropertyContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_ControlType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_ControlType([NotNull] VB6Parser.Cp_ControlTypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.cp_ControlIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCp_ControlIdentifier([NotNull] VB6Parser.Cp_ControlIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.moduleBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitModuleBlock([NotNull] VB6Parser.ModuleBlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.attributeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAttributeStmt([NotNull] VB6Parser.AttributeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.block"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBlock([NotNull] VB6Parser.BlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.blockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBlockStmt([NotNull] VB6Parser.BlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.appActivateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAppActivateStmt([NotNull] VB6Parser.AppActivateStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.beepStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBeepStmt([NotNull] VB6Parser.BeepStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.chDirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitChDirStmt([NotNull] VB6Parser.ChDirStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.chDriveStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitChDriveStmt([NotNull] VB6Parser.ChDriveStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.closeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCloseStmt([NotNull] VB6Parser.CloseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.constStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitConstStmt([NotNull] VB6Parser.ConstStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.constSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitConstSubStmt([NotNull] VB6Parser.ConstSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.dateStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDateStmt([NotNull] VB6Parser.DateStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.declareStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDeclareStmt([NotNull] VB6Parser.DeclareStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.deftypeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDeftypeStmt([NotNull] VB6Parser.DeftypeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.deleteSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDeleteSettingStmt([NotNull] VB6Parser.DeleteSettingStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>doBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDoBlockLoop([NotNull] VB6Parser.DoBlockLoopContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>doWhileBlockLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDoWhileBlockLoop([NotNull] VB6Parser.DoWhileBlockLoopContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>doBlockWhileLoop</c>
	/// labeled alternative in <see cref="VB6Parser.doLoopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDoBlockWhileLoop([NotNull] VB6Parser.DoBlockWhileLoopContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.endStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEndStmt([NotNull] VB6Parser.EndStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.enumerationStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEnumerationStmt([NotNull] VB6Parser.EnumerationStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.enumerationStmt_Constant"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEnumerationStmt_Constant([NotNull] VB6Parser.EnumerationStmt_ConstantContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.eraseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEraseStmt([NotNull] VB6Parser.EraseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.errorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitErrorStmt([NotNull] VB6Parser.ErrorStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.eventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitEventStmt([NotNull] VB6Parser.EventStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.exitStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitExitStmt([NotNull] VB6Parser.ExitStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.continueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitContinueStmt([NotNull] VB6Parser.ContinueStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.filecopyStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFilecopyStmt([NotNull] VB6Parser.FilecopyStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.forEachStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitForEachStmt([NotNull] VB6Parser.ForEachStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.forNextStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitForNextStmt([NotNull] VB6Parser.ForNextStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.functionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFunctionStmt([NotNull] VB6Parser.FunctionStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.getStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGetStmt([NotNull] VB6Parser.GetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.goSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGoSubStmt([NotNull] VB6Parser.GoSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.goToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitGoToStmt([NotNull] VB6Parser.GoToStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>inlineIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitInlineIfThenElse([NotNull] VB6Parser.InlineIfThenElseContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>blockIfThenElse</c>
	/// labeled alternative in <see cref="VB6Parser.ifThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBlockIfThenElse([NotNull] VB6Parser.BlockIfThenElseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.ifBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfBlockStmt([NotNull] VB6Parser.IfBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.ifConditionStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfConditionStmt([NotNull] VB6Parser.IfConditionStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.ifElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfElseIfBlockStmt([NotNull] VB6Parser.IfElseIfBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.ifElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitIfElseBlockStmt([NotNull] VB6Parser.IfElseBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.implementsStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitImplementsStmt([NotNull] VB6Parser.ImplementsStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.inputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitInputStmt([NotNull] VB6Parser.InputStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.killStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitKillStmt([NotNull] VB6Parser.KillStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.letStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLetStmt([NotNull] VB6Parser.LetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.lineInputStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineInputStmt([NotNull] VB6Parser.LineInputStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.loadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLoadStmt([NotNull] VB6Parser.LoadStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.lockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLockStmt([NotNull] VB6Parser.LockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.lsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLsetStmt([NotNull] VB6Parser.LsetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.macroIfThenElseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMacroIfThenElseStmt([NotNull] VB6Parser.MacroIfThenElseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.macroIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMacroIfBlockStmt([NotNull] VB6Parser.MacroIfBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.macroElseIfBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMacroElseIfBlockStmt([NotNull] VB6Parser.MacroElseIfBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.macroElseBlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMacroElseBlockStmt([NotNull] VB6Parser.MacroElseBlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.midStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMidStmt([NotNull] VB6Parser.MidStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.mkdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitMkdirStmt([NotNull] VB6Parser.MkdirStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.nameStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitNameStmt([NotNull] VB6Parser.NameStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.onErrorStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOnErrorStmt([NotNull] VB6Parser.OnErrorStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.onGoToStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOnGoToStmt([NotNull] VB6Parser.OnGoToStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.onGoSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOnGoSubStmt([NotNull] VB6Parser.OnGoSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.openStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOpenStmt([NotNull] VB6Parser.OpenStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.outputList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOutputList([NotNull] VB6Parser.OutputListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.outputList_Expression"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitOutputList_Expression([NotNull] VB6Parser.OutputList_ExpressionContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.printStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPrintStmt([NotNull] VB6Parser.PrintStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.propertyGetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPropertyGetStmt([NotNull] VB6Parser.PropertyGetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.propertySetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPropertySetStmt([NotNull] VB6Parser.PropertySetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.propertyLetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPropertyLetStmt([NotNull] VB6Parser.PropertyLetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.putStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPutStmt([NotNull] VB6Parser.PutStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.raiseEventStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRaiseEventStmt([NotNull] VB6Parser.RaiseEventStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.randomizeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRandomizeStmt([NotNull] VB6Parser.RandomizeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.redimStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRedimStmt([NotNull] VB6Parser.RedimStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.redimSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRedimSubStmt([NotNull] VB6Parser.RedimSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.resetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitResetStmt([NotNull] VB6Parser.ResetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.resumeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitResumeStmt([NotNull] VB6Parser.ResumeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.returnStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitReturnStmt([NotNull] VB6Parser.ReturnStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.rmdirStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRmdirStmt([NotNull] VB6Parser.RmdirStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.rsetStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitRsetStmt([NotNull] VB6Parser.RsetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.savepictureStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSavepictureStmt([NotNull] VB6Parser.SavepictureStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.saveSettingStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSaveSettingStmt([NotNull] VB6Parser.SaveSettingStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.seekStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSeekStmt([NotNull] VB6Parser.SeekStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.selectCaseStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSelectCaseStmt([NotNull] VB6Parser.SelectCaseStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.sC_Case"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSC_Case([NotNull] VB6Parser.SC_CaseContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>caseCondElse</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseCondElse([NotNull] VB6Parser.CaseCondElseContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>caseCondExpr</c>
	/// labeled alternative in <see cref="VB6Parser.sC_Cond"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseCondExpr([NotNull] VB6Parser.CaseCondExprContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>caseCondExprIs</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseCondExprIs([NotNull] VB6Parser.CaseCondExprIsContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>caseCondExprValue</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseCondExprValue([NotNull] VB6Parser.CaseCondExprValueContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>caseCondExprTo</c>
	/// labeled alternative in <see cref="VB6Parser.sC_CondExpr"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCaseCondExprTo([NotNull] VB6Parser.CaseCondExprToContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.sendkeysStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSendkeysStmt([NotNull] VB6Parser.SendkeysStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.setattrStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSetattrStmt([NotNull] VB6Parser.SetattrStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.setStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSetStmt([NotNull] VB6Parser.SetStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.stopStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitStopStmt([NotNull] VB6Parser.StopStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.subStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSubStmt([NotNull] VB6Parser.SubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.timeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTimeStmt([NotNull] VB6Parser.TimeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.typeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypeStmt([NotNull] VB6Parser.TypeStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.typeStmt_Element"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypeStmt_Element([NotNull] VB6Parser.TypeStmt_ElementContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.typeOfStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypeOfStmt([NotNull] VB6Parser.TypeOfStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.unloadStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnloadStmt([NotNull] VB6Parser.UnloadStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.unlockStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitUnlockStmt([NotNull] VB6Parser.UnlockStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsStruct</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsStruct([NotNull] VB6Parser.VsStructContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsAdd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsAdd([NotNull] VB6Parser.VsAddContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsLt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsLt([NotNull] VB6Parser.VsLtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsAddressOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsAddressOf([NotNull] VB6Parser.VsAddressOfContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsNew</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsNew([NotNull] VB6Parser.VsNewContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsMult</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsMult([NotNull] VB6Parser.VsMultContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsNegation</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsNegation([NotNull] VB6Parser.VsNegationContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsAssign</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsAssign([NotNull] VB6Parser.VsAssignContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsDiv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsDiv([NotNull] VB6Parser.VsDivContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsLike</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsLike([NotNull] VB6Parser.VsLikeContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsPlus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsPlus([NotNull] VB6Parser.VsPlusContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsNot</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsNot([NotNull] VB6Parser.VsNotContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsGeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsGeq([NotNull] VB6Parser.VsGeqContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsTypeOf</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsTypeOf([NotNull] VB6Parser.VsTypeOfContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsICS</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsICS([NotNull] VB6Parser.VsICSContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsNeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsNeq([NotNull] VB6Parser.VsNeqContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsXor</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsXor([NotNull] VB6Parser.VsXorContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsAnd</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsAnd([NotNull] VB6Parser.VsAndContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsPow</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsPow([NotNull] VB6Parser.VsPowContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsLeq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsLeq([NotNull] VB6Parser.VsLeqContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsIs</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsIs([NotNull] VB6Parser.VsIsContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsMod</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsMod([NotNull] VB6Parser.VsModContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsAmp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsAmp([NotNull] VB6Parser.VsAmpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsOr</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsOr([NotNull] VB6Parser.VsOrContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsMinus</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsMinus([NotNull] VB6Parser.VsMinusContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsLiteral</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsLiteral([NotNull] VB6Parser.VsLiteralContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsEqv</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsEqv([NotNull] VB6Parser.VsEqvContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsImp</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsImp([NotNull] VB6Parser.VsImpContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsGt</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsGt([NotNull] VB6Parser.VsGtContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsEq</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsEq([NotNull] VB6Parser.VsEqContext context);
	/// <summary>
	/// Visit a parse tree produced by the <c>vsMid</c>
	/// labeled alternative in <see cref="VB6Parser.valueStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVsMid([NotNull] VB6Parser.VsMidContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.variableStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableStmt([NotNull] VB6Parser.VariableStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.variableListStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableListStmt([NotNull] VB6Parser.VariableListStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.variableSubStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVariableSubStmt([NotNull] VB6Parser.VariableSubStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.whileWendStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWhileWendStmt([NotNull] VB6Parser.WhileWendStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.widthStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWidthStmt([NotNull] VB6Parser.WidthStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.withStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWithStmt([NotNull] VB6Parser.WithStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.writeStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitWriteStmt([NotNull] VB6Parser.WriteStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.explicitCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitExplicitCallStmt([NotNull] VB6Parser.ExplicitCallStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.eCS_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitECS_ProcedureCall([NotNull] VB6Parser.ECS_ProcedureCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.eCS_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitECS_MemberProcedureCall([NotNull] VB6Parser.ECS_MemberProcedureCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InBlock"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitImplicitCallStmt_InBlock([NotNull] VB6Parser.ImplicitCallStmt_InBlockContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_B_ProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_B_ProcedureCall([NotNull] VB6Parser.ICS_B_ProcedureCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_B_MemberProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_B_MemberProcedureCall([NotNull] VB6Parser.ICS_B_MemberProcedureCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.implicitCallStmt_InStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitImplicitCallStmt_InStmt([NotNull] VB6Parser.ImplicitCallStmt_InStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_S_VariableOrProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_S_VariableOrProcedureCall([NotNull] VB6Parser.ICS_S_VariableOrProcedureCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_S_ProcedureOrArrayCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_S_ProcedureOrArrayCall([NotNull] VB6Parser.ICS_S_ProcedureOrArrayCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_S_NestedProcedureCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_S_NestedProcedureCall([NotNull] VB6Parser.ICS_S_NestedProcedureCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_S_MembersCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_S_MembersCall([NotNull] VB6Parser.ICS_S_MembersCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_S_MemberCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_S_MemberCall([NotNull] VB6Parser.ICS_S_MemberCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.iCS_S_DictionaryCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitICS_S_DictionaryCall([NotNull] VB6Parser.ICS_S_DictionaryCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.argsCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgsCall([NotNull] VB6Parser.ArgsCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.argCall"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgCall([NotNull] VB6Parser.ArgCallContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.dictionaryCallStmt"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitDictionaryCallStmt([NotNull] VB6Parser.DictionaryCallStmtContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.argList"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgList([NotNull] VB6Parser.ArgListContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.arg"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArg([NotNull] VB6Parser.ArgContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.argDefaultValue"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitArgDefaultValue([NotNull] VB6Parser.ArgDefaultValueContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.subscripts"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSubscripts([NotNull] VB6Parser.SubscriptsContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.subscript"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitSubscript([NotNull] VB6Parser.SubscriptContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.ambiguousIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAmbiguousIdentifier([NotNull] VB6Parser.AmbiguousIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.asTypeClause"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAsTypeClause([NotNull] VB6Parser.AsTypeClauseContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.baseType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitBaseType([NotNull] VB6Parser.BaseTypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.certainIdentifier"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitCertainIdentifier([NotNull] VB6Parser.CertainIdentifierContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.comparisonOperator"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitComparisonOperator([NotNull] VB6Parser.ComparisonOperatorContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.complexType"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitComplexType([NotNull] VB6Parser.ComplexTypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.fieldLength"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitFieldLength([NotNull] VB6Parser.FieldLengthContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.letterrange"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLetterrange([NotNull] VB6Parser.LetterrangeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.lineLabel"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLineLabel([NotNull] VB6Parser.LineLabelContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.literal"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitLiteral([NotNull] VB6Parser.LiteralContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.publicPrivateVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPublicPrivateVisibility([NotNull] VB6Parser.PublicPrivateVisibilityContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.publicPrivateGlobalVisibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitPublicPrivateGlobalVisibility([NotNull] VB6Parser.PublicPrivateGlobalVisibilityContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.type"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitType([NotNull] VB6Parser.TypeContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.typeHint"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitTypeHint([NotNull] VB6Parser.TypeHintContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.visibility"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitVisibility([NotNull] VB6Parser.VisibilityContext context);
	/// <summary>
	/// Visit a parse tree produced by <see cref="VB6Parser.ambiguousKeyword"/>.
	/// </summary>
	/// <param name="context">The parse tree.</param>
	/// <return>The visitor result.</return>
	Result VisitAmbiguousKeyword([NotNull] VB6Parser.AmbiguousKeywordContext context);
}
