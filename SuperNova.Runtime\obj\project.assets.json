{"version": 3, "targets": {"net9.0": {"Antlr4.Runtime.Standard/4.13.1": {"type": "package", "compile": {"lib/netstandard2.0/Antlr4.Runtime.Standard.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Antlr4.Runtime.Standard.dll": {"related": ".xml"}}}, "Antlr4BuildTasks/12.8.0": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Utilities.Core": "17.8.3"}, "compile": {"lib/netstandard2.0/Antlr4BuildTasks.dll": {}}, "runtime": {"lib/netstandard2.0/Antlr4BuildTasks.dll": {}}, "build": {"build/Antlr4BuildTasks.props": {}, "build/Antlr4BuildTasks.targets": {}}}, "Avalonia/11.2.3": {"type": "package", "dependencies": {"Avalonia.BuildServices": "0.0.29", "Avalonia.Remote.Protocol": "11.2.3", "MicroCom.Runtime": "0.11.0"}, "compile": {"ref/net8.0/Avalonia.Base.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Controls.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "ref/net8.0/Avalonia.Metal.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.Vulkan.dll": {"related": ".xml"}, "ref/net8.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.Vulkan.xml;.xml"}}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Controls.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "lib/net8.0/Avalonia.Metal.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.Vulkan.dll": {"related": ".xml"}, "lib/net8.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.Vulkan.xml;.xml"}}, "build": {"buildTransitive/Avalonia.props": {}, "buildTransitive/Avalonia.targets": {}}}, "Avalonia.BuildServices/0.0.29": {"type": "package", "build": {"buildTransitive/Avalonia.BuildServices.targets": {}}}, "Avalonia.Controls.ColorPicker/11.2.3": {"type": "package", "dependencies": {"Avalonia": "11.2.3", "Avalonia.Remote.Protocol": "11.2.3"}, "compile": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"related": ".xml"}}}, "Avalonia.Controls.DataGrid/11.2.3": {"type": "package", "dependencies": {"Avalonia": "11.2.3", "Avalonia.Remote.Protocol": "11.2.3"}, "compile": {"lib/net8.0/Avalonia.Controls.DataGrid.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Controls.DataGrid.dll": {"related": ".xml"}}}, "Avalonia.Diagnostics/11.2.3": {"type": "package", "dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.ColorPicker": "11.2.3", "Avalonia.Controls.DataGrid": "11.2.3", "Avalonia.Themes.Simple": "11.2.3"}, "compile": {"lib/net8.0/Avalonia.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"related": ".xml"}}}, "Avalonia.Remote.Protocol/11.2.3": {"type": "package", "compile": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}}, "Avalonia.Skia/11.2.0": {"type": "package", "dependencies": {"Avalonia": "11.2.0", "HarfBuzzSharp": "7.3.0.2", "HarfBuzzSharp.NativeAssets.Linux": "7.3.0.2", "HarfBuzzSharp.NativeAssets.WebAssembly": "7.3.0.3-preview.2.2", "SkiaSharp": "2.88.8", "SkiaSharp.NativeAssets.Linux": "2.88.8", "SkiaSharp.NativeAssets.WebAssembly": "2.88.8"}, "compile": {"lib/net8.0/Avalonia.Skia.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"related": ".xml"}}}, "Avalonia.Themes.Simple/11.2.3": {"type": "package", "dependencies": {"Avalonia": "11.2.3"}, "compile": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"related": ".xml"}}}, "Classic.Avalonia.Theme/11.2.0": {"type": "package", "dependencies": {"Avalonia": "11.2.0", "Classic.CommonControls.Avalonia": "11.2.0"}, "compile": {"lib/netstandard2.0/Classic.Avalonia.Theme.dll": {}}, "runtime": {"lib/netstandard2.0/Classic.Avalonia.Theme.dll": {}}}, "Classic.CommonControls.Avalonia/11.2.0": {"type": "package", "dependencies": {"Avalonia": "11.2.0", "Avalonia.Skia": "11.2.0"}, "compile": {"lib/netstandard2.0/Classic.CommonControls.Avalonia.dll": {}}, "runtime": {"lib/netstandard2.0/Classic.CommonControls.Avalonia.dll": {}}}, "HarfBuzzSharp/7.3.0.2": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.2", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.2"}, "compile": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.2": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0.2"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0.3-preview.2.2": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "LanguageExt.Core/4.4.9": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0"}, "compile": {"lib/netstandard2.0/LanguageExt.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/LanguageExt.Core.dll": {"related": ".xml"}}}, "MicroCom.Runtime/0.11.0": {"type": "package", "compile": {"lib/net5.0/MicroCom.Runtime.dll": {}}, "runtime": {"lib/net5.0/MicroCom.Runtime.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "compile": {"ref/net8.0/Microsoft.Build.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Build.Framework.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Build.Utilities.Core/17.8.3": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.NET.StringTools": "17.8.3", "Microsoft.VisualStudio.Setup.Configuration.Interop": "3.2.2146", "System.Configuration.ConfigurationManager": "7.0.0"}, "compile": {"ref/net8.0/Microsoft.Build.Utilities.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Build.Utilities.Core.dll": {"related": ".pdb;.xml"}}}, "Microsoft.NET.StringTools/17.8.3": {"type": "package", "compile": {"ref/net8.0/Microsoft.NET.StringTools.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"related": ".pdb;.xml"}}}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "PropertyChanged.SourceGenerator/1.0.8": {"type": "package"}, "SkiaSharp/2.88.8": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"type": "package", "dependencies": {"SkiaSharp": "2.88.8"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "compile": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/7.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "compile": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/7.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "7.0.0"}, "compile": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Windows.Extensions/7.0.0": {"type": "package", "dependencies": {"System.Drawing.Common": "7.0.0"}, "compile": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Antlr4.Runtime.Standard/4.13.1": {"sha512": "Da5+i4kFHUseJRZGcBG5fmZGpA/Ns180ibrQMxgZzjpQOnENVvSL5gi5HZ8Ncz8/AR2WsKbOg2lMBzjz0HUQcA==", "type": "package", "path": "antlr4.runtime.standard/4.13.1", "files": [".nupkg.metadata", ".signature.p7s", "antlr4.runtime.standard.4.13.1.nupkg.sha512", "antlr4.runtime.standard.nuspec", "lib/net45/Antlr4.Runtime.Standard.dll", "lib/net45/Antlr4.Runtime.Standard.xml", "lib/netstandard2.0/Antlr4.Runtime.Standard.dll", "lib/netstandard2.0/Antlr4.Runtime.Standard.xml"]}, "Antlr4BuildTasks/12.8.0": {"sha512": "ZIcmPs7ykc6WDtuHSBvV5wZ1BkuR6DHbZnCrUWVAx3F5Lg4LcxHlzQEE0hB7NdyXhOoB3ykcyQ7EY88EzSflVQ==", "type": "package", "path": "antlr4buildtasks/12.8.0", "files": [".nupkg.metadata", ".signature.p7s", "antlr4buildtasks.12.8.0.nupkg.sha512", "antlr4buildtasks.nuspec", "build/Antlr4BuildTasks.props", "build/Antlr4BuildTasks.targets", "build/Antlr4BuildTasks.xml", "icon.png", "lib/netstandard2.0/Antlr4BuildTasks.dll"]}, "Avalonia/11.2.3": {"sha512": "pD6woFAUfGcyEvMmrpctntU4jv4fT8752pfx1J5iRORVX3Ob0oQi8PWo0TXVaAJZiSfH0cdKTeKx0w0DzD0/mg==", "type": "package", "path": "avalonia/11.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Avalonia.Analyzers.dll", "analyzers/dotnet/cs/Avalonia.Generators.dll", "avalonia.11.2.3.nupkg.sha512", "avalonia.nuspec", "build/Avalonia.Generators.props", "build/Avalonia.props", "build/Avalonia.targets", "build/AvaloniaBuildTasks.props", "build/AvaloniaBuildTasks.targets", "build/AvaloniaItemSchema.xaml", "build/AvaloniaPrivateApis.targets", "build/AvaloniaRules.Project.xml", "build/AvaloniaSingleProject.targets", "build/AvaloniaVersion.props", "buildTransitive/Avalonia.Generators.props", "buildTransitive/Avalonia.props", "buildTransitive/Avalonia.targets", "buildTransitive/AvaloniaBuildTasks.props", "buildTransitive/AvaloniaBuildTasks.targets", "buildTransitive/AvaloniaItemSchema.xaml", "buildTransitive/AvaloniaPrivateApis.targets", "buildTransitive/AvaloniaRules.Project.xml", "buildTransitive/AvaloniaSingleProject.targets", "lib/net6.0/Avalonia.Base.dll", "lib/net6.0/Avalonia.Base.xml", "lib/net6.0/Avalonia.Controls.dll", "lib/net6.0/Avalonia.Controls.xml", "lib/net6.0/Avalonia.DesignerSupport.dll", "lib/net6.0/Avalonia.DesignerSupport.xml", "lib/net6.0/Avalonia.Dialogs.dll", "lib/net6.0/Avalonia.Dialogs.xml", "lib/net6.0/Avalonia.Markup.Xaml.dll", "lib/net6.0/Avalonia.Markup.Xaml.xml", "lib/net6.0/Avalonia.Markup.dll", "lib/net6.0/Avalonia.Markup.xml", "lib/net6.0/Avalonia.Metal.dll", "lib/net6.0/Avalonia.Metal.xml", "lib/net6.0/Avalonia.MicroCom.dll", "lib/net6.0/Avalonia.MicroCom.xml", "lib/net6.0/Avalonia.OpenGL.dll", "lib/net6.0/Avalonia.OpenGL.xml", "lib/net6.0/Avalonia.Vulkan.dll", "lib/net6.0/Avalonia.Vulkan.xml", "lib/net6.0/Avalonia.dll", "lib/net6.0/Avalonia.xml", "lib/net8.0/Avalonia.Base.dll", "lib/net8.0/Avalonia.Base.xml", "lib/net8.0/Avalonia.Controls.dll", "lib/net8.0/Avalonia.Controls.xml", "lib/net8.0/Avalonia.DesignerSupport.dll", "lib/net8.0/Avalonia.DesignerSupport.xml", "lib/net8.0/Avalonia.Dialogs.dll", "lib/net8.0/Avalonia.Dialogs.xml", "lib/net8.0/Avalonia.Markup.Xaml.dll", "lib/net8.0/Avalonia.Markup.Xaml.xml", "lib/net8.0/Avalonia.Markup.dll", "lib/net8.0/Avalonia.Markup.xml", "lib/net8.0/Avalonia.Metal.dll", "lib/net8.0/Avalonia.Metal.xml", "lib/net8.0/Avalonia.MicroCom.dll", "lib/net8.0/Avalonia.MicroCom.xml", "lib/net8.0/Avalonia.OpenGL.dll", "lib/net8.0/Avalonia.OpenGL.xml", "lib/net8.0/Avalonia.Vulkan.dll", "lib/net8.0/Avalonia.Vulkan.xml", "lib/net8.0/Avalonia.dll", "lib/net8.0/Avalonia.xml", "lib/netstandard2.0/Avalonia.Base.dll", "lib/netstandard2.0/Avalonia.Base.xml", "lib/netstandard2.0/Avalonia.Controls.dll", "lib/netstandard2.0/Avalonia.Controls.xml", "lib/netstandard2.0/Avalonia.DesignerSupport.dll", "lib/netstandard2.0/Avalonia.DesignerSupport.xml", "lib/netstandard2.0/Avalonia.Dialogs.dll", "lib/netstandard2.0/Avalonia.Dialogs.xml", "lib/netstandard2.0/Avalonia.Markup.Xaml.dll", "lib/netstandard2.0/Avalonia.Markup.Xaml.xml", "lib/netstandard2.0/Avalonia.Markup.dll", "lib/netstandard2.0/Avalonia.Markup.xml", "lib/netstandard2.0/Avalonia.Metal.dll", "lib/netstandard2.0/Avalonia.Metal.xml", "lib/netstandard2.0/Avalonia.MicroCom.dll", "lib/netstandard2.0/Avalonia.MicroCom.xml", "lib/netstandard2.0/Avalonia.OpenGL.dll", "lib/netstandard2.0/Avalonia.OpenGL.xml", "lib/netstandard2.0/Avalonia.Vulkan.dll", "lib/netstandard2.0/Avalonia.Vulkan.xml", "lib/netstandard2.0/Avalonia.dll", "lib/netstandard2.0/Avalonia.xml", "ref/net6.0/Avalonia.Base.dll", "ref/net6.0/Avalonia.Base.xml", "ref/net6.0/Avalonia.Controls.dll", "ref/net6.0/Avalonia.Controls.xml", "ref/net6.0/Avalonia.DesignerSupport.dll", "ref/net6.0/Avalonia.DesignerSupport.xml", "ref/net6.0/Avalonia.Dialogs.dll", "ref/net6.0/Avalonia.Dialogs.xml", "ref/net6.0/Avalonia.Markup.Xaml.dll", "ref/net6.0/Avalonia.Markup.Xaml.xml", "ref/net6.0/Avalonia.Markup.dll", "ref/net6.0/Avalonia.Markup.xml", "ref/net6.0/Avalonia.Metal.dll", "ref/net6.0/Avalonia.Metal.xml", "ref/net6.0/Avalonia.MicroCom.dll", "ref/net6.0/Avalonia.MicroCom.xml", "ref/net6.0/Avalonia.OpenGL.dll", "ref/net6.0/Avalonia.OpenGL.xml", "ref/net6.0/Avalonia.Vulkan.dll", "ref/net6.0/Avalonia.Vulkan.xml", "ref/net6.0/Avalonia.dll", "ref/net6.0/Avalonia.xml", "ref/net8.0/Avalonia.Base.dll", "ref/net8.0/Avalonia.Base.xml", "ref/net8.0/Avalonia.Controls.dll", "ref/net8.0/Avalonia.Controls.xml", "ref/net8.0/Avalonia.DesignerSupport.dll", "ref/net8.0/Avalonia.DesignerSupport.xml", "ref/net8.0/Avalonia.Dialogs.dll", "ref/net8.0/Avalonia.Dialogs.xml", "ref/net8.0/Avalonia.Markup.Xaml.dll", "ref/net8.0/Avalonia.Markup.Xaml.xml", "ref/net8.0/Avalonia.Markup.dll", "ref/net8.0/Avalonia.Markup.xml", "ref/net8.0/Avalonia.Metal.dll", "ref/net8.0/Avalonia.Metal.xml", "ref/net8.0/Avalonia.MicroCom.dll", "ref/net8.0/Avalonia.MicroCom.xml", "ref/net8.0/Avalonia.OpenGL.dll", "ref/net8.0/Avalonia.OpenGL.xml", "ref/net8.0/Avalonia.Vulkan.dll", "ref/net8.0/Avalonia.Vulkan.xml", "ref/net8.0/Avalonia.dll", "ref/net8.0/Avalonia.xml", "ref/netstandard2.0/Avalonia.Base.dll", "ref/netstandard2.0/Avalonia.Base.xml", "ref/netstandard2.0/Avalonia.Controls.dll", "ref/netstandard2.0/Avalonia.Controls.xml", "ref/netstandard2.0/Avalonia.DesignerSupport.dll", "ref/netstandard2.0/Avalonia.DesignerSupport.xml", "ref/netstandard2.0/Avalonia.Dialogs.dll", "ref/netstandard2.0/Avalonia.Dialogs.xml", "ref/netstandard2.0/Avalonia.Markup.Xaml.dll", "ref/netstandard2.0/Avalonia.Markup.Xaml.xml", "ref/netstandard2.0/Avalonia.Markup.dll", "ref/netstandard2.0/Avalonia.Markup.xml", "ref/netstandard2.0/Avalonia.Metal.dll", "ref/netstandard2.0/Avalonia.Metal.xml", "ref/netstandard2.0/Avalonia.MicroCom.dll", "ref/netstandard2.0/Avalonia.MicroCom.xml", "ref/netstandard2.0/Avalonia.OpenGL.dll", "ref/netstandard2.0/Avalonia.OpenGL.xml", "ref/netstandard2.0/Avalonia.Vulkan.dll", "ref/netstandard2.0/Avalonia.Vulkan.xml", "ref/netstandard2.0/Avalonia.dll", "ref/netstandard2.0/Avalonia.xml", "tools/net461/designer/Avalonia.Designer.HostApp.exe", "tools/netstandard2.0/Avalonia.Build.Tasks.dll", "tools/netstandard2.0/designer/Avalonia.Designer.HostApp.dll"]}, "Avalonia.BuildServices/0.0.29": {"sha512": "U4eJLQdoDNHXtEba7MZUCwrBErBTxFp6sUewXBOdAhU0Kwzwaa/EKFcYm8kpcysjzKtfB4S0S9n0uxKZFz/ikw==", "type": "package", "path": "avalonia.buildservices/0.0.29", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "avalonia.buildservices.0.0.29.nupkg.sha512", "avalonia.buildservices.nuspec", "build/Avalonia.BuildServices.targets", "buildTransitive/Avalonia.BuildServices.targets", "tools/netstandard2.0/Avalonia.BuildServices.Collector.dll", "tools/netstandard2.0/Avalonia.BuildServices.dll", "tools/netstandard2.0/runtimeconfig.json"]}, "Avalonia.Controls.ColorPicker/11.2.3": {"sha512": "JpK84GN+CkS5H0L/mDyYa4aMAJw93dukedKK1xC4g/J5NmwQ23qFPQNqZlTs+6f9nULwJPL+jiffJpSyGMt0gg==", "type": "package", "path": "avalonia.controls.colorpicker/11.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.controls.colorpicker.11.2.3.nupkg.sha512", "avalonia.controls.colorpicker.nuspec", "lib/net6.0/Avalonia.Controls.ColorPicker.dll", "lib/net6.0/Avalonia.Controls.ColorPicker.xml", "lib/net8.0/Avalonia.Controls.ColorPicker.dll", "lib/net8.0/Avalonia.Controls.ColorPicker.xml", "lib/netstandard2.0/Avalonia.Controls.ColorPicker.dll", "lib/netstandard2.0/Avalonia.Controls.ColorPicker.xml"]}, "Avalonia.Controls.DataGrid/11.2.3": {"sha512": "Ul6oWEoqs4eTQZIb4Hzf0+ajhgrCX9ypOj8TahKbkKoIznJkUoka3iV90Vpj/AuoT5AZsN6f+1+62SVPpeMApA==", "type": "package", "path": "avalonia.controls.datagrid/11.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.controls.datagrid.11.2.3.nupkg.sha512", "avalonia.controls.datagrid.nuspec", "lib/net6.0/Avalonia.Controls.DataGrid.dll", "lib/net6.0/Avalonia.Controls.DataGrid.xml", "lib/net8.0/Avalonia.Controls.DataGrid.dll", "lib/net8.0/Avalonia.Controls.DataGrid.xml", "lib/netstandard2.0/Avalonia.Controls.DataGrid.dll", "lib/netstandard2.0/Avalonia.Controls.DataGrid.xml"]}, "Avalonia.Diagnostics/11.2.3": {"sha512": "Qld0UvkSLfIuZD7/gS8RIO6ww3jP+xJvsMyqU8evdphPoic7h6LAeY/ppT9NtI0r1KUDT2BpFcVDqPyQH6eSiw==", "type": "package", "path": "avalonia.diagnostics/11.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.diagnostics.11.2.3.nupkg.sha512", "avalonia.diagnostics.nuspec", "lib/net6.0/Avalonia.Diagnostics.dll", "lib/net6.0/Avalonia.Diagnostics.xml", "lib/net8.0/Avalonia.Diagnostics.dll", "lib/net8.0/Avalonia.Diagnostics.xml", "lib/netstandard2.0/Avalonia.Diagnostics.dll", "lib/netstandard2.0/Avalonia.Diagnostics.xml"]}, "Avalonia.Remote.Protocol/11.2.3": {"sha512": "6V0aNtld48WmO8tAlWwlRlUmXYcOWv+1eJUSl1ETF+1blUe5yhcSmuWarPprO0hDk8Ta6wGfdfcrnVl2gITYcA==", "type": "package", "path": "avalonia.remote.protocol/11.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.remote.protocol.11.2.3.nupkg.sha512", "avalonia.remote.protocol.nuspec", "lib/net6.0/Avalonia.Remote.Protocol.dll", "lib/net6.0/Avalonia.Remote.Protocol.xml", "lib/net8.0/Avalonia.Remote.Protocol.dll", "lib/net8.0/Avalonia.Remote.Protocol.xml", "lib/netstandard2.0/Avalonia.Remote.Protocol.dll", "lib/netstandard2.0/Avalonia.Remote.Protocol.xml"]}, "Avalonia.Skia/11.2.0": {"sha512": "UZEwEgEV8XzkzT0XxoLx3HglqeS1J6ieaui5Kcvtni4va6XJUhUDDwaCv6FagrUU9hF0y/VkbYbVMylEN8z2Gg==", "type": "package", "path": "avalonia.skia/11.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.skia.11.2.0.nupkg.sha512", "avalonia.skia.nuspec", "lib/net6.0/Avalonia.Skia.dll", "lib/net6.0/Avalonia.Skia.xml", "lib/net8.0/Avalonia.Skia.dll", "lib/net8.0/Avalonia.Skia.xml", "lib/netstandard2.0/Avalonia.Skia.dll", "lib/netstandard2.0/Avalonia.Skia.xml"]}, "Avalonia.Themes.Simple/11.2.3": {"sha512": "Okio7RYUHUk1m/1n6VGrkoq0C3Y3J6RwtrTGEfXPkYMJsL6yPqstJZMYkMFQPISUr8TYUNVKv82hL1qLRw7hwA==", "type": "package", "path": "avalonia.themes.simple/11.2.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.themes.simple.11.2.3.nupkg.sha512", "avalonia.themes.simple.nuspec", "lib/net6.0/Avalonia.Themes.Simple.dll", "lib/net6.0/Avalonia.Themes.Simple.xml", "lib/net8.0/Avalonia.Themes.Simple.dll", "lib/net8.0/Avalonia.Themes.Simple.xml", "lib/netstandard2.0/Avalonia.Themes.Simple.dll", "lib/netstandard2.0/Avalonia.Themes.Simple.xml"]}, "Classic.Avalonia.Theme/11.2.0": {"sha512": "NQLDiq6YWRZpeSgjFtNrYPq0xbRQaWp3tjyqPKrvN+QNfvQ3D6hrMmtQItKNhi3Vmqk1jLYej2cVA6UdzWI/5Q==", "type": "package", "path": "classic.avalonia.theme/11.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "classic.avalonia.theme.11.2.0.nupkg.sha512", "classic.avalonia.theme.nuspec", "icon.png", "lib/netstandard2.0/Classic.Avalonia.Theme.dll"]}, "Classic.CommonControls.Avalonia/11.2.0": {"sha512": "v2c6tbe0PQoLk4mwt4y/ggEMxfkGBni1qhEDJbumQ1v1J3/0D3RQEFHbFk03l+BVcyPRcDcJZc25DVnK/XE7Mw==", "type": "package", "path": "classic.commoncontrols.avalonia/11.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "classic.commoncontrols.avalonia.11.2.0.nupkg.sha512", "classic.commoncontrols.avalonia.nuspec", "icon.png", "lib/netstandard2.0/Classic.CommonControls.Avalonia.dll"]}, "HarfBuzzSharp/7.3.0.2": {"sha512": "0tCd6HyCmNsX/DniCp2b00fo0xPbdNwKOs9BxxyT8oOOuMlWjcSFwzONKyeckCKVBFEsbSmsAHPDTqxoSDwZMg==", "type": "package", "path": "harfbuzzsharp/7.3.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.7.3.0.2.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.2": {"sha512": "aKa5J1RqjXKAtdcZJp5wjC78klfBIzJHM6CneN76lFmQ9LLRJA9Oa0TkIDaV8lVLDKMAy5fCKHXFlXUK1YfL/g==", "type": "package", "path": "harfbuzzsharp.nativeassets.linux/7.3.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "harfbuzzsharp.nativeassets.linux.7.3.0.2.nupkg.sha512", "harfbuzzsharp.nativeassets.linux.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libHarfBuzzSharp.so", "runtimes/linux-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "runtimes/linux-x64/native/libHarfBuzzSharp.so"]}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"sha512": "nycYH/WLJ6ogm+I+QSFCdPJsdxSb5GANWYbQyp1vsd/KjXN56RVUJWPhbgP2GKb/Y7mrsHM7EProqVXlO/EMsA==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.7.3.0.2.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0.3-preview.2.2": {"sha512": "Dc+dolrhmkpqwT25NfNEEgceW0//KRR2WIOvxlyIIHIIMBCn0FfUeJX5RhFll8kyaZwF8tuKsxRJtQG/rzSBog==", "type": "package", "path": "harfbuzzsharp.nativeassets.webassembly/7.3.0.3-preview.2.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libHarfBuzzSharp.a/2.0.23/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/2.0.6/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt,simd/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/simd/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/simd,mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/simd,st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/simd,mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/simd,st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.56/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.7/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "harfbuzzsharp.nativeassets.webassembly.7.3.0.3-preview.2.2.nupkg.sha512", "harfbuzzsharp.nativeassets.webassembly.nuspec", "lib/netstandard1.0/_._"]}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"sha512": "DpF9JBzwws2dupOLnjME65hxQWWbN/GD40AoTkwB4S05WANvxo3n81AnQJKxWDCnrWfWhLPB36OF27TvEqzb/A==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.7.3.0.2.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "LanguageExt.Core/4.4.9": {"sha512": "K9VGWkThJkaomifa3zcmwysw1BaSqIZZPZc6trBnJN8u9mpmA/cMMwCWEa/v7bPv/+NnG6PbyIDB7HtxBX7yCQ==", "type": "package", "path": "languageext.core/4.4.9", "files": [".nupkg.metadata", ".signature.p7s", "lang-ext-small.png", "languageext.core.4.4.9.nupkg.sha512", "languageext.core.nuspec", "lib/netstandard2.0/LanguageExt.Core.dll", "lib/netstandard2.0/LanguageExt.Core.xml"]}, "MicroCom.Runtime/0.11.0": {"sha512": "MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "type": "package", "path": "microcom.runtime/0.11.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/MicroCom.Runtime.dll", "lib/netstandard2.0/MicroCom.Runtime.dll", "microcom.runtime.0.11.0.nupkg.sha512", "microcom.runtime.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"sha512": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Build.Framework/17.8.3": {"sha512": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "type": "package", "path": "microsoft.build.framework/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Framework.dll", "lib/net472/Microsoft.Build.Framework.pdb", "lib/net472/Microsoft.Build.Framework.xml", "lib/net8.0/Microsoft.Build.Framework.dll", "lib/net8.0/Microsoft.Build.Framework.pdb", "lib/net8.0/Microsoft.Build.Framework.xml", "microsoft.build.framework.17.8.3.nupkg.sha512", "microsoft.build.framework.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Framework.dll", "ref/net472/Microsoft.Build.Framework.xml", "ref/net8.0/Microsoft.Build.Framework.dll", "ref/net8.0/Microsoft.Build.Framework.xml", "ref/netstandard2.0/Microsoft.Build.Framework.dll", "ref/netstandard2.0/Microsoft.Build.Framework.xml"]}, "Microsoft.Build.Utilities.Core/17.8.3": {"sha512": "ex8Sjx02q0FmForLRFItB82sJx5s2JRWIpJgYDW1g7xLUoFlKLoNp67UwS5xN8YcYBkT7vRxXeYx/dQ96KaWtg==", "type": "package", "path": "microsoft.build.utilities.core/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Utilities.Core.dll", "lib/net472/Microsoft.Build.Utilities.Core.pdb", "lib/net472/Microsoft.Build.Utilities.Core.xml", "lib/net8.0/Microsoft.Build.Utilities.Core.dll", "lib/net8.0/Microsoft.Build.Utilities.Core.pdb", "lib/net8.0/Microsoft.Build.Utilities.Core.xml", "microsoft.build.utilities.core.17.8.3.nupkg.sha512", "microsoft.build.utilities.core.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Utilities.Core.dll", "ref/net472/Microsoft.Build.Utilities.Core.xml", "ref/net8.0/Microsoft.Build.Utilities.Core.dll", "ref/net8.0/Microsoft.Build.Utilities.Core.xml", "ref/netstandard2.0/Microsoft.Build.Utilities.Core.dll", "ref/netstandard2.0/Microsoft.Build.Utilities.Core.xml"]}, "Microsoft.NET.StringTools/17.8.3": {"sha512": "y6DiuacjlIfXH3XVQG5htf+4oheinZAo7sHbITB3z7yCXQec48f9ZhGSXkr+xn1bfl73Yc3ZQEW2peJ5X68AvQ==", "type": "package", "path": "microsoft.net.stringtools/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.NET.StringTools.dll", "lib/net472/Microsoft.NET.StringTools.pdb", "lib/net472/Microsoft.NET.StringTools.xml", "lib/net8.0/Microsoft.NET.StringTools.dll", "lib/net8.0/Microsoft.NET.StringTools.pdb", "lib/net8.0/Microsoft.NET.StringTools.xml", "lib/netstandard2.0/Microsoft.NET.StringTools.dll", "lib/netstandard2.0/Microsoft.NET.StringTools.pdb", "lib/netstandard2.0/Microsoft.NET.StringTools.xml", "microsoft.net.stringtools.17.8.3.nupkg.sha512", "microsoft.net.stringtools.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.NET.StringTools.dll", "ref/net472/Microsoft.NET.StringTools.xml", "ref/net8.0/Microsoft.NET.StringTools.dll", "ref/net8.0/Microsoft.NET.StringTools.xml", "ref/netstandard2.0/Microsoft.NET.StringTools.dll", "ref/netstandard2.0/Microsoft.NET.StringTools.xml"]}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"sha512": "gMq8uGy8zTIp0kQGTI45buZC3JOStGJyjGD8gksskk83aQISW65IESErLE/WDT7Bdy+QWbdUi7QyO1LEzUSOFA==", "type": "package", "path": "microsoft.visualstudio.setup.configuration.interop/3.2.2146", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.VisualStudio.Setup.Configuration.Interop.targets", "lib/net35/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "lib/net35/Microsoft.VisualStudio.Setup.Configuration.Interop.xml", "lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.xml", "microsoft.visualstudio.setup.configuration.interop.3.2.2146.nupkg.sha512", "microsoft.visualstudio.setup.configuration.interop.nuspec"]}, "Microsoft.Win32.SystemEvents/7.0.0": {"sha512": "2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "type": "package", "path": "microsoft.win32.systemevents/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net6.0/Microsoft.Win32.SystemEvents.dll", "lib/net6.0/Microsoft.Win32.SystemEvents.xml", "lib/net7.0/Microsoft.Win32.SystemEvents.dll", "lib/net7.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.7.0.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "PropertyChanged.SourceGenerator/1.0.8": {"sha512": "XufLv9ReskTWjx3assV8aRAfcHfAATAkR/MugYSHkyYm7d2voQJHtQKSncVMNZiVGwXaEg5aGtWgKMweR11TAg==", "type": "package", "path": "propertychanged.sourcegenerator/1.0.8", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "analyzers/dotnet/cs/PropertyChanged.SourceGenerator.dll", "icon.png", "propertychanged.sourcegenerator.1.0.8.nupkg.sha512", "propertychanged.sourcegenerator.nuspec"]}, "SkiaSharp/2.88.8": {"sha512": "bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "type": "package", "path": "skiasharp/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.8.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"sha512": "0FO6YA7paNFBMJULvEyecPmCvL9/STvOAi5VOUw2srqJ7pNTbiiZkfl7sulAzcumbWgfzaVjRXYTgMj7SoUnWQ==", "type": "package", "path": "skiasharp.nativeassets.linux/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.2.88.8.nupkg.sha512", "skiasharp.nativeassets.linux.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"sha512": "6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {"sha512": "S3qRo8c+gVYOyfrdf6FYnjx/ft+gPkb4dNY2IPv5Oy5yNBhDhXhKqHFr9h4+ne6ZU+7D4dbuRQqsIqCo8u1/DA==", "type": "package", "path": "skiasharp.nativeassets.webassembly/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libSkiaSharp.a/2.0.23/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/2.0.6/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt,simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.7/libSkiaSharp.a", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "lib/netstandard1.0/_._", "skiasharp.nativeassets.webassembly.2.88.8.nupkg.sha512", "skiasharp.nativeassets.webassembly.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"sha512": "O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "System.Configuration.ConfigurationManager/7.0.0": {"sha512": "WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "type": "package", "path": "system.configuration.configurationmanager/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.7.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/7.0.0": {"sha512": "eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "type": "package", "path": "system.diagnostics.eventlog/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.7.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/7.0.0": {"sha512": "KIX+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "type": "package", "path": "system.drawing.common/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.xml", "lib/net6.0/System.Drawing.Common.dll", "lib/net6.0/System.Drawing.Common.xml", "lib/net7.0/System.Drawing.Common.dll", "lib/net7.0/System.Drawing.Common.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Drawing.Common.dll", "runtimes/win/lib/net6.0/System.Drawing.Common.xml", "runtimes/win/lib/net7.0/System.Drawing.Common.dll", "runtimes/win/lib/net7.0/System.Drawing.Common.xml", "system.drawing.common.7.0.0.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/7.0.0": {"sha512": "xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "type": "package", "path": "system.security.cryptography.protecteddata/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/7.0.0": {"sha512": "Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "type": "package", "path": "system.security.permissions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/net7.0/System.Security.Permissions.dll", "lib/net7.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.7.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Windows.Extensions/7.0.0": {"sha512": "bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "type": "package", "path": "system.windows.extensions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/net7.0/System.Windows.Extensions.dll", "lib/net7.0/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/net7.0/System.Windows.Extensions.dll", "runtimes/win/lib/net7.0/System.Windows.Extensions.xml", "system.windows.extensions.7.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Antlr4.Runtime.Standard >= 4.13.1", "Antlr4BuildTasks >= 12.8.0", "Avalonia >= 11.2.3", "Avalonia.Diagnostics >= 11.2.3", "Classic.Avalonia.Theme >= 11.2.0", "LanguageExt.Core >= 4.4.9", "PropertyChanged.SourceGenerator >= 1.0.8"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "D:\\sdk\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\codesecondfolder\\BRU-Avtopark-Av<PERSON><PERSON>ov\\SuperNova.Runtime\\SuperNova.Runtime.csproj", "projectName": "SuperNova.Runtime", "projectPath": "G:\\codesecondfolder\\BRU-Avtopark-Av<PERSON><PERSON>ov\\SuperNova.Runtime\\SuperNova.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\SuperNova.Runtime\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "D:\\sdk\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\vs2010nugetlocal": {}, "https://www.nuget.org/api/v2/": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Antlr4.Runtime.Standard": {"target": "Package", "version": "[4.13.1, )"}, "Antlr4BuildTasks": {"target": "Package", "version": "[12.8.0, )"}, "Avalonia": {"target": "Package", "version": "[11.2.3, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.2.3, )"}, "Classic.Avalonia.Theme": {"target": "Package", "version": "[11.2.0, )"}, "LanguageExt.Core": {"target": "Package", "version": "[4.4.9, )"}, "PropertyChanged.SourceGenerator": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[1.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[9.0.1, 9.0.1]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[9.0.1, 9.0.1]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[9.0.1, 9.0.1]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.CSharp": "(, 4.7.0]", "Microsoft.NETCore.App": "(, 2.1.0]", "Microsoft.VisualBasic": "(, 10.4.0]", "Microsoft.Win32.Primitives": "(, 4.3.0]", "Microsoft.Win32.Registry": "(, 5.0.0]", "runtime.any.System.Collections": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.any.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.any.System.Globalization": "(, 4.3.0]", "runtime.any.System.Globalization.Calendars": "(, 4.3.0]", "runtime.any.System.IO": "(, 4.3.0]", "runtime.any.System.Reflection": "(, 4.3.0]", "runtime.any.System.Reflection.Extensions": "(, 4.3.0]", "runtime.any.System.Reflection.Primitives": "(, 4.3.0]", "runtime.any.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.any.System.Runtime": "(, 4.3.1]", "runtime.any.System.Runtime.Handles": "(, 4.3.0]", "runtime.any.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.any.System.Text.Encoding": "(, 4.3.0]", "runtime.any.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.any.System.Threading.Tasks": "(, 4.3.0]", "runtime.any.System.Threading.Timer": "(, 4.3.0]", "runtime.aot.System.Collections": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tools": "(, 4.3.0]", "runtime.aot.System.Diagnostics.Tracing": "(, 4.3.0]", "runtime.aot.System.Globalization": "(, 4.3.0]", "runtime.aot.System.Globalization.Calendars": "(, 4.3.0]", "runtime.aot.System.IO": "(, 4.3.0]", "runtime.aot.System.Reflection": "(, 4.3.0]", "runtime.aot.System.Reflection.Extensions": "(, 4.3.0]", "runtime.aot.System.Reflection.Primitives": "(, 4.3.0]", "runtime.aot.System.Resources.ResourceManager": "(, 4.3.0]", "runtime.aot.System.Runtime": "(, 4.3.1]", "runtime.aot.System.Runtime.Handles": "(, 4.3.0]", "runtime.aot.System.Runtime.InteropServices": "(, 4.3.0]", "runtime.aot.System.Text.Encoding": "(, 4.3.0]", "runtime.aot.System.Text.Encoding.Extensions": "(, 4.3.0]", "runtime.aot.System.Threading.Tasks": "(, 4.3.0]", "runtime.aot.System.Threading.Timer": "(, 4.3.0]", "runtime.debian.8-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.debian.9-x64.runtime.native.System": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.debian.9-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.23-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.24-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.27-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.27-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.fedora.28-x64.runtime.native.System": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.fedora.28-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.13.2-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.1-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.opensuse.42.3-x64.runtime.native.System": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.opensuse.42.3-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.osx.10.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(, 4.3.1]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.rhel.7-x64.runtime.native.System": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(, 4.3.1]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography": "(, 4.3.4]", "runtime.ubuntu.18.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(, 4.3.3]", "runtime.unix.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.unix.System.Console": "(, 4.3.1]", "runtime.unix.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.unix.System.IO.FileSystem": "(, 4.3.0]", "runtime.unix.System.Net.Primitives": "(, 4.3.0]", "runtime.unix.System.Net.Sockets": "(, 4.3.0]", "runtime.unix.System.Private.Uri": "(, 4.3.1]", "runtime.unix.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win.Microsoft.Win32.Primitives": "(, 4.3.0]", "runtime.win.System.Console": "(, 4.3.1]", "runtime.win.System.Diagnostics.Debug": "(, 4.3.0]", "runtime.win.System.IO.FileSystem": "(, 4.3.0]", "runtime.win.System.Net.Primitives": "(, 4.3.0]", "runtime.win.System.Net.Sockets": "(, 4.3.0]", "runtime.win.System.Runtime.Extensions": "(, 4.3.1]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(, 4.0.1]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(, 4.3.2]", "runtime.win7.System.Private.Uri": "(, 4.3.1]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(, 4.3.2]", "System.AppContext": "(, 4.3.0]", "System.Buffers": "(, 5.0.0]", "System.Collections": "(, 4.3.0]", "System.Collections.Concurrent": "(, 4.3.0]", "System.Collections.Immutable": "(, 9.0.0]", "System.Collections.NonGeneric": "(, 4.3.0]", "System.Collections.Specialized": "(, 4.3.0]", "System.ComponentModel": "(, 4.3.0]", "System.ComponentModel.Annotations": "(, 5.0.0]", "System.ComponentModel.EventBasedAsync": "(, 4.3.0]", "System.ComponentModel.Primitives": "(, 4.3.0]", "System.ComponentModel.TypeConverter": "(, 4.3.0]", "System.Console": "(, 4.3.1]", "System.Data.Common": "(, 4.3.0]", "System.Data.DataSetExtensions": "(, 4.5.0]", "System.Diagnostics.Contracts": "(, 4.3.0]", "System.Diagnostics.Debug": "(, 4.3.0]", "System.Diagnostics.DiagnosticSource": "(, 9.0.0]", "System.Diagnostics.FileVersionInfo": "(, 4.3.0]", "System.Diagnostics.Process": "(, 4.3.0]", "System.Diagnostics.StackTrace": "(, 4.3.0]", "System.Diagnostics.TextWriterTraceListener": "(, 4.3.0]", "System.Diagnostics.Tools": "(, 4.3.0]", "System.Diagnostics.TraceSource": "(, 4.3.0]", "System.Diagnostics.Tracing": "(, 4.3.0]", "System.Drawing.Primitives": "(, 4.3.0]", "System.Dynamic.Runtime": "(, 4.3.0]", "System.Formats.Asn1": "(, 9.0.0]", "System.Formats.Tar": "(, 9.0.0]", "System.Globalization": "(, 4.3.0]", "System.Globalization.Calendars": "(, 4.3.0]", "System.Globalization.Extensions": "(, 4.3.0]", "System.IO": "(, 4.3.0]", "System.IO.Compression": "(, 4.3.0]", "System.IO.Compression.ZipFile": "(, 4.3.0]", "System.IO.FileSystem": "(, 4.3.0]", "System.IO.FileSystem.AccessControl": "(, 5.0.0]", "System.IO.FileSystem.DriveInfo": "(, 4.3.1]", "System.IO.FileSystem.Primitives": "(, 4.3.0]", "System.IO.FileSystem.Watcher": "(, 4.3.0]", "System.IO.IsolatedStorage": "(, 4.3.0]", "System.IO.MemoryMappedFiles": "(, 4.3.0]", "System.IO.Pipelines": "(, 9.0.0]", "System.IO.Pipes": "(, 4.3.0]", "System.IO.Pipes.AccessControl": "(, 4.6.0]", "System.IO.UnmanagedMemoryStream": "(, 4.3.0]", "System.Linq": "(, 4.3.0]", "System.Linq.Expressions": "(, 4.3.0]", "System.Linq.Parallel": "(, 4.3.0]", "System.Linq.Queryable": "(, 4.3.0]", "System.Memory": "(, 5.0.0]", "System.Net.Http": "(, 4.3.4]", "System.Net.Http.Json": "(, 9.0.0]", "System.Net.NameResolution": "(, 4.3.0]", "System.Net.NetworkInformation": "(, 4.3.0]", "System.Net.Ping": "(, 4.3.0]", "System.Net.Primitives": "(, 4.3.1]", "System.Net.Requests": "(, 4.3.0]", "System.Net.Security": "(, 4.3.2]", "System.Net.Sockets": "(, 4.3.0]", "System.Net.WebHeaderCollection": "(, 4.3.0]", "System.Net.WebSockets": "(, 4.3.0]", "System.Net.WebSockets.Client": "(, 4.3.2]", "System.Numerics.Vectors": "(, 5.0.0]", "System.ObjectModel": "(, 4.3.0]", "System.Private.DataContractSerialization": "(, 4.3.0]", "System.Private.Uri": "(, 4.3.2]", "System.Reflection": "(, 4.3.0]", "System.Reflection.DispatchProxy": "(, 6.0.0]", "System.Reflection.Emit": "(, 4.7.0]", "System.Reflection.Emit.ILGeneration": "(, 4.7.0]", "System.Reflection.Emit.Lightweight": "(, 4.7.0]", "System.Reflection.Extensions": "(, 4.3.0]", "System.Reflection.Metadata": "(, 9.0.0]", "System.Reflection.Primitives": "(, 4.3.0]", "System.Reflection.TypeExtensions": "(, 4.7.0]", "System.Resources.Reader": "(, 4.3.0]", "System.Resources.ResourceManager": "(, 4.3.0]", "System.Resources.Writer": "(, 4.3.0]", "System.Runtime": "(, 4.3.1]", "System.Runtime.CompilerServices.Unsafe": "(, 7.0.0]", "System.Runtime.CompilerServices.VisualC": "(, 4.3.0]", "System.Runtime.Extensions": "(, 4.3.1]", "System.Runtime.Handles": "(, 4.3.0]", "System.Runtime.InteropServices": "(, 4.3.0]", "System.Runtime.InteropServices.RuntimeInformation": "(, 4.3.0]", "System.Runtime.InteropServices.WindowsRuntime": "(, 4.3.0]", "System.Runtime.Loader": "(, 4.3.0]", "System.Runtime.Numerics": "(, 4.3.0]", "System.Runtime.Serialization.Formatters": "(, 4.3.0]", "System.Runtime.Serialization.Json": "(, 4.3.0]", "System.Runtime.Serialization.Primitives": "(, 4.3.0]", "System.Runtime.Serialization.Xml": "(, 4.3.0]", "System.Runtime.WindowsRuntime": "(, 4.7.0]", "System.Runtime.WindowsRuntime.UI.Xaml": "(, 4.7.0]", "System.Security.AccessControl": "(, 6.0.1]", "System.Security.Claims": "(, 4.3.0]", "System.Security.Cryptography.Algorithms": "(, 4.3.1]", "System.Security.Cryptography.Cng": "(, 4.6.0]", "System.Security.Cryptography.Csp": "(, 4.3.0]", "System.Security.Cryptography.Encoding": "(, 4.3.0]", "System.Security.Cryptography.OpenSsl": "(, 5.0.0]", "System.Security.Cryptography.Primitives": "(, 4.3.0]", "System.Security.Cryptography.X509Certificates": "(, 4.3.2]", "System.Security.Cryptography.Xml": "(, 4.4.0]", "System.Security.Principal": "(, 4.3.0]", "System.Security.Principal.Windows": "(, 5.0.0]", "System.Security.SecureString": "(, 4.3.0]", "System.Text.Encoding": "(, 4.3.0]", "System.Text.Encoding.CodePages": "(, 9.0.0]", "System.Text.Encoding.Extensions": "(, 4.3.0]", "System.Text.Encodings.Web": "(, 9.0.0]", "System.Text.Json": "(, 9.0.0]", "System.Text.RegularExpressions": "(, 4.3.1]", "System.Threading": "(, 4.3.0]", "System.Threading.Channels": "(, 9.0.0]", "System.Threading.Overlapped": "(, 4.3.0]", "System.Threading.Tasks": "(, 4.3.0]", "System.Threading.Tasks.Dataflow": "(, 9.0.0]", "System.Threading.Tasks.Extensions": "(, 5.0.0]", "System.Threading.Tasks.Parallel": "(, 4.3.0]", "System.Threading.Thread": "(, 4.3.0]", "System.Threading.ThreadPool": "(, 4.3.0]", "System.Threading.Timer": "(, 4.3.0]", "System.ValueTuple": "(, 4.5.0]", "System.Xml.ReaderWriter": "(, 4.3.1]", "System.Xml.XDocument": "(, 4.3.0]", "System.Xml.XmlDocument": "(, 4.3.0]", "System.Xml.XmlSerializer": "(, 4.3.0]", "System.Xml.XPath": "(, 4.3.0]", "System.Xml.XPath.XDocument": "(, 5.0.0]"}}}}}