{"version": 2, "dgSpecHash": "gS20LAe1sIw=", "success": true, "projectFilePath": "G:\\codesecondfolder\\BRU-Avtopark-Av<PERSON><PERSON>ov\\SuperNova.Runtime\\SuperNova.Runtime.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\antlr4.runtime.standard\\4.13.1\\antlr4.runtime.standard.4.13.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\antlr4buildtasks\\12.8.0\\antlr4buildtasks.12.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia\\11.2.3\\avalonia.11.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.buildservices\\0.0.29\\avalonia.buildservices.0.0.29.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.colorpicker\\11.2.3\\avalonia.controls.colorpicker.11.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.controls.datagrid\\11.2.3\\avalonia.controls.datagrid.11.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.diagnostics\\11.2.3\\avalonia.diagnostics.11.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.remote.protocol\\11.2.3\\avalonia.remote.protocol.11.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.skia\\11.2.0\\avalonia.skia.11.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\avalonia.themes.simple\\11.2.3\\avalonia.themes.simple.11.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\classic.avalonia.theme\\11.2.0\\classic.avalonia.theme.11.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\classic.commoncontrols.avalonia\\11.2.0\\classic.commoncontrols.avalonia.11.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp\\7.3.0.2\\harfbuzzsharp.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.linux\\7.3.0.2\\harfbuzzsharp.nativeassets.linux.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.macos\\7.3.0.2\\harfbuzzsharp.nativeassets.macos.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.webassembly\\7.3.0.3-preview.2.2\\harfbuzzsharp.nativeassets.webassembly.7.3.0.3-preview.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\harfbuzzsharp.nativeassets.win32\\7.3.0.2\\harfbuzzsharp.nativeassets.win32.7.3.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\languageext.core\\4.4.9\\languageext.core.4.4.9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microcom.runtime\\0.11.0\\microcom.runtime.0.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\7.0.0\\microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\17.8.3\\microsoft.build.framework.17.8.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.utilities.core\\17.8.3\\microsoft.build.utilities.core.17.8.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.8.3\\microsoft.net.stringtools.17.8.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.setup.configuration.interop\\3.2.2146\\microsoft.visualstudio.setup.configuration.interop.3.2.2146.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\7.0.0\\microsoft.win32.systemevents.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\propertychanged.sourcegenerator\\1.0.8\\propertychanged.sourcegenerator.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\2.88.8\\skiasharp.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.linux\\2.88.8\\skiasharp.nativeassets.linux.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.macos\\2.88.8\\skiasharp.nativeassets.macos.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.webassembly\\2.88.8\\skiasharp.nativeassets.webassembly.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.nativeassets.win32\\2.88.8\\skiasharp.nativeassets.win32.2.88.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\7.0.0\\system.configuration.configurationmanager.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\7.0.0\\system.diagnostics.eventlog.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\7.0.0\\system.drawing.common.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\7.0.0\\system.security.cryptography.protecteddata.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\7.0.0\\system.security.permissions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\7.0.0\\system.windows.extensions.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\9.0.1\\microsoft.windowsdesktop.app.ref.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\9.0.1\\microsoft.netcore.app.ref.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\9.0.1\\microsoft.aspnetcore.app.ref.9.0.1.nupkg.sha512"], "logs": []}