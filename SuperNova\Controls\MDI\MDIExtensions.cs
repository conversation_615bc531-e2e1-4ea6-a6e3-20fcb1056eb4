using System.Collections.Generic;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.VisualTree;

namespace SuperNova.Controls;

public static class MDIExtensions
{
    private static readonly HashSet<Control> _activatingControls = new();

    public static void ActivateMDIForm(this Control control)
    {
        // Prevent stack overflow by checking if we're already activating this control
        if (_activatingControls.Contains(control))
            return;

        try
        {
            _activatingControls.Add(control);

            // Only activate if the control is attached to visual tree
            if (control.IsAttachedToVisualTree())
            {
                control.RaiseEvent(new RoutedEventArgs()
                {
                    Source = control,
                    RoutedEvent = MDIHost.ActivateWindowEvent
                });
            }
        }
        finally
        {
            _activatingControls.Remove(control);
        }
    }
}