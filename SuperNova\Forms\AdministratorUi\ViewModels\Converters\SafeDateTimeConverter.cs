using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace SuperNova.Forms.AdministratorUi.ViewModels.Converters;

/// <summary>
/// Safe DateTime converter that prevents binding loops and stack overflow issues
/// </summary>
public class SafeDateTimeConverter : IValueConverter
{
    private static readonly object _lockObject = new object();
    private static volatile bool _isConverting = false;
    
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        // Prevent recursive conversion that can cause stack overflow
        if (_isConverting)
            return value;
            
        lock (_lockObject)
        {
            if (_isConverting)
                return value;
                
            try
            {
                _isConverting = true;
                
                if (value == null)
                    return null;
                    
                // Handle DateTimeOffset to DateTime conversion
                if (value is DateTimeOffset dateTimeOffset)
                {
                    return dateTimeOffset.DateTime;
                }
                
                // Handle DateTimeOffset? to DateTime conversion
                if (value is DateTimeOffset nullableDateTimeOffset)
                {
                    return nullableDateTimeOffset.DateTime;
                }
                
                // Handle string to DateTime conversion with safety
                if (value is string stringValue && !string.IsNullOrEmpty(stringValue))
                {
                    if (DateTime.TryParse(stringValue, culture, DateTimeStyles.None, out var result))
                    {
                        return result;
                    }
                }
                
                // Return value as-is if no conversion needed
                return value;
            }
            catch (Exception)
            {
                // Return null on any conversion error to prevent crashes
                return null;
            }
            finally
            {
                _isConverting = false;
            }
        }
    }
    
    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        // Prevent recursive conversion that can cause stack overflow
        if (_isConverting)
            return value;
            
        lock (_lockObject)
        {
            if (_isConverting)
                return value;
                
            try
            {
                _isConverting = true;
                
                if (value == null)
                    return null;
                    
                // Handle DateTime to DateTimeOffset conversion
                if (value is DateTime dateTime)
                {
                    return new DateTimeOffset(dateTime);
                }
                
                // Handle string conversion
                if (value is string stringValue && !string.IsNullOrEmpty(stringValue))
                {
                    if (DateTime.TryParse(stringValue, culture, DateTimeStyles.None, out var result))
                    {
                        return new DateTimeOffset(result);
                    }
                }
                
                // Return value as-is if no conversion needed
                return value;
            }
            catch (Exception)
            {
                // Return null on any conversion error to prevent crashes
                return null;
            }
            finally
            {
                _isConverting = false;
            }
        }
    }
}

/// <summary>
/// Static helper methods for safe DateTime operations
/// </summary>
public static class SafeDateTimeHelper
{
    /// <summary>
    /// Safely extract Date component from DateTimeOffset? without causing binding loops
    /// </summary>
    public static DateTime SafeGetDate(DateTimeOffset? dateTimeOffset, DateTime defaultValue = default)
    {
        try
        {
            return dateTimeOffset?.Date ?? defaultValue;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }
    
    /// <summary>
    /// Safely extract DateTime from DateTimeOffset? without causing binding loops
    /// </summary>
    public static DateTime SafeGetDateTime(DateTimeOffset? dateTimeOffset, DateTime defaultValue = default)
    {
        try
        {
            return dateTimeOffset?.DateTime ?? defaultValue;
        }
        catch (Exception)
        {
            return defaultValue;
        }
    }
    
    /// <summary>
    /// Safely create DateTimeOffset from DateTime
    /// </summary>
    public static DateTimeOffset SafeCreateDateTimeOffset(DateTime dateTime)
    {
        try
        {
            return new DateTimeOffset(dateTime);
        }
        catch (Exception)
        {
            return DateTimeOffset.Now;
        }
    }
}
