﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFrameworks>$(DotNetVersion)</TargetFrameworks>
        <Nullable>enable</Nullable>
        <LangVersion>latest</LangVersion>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <TrimMode>full</TrimMode>
        <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
        <NoWarn>$(NoWarn);CS1998</NoWarn>
        <WarningsNotAsErrors>IL2026;IL3053;IL2104;</WarningsNotAsErrors>
        <TargetFramework>net9.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.2.3" />
        <PackageReference Include="Avalonia.AvaloniaEdit" Version="11.1.0" />
        <PackageReference Include="Avalonia.Controls.DataGrid" Version="11.2.3" />
        <PackageReference Include="Avalonia.Controls.ColorPicker" Version="11.2.3" />
        <PackageReference Include="Avalonia.Labs.Controls" Version="11.0.0" />
        <PackageReference Include="Classic.CommonControls.Avalonia" Version="11.2.0" />
        <PackageReference Include="Classic.Avalonia.Theme" Version="11.2.0" />
        <PackageReference Include="Classic.Avalonia.Theme.Dock" Version="11.2.0" />
        <PackageReference Include="Classic.Avalonia.Theme.ColorPicker" Version="11.2.0" />
        <PackageReference Include="Classic.Avalonia.Theme.DataGrid" Version="11.2.0" />
        <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.2.3" />
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.3.2" />
        <PackageReference Include="Pure.DI" Version="2.1.37">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Dock.Avalonia" Version="11.2.0" />
        <PackageReference Include="Dock.Model" Version="11.2.0" />
        <PackageReference Include="Dock.Model.MVVM" Version="11.2.0" />
        <PackageReference Include="Dock.Model.Avalonia" Version="11.2.0" />
        <PackageReference Include="Dock.Serializer" Version="11.2.0" />
        <PackageReference Include="PropertyChanged.SourceGenerator" Version="1.1.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="R3" Version="1.2.9" />
        <PackageReference Include="Serilog" Version="4.2.1-dev-02337" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.1-dev-02317" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <PackageReference Include="System.Text.Json" Version="9.0.1" />
        <PackageReference Include="Avalonia.ReactiveUI" Version="11.2.3" />
        <PackageReference Include="MessageBox.Avalonia" Version="3.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
        <PackageReference Include="Material.Icons.Avalonia" Version="2.1.0" />
        <PackageReference Include="PleasantUI" Version="4.0.1" />
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Avalonia" Version="2.0.0-rc2" />
        <PackageReference Include="Semi.Avalonia" Version="11.0.7" />
        <PackageReference Include="Semi.Avalonia.DataGrid" Version="11.0.7" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\SuperNova.Runtime\SuperNova.Runtime.csproj" />
        <ProjectReference Include="..\TicketSalesApp.Core\TicketSalesApp.Core.csproj" />
        <ProjectReference Include="..\TicketSalesApp.Services\TicketSalesApp.Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Remove="Icons\**\*.gif" />
        <AvaloniaResource Include="Icons\**\*.gif" />
        <AvaloniaResource Include="Resources\**\*" />
        <None Remove="vb6icon.ico" />
        <AvaloniaResource Include="vb6icon.ico" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Properties\Resources.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Resources.resx</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Properties\Resources.resx">
        <Generator>PublicResXFileCodeGenerator</Generator>
        <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    </ItemGroup>
</Project>