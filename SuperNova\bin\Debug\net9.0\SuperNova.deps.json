{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"SuperNova/1.0.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.AvaloniaEdit": "11.1.0", "Avalonia.Controls.ColorPicker": "11.2.3", "Avalonia.Controls.DataGrid": "11.2.3", "Avalonia.Diagnostics": "11.2.3", "Avalonia.Labs.Controls": "11.0.0", "Avalonia.ReactiveUI": "11.2.3", "Classic.Avalonia.Theme": "11.2.0", "Classic.Avalonia.Theme.ColorPicker": "11.2.0", "Classic.Avalonia.Theme.DataGrid": "11.2.0", "Classic.Avalonia.Theme.Dock": "11.2.0", "Classic.CommonControls.Avalonia": "11.2.0", "CommunityToolkit.Mvvm": "8.3.2", "Dock.Avalonia": "11.2.0", "Dock.Model": "11.2.0", "Dock.Model.Avalonia": "11.2.0", "Dock.Model.Mvvm": "11.2.0", "Dock.Serializer": "11.2.0", "LiveChartsCore.SkiaSharpView.Avalonia": "2.0.0-rc2", "Material.Icons.Avalonia": "2.1.0", "MessageBox.Avalonia": "3.2.0", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.1", "Microsoft.EntityFrameworkCore.Tools": "9.0.1", "PleasantUI": "4.0.1", "PropertyChanged.SourceGenerator": "1.1.0", "Pure.DI": "2.1.37", "R3": "1.2.9", "Semi.Avalonia": "11.0.7", "Semi.Avalonia.DataGrid": "11.0.7", "Serilog": "4.2.1-dev-02337", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "3.0.0", "Serilog.Sinks.File": "6.0.0", "SuperNova.Runtime": "1.0.0", "System.IdentityModel.Tokens.Jwt": "7.4.0", "System.Text.Json": "9.0.1", "TicketSalesApp.Core": "1.0.0", "TicketSalesApp.Services": "1.0.0"}, "runtime": {"SuperNova.dll": {}}}, "Antlr4.Runtime.Standard/4.13.1": {"runtime": {"lib/netstandard2.0/Antlr4.Runtime.Standard.dll": {"assemblyVersion": "4.13.1.0", "fileVersion": "4.13.1.0"}}}, "Antlr4BuildTasks/12.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Utilities.Core": "17.8.3"}, "runtime": {"lib/netstandard2.0/Antlr4BuildTasks.dll": {"assemblyVersion": "12.8.0.0", "fileVersion": "12.8.0.0"}}}, "Avalonia/11.2.3": {"dependencies": {"Avalonia.BuildServices": "0.0.29", "Avalonia.Remote.Protocol": "11.2.3", "MicroCom.Runtime": "0.11.0"}, "runtime": {"lib/net8.0/Avalonia.Base.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Controls.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "0.7.0.0", "fileVersion": "0.7.0.0"}, "lib/net8.0/Avalonia.Dialogs.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Markup.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Metal.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.MicroCom.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.OpenGL.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.Vulkan.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}, "lib/net8.0/Avalonia.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.AvaloniaEdit/11.1.0": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net6.0/AvaloniaEdit.dll": {"assemblyVersion": "11.0.6.0", "fileVersion": "11.0.6.0"}}}, "Avalonia.BuildServices/0.0.29": {}, "Avalonia.Controls.ColorPicker/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Remote.Protocol": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Controls.DataGrid/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Remote.Protocol": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Controls.DataGrid.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Controls.ProportionalStackPanel/11.2.0": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Controls.ProportionalStackPanel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Controls.Recycling/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.Recycling.Model": "11.2.0"}, "runtime": {"lib/net8.0/Avalonia.Controls.Recycling.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Controls.Recycling.Model/11.2.0": {"runtime": {"lib/net8.0/Avalonia.Controls.Recycling.Model.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Diagnostics/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.ColorPicker": "11.2.3", "Avalonia.Controls.DataGrid": "11.2.3", "Avalonia.Themes.Simple": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Diagnostics.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Labs.Controls/11.0.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Themes.Fluent": "11.0.0"}, "runtime": {"lib/net6.0/Avalonia.Labs.Controls.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Avalonia.MarkupExtension/11.2.0": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.MarkupExtension.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.ReactiveUI/11.2.3": {"dependencies": {"Avalonia": "11.2.3", "ReactiveUI": "20.1.1", "System.Reactive": "6.0.1"}, "runtime": {"lib/net8.0/Avalonia.ReactiveUI.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Remote.Protocol/11.2.3": {"runtime": {"lib/net8.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Avalonia.Skia/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "HarfBuzzSharp": "7.3.0.2", "HarfBuzzSharp.NativeAssets.Linux": "7.3.0.2", "HarfBuzzSharp.NativeAssets.WebAssembly": "7.3.0.3-preview.2.2", "SkiaSharp": "2.88.8", "SkiaSharp.NativeAssets.Linux": "2.88.8", "SkiaSharp.NativeAssets.WebAssembly": "2.88.8"}, "runtime": {"lib/net8.0/Avalonia.Skia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Themes.Fluent/11.0.0": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net6.0/Avalonia.Themes.Fluent.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Avalonia.Themes.Simple/11.2.3": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net8.0/Avalonia.Themes.Simple.dll": {"assemblyVersion": "11.2.3.0", "fileVersion": "11.2.3.0"}}}, "Classic.Avalonia.Theme/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Classic.CommonControls.Avalonia": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.Avalonia.Theme.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Classic.Avalonia.Theme.ColorPicker/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.ColorPicker": "11.2.3", "Classic.Avalonia.Theme": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.Avalonia.Theme.ColorPicker.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Classic.Avalonia.Theme.DataGrid/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.DataGrid": "11.2.3", "Classic.Avalonia.Theme": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.Avalonia.Theme.DataGrid.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Classic.Avalonia.Theme.Dock/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Classic.Avalonia.Theme": "11.2.0", "Dock.Avalonia": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.Avalonia.Theme.Dock.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Classic.CommonControls.Avalonia/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Skia": "11.2.0"}, "runtime": {"lib/netstandard2.0/Classic.CommonControls.Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "CommunityToolkit.Mvvm/8.3.2": {"runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CsvHelper/33.0.1": {"runtime": {"lib/net8.0/CsvHelper.dll": {"assemblyVersion": "********", "fileVersion": "*********"}}}, "DialogHost.Avalonia/0.8.1": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/netstandard2.0/DialogHost.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Dock.Avalonia/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.ProportionalStackPanel": "11.2.0", "Avalonia.Controls.Recycling": "11.2.0", "Avalonia.MarkupExtension": "11.2.0", "Dock.Model": "11.2.0", "Dock.Settings": "11.2.0"}, "runtime": {"lib/net8.0/Dock.Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Dock.Model/11.2.0": {"dependencies": {"Avalonia.Controls.Recycling.Model": "11.2.0"}, "runtime": {"lib/net8.0/Dock.Model.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Dock.Model.Avalonia/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Dock.Avalonia": "11.2.0", "Dock.Model": "11.2.0", "Dock.Settings": "11.2.0"}, "runtime": {"lib/net8.0/Dock.Model.Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Dock.Model.Mvvm/11.2.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.3.2", "Dock.Model": "11.2.0"}, "runtime": {"lib/net8.0/Dock.Model.Mvvm.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Dock.Serializer/11.2.0": {"dependencies": {"Dock.Model": "11.2.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net8.0/Dock.Serializer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Dock.Settings/11.2.0": {"dependencies": {"Avalonia": "11.2.3", "Dock.Model": "11.2.0"}, "runtime": {"lib/net8.0/Dock.Settings.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "DynamicData/8.4.1": {"dependencies": {"System.Reactive": "6.0.1"}, "runtime": {"lib/net8.0/DynamicData.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.1.20756"}}}, "HarfBuzzSharp/7.3.0.2": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0.2", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0.2"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "*******", "fileVersion": "7.3.0.2"}}}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.2": {"dependencies": {"HarfBuzzSharp": "7.3.0.2"}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0.3-preview.2.2": {}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "LanguageExt.Core/4.4.9": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0"}, "runtime": {"lib/netstandard2.0/LanguageExt.Core.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "LiveChartsCore/2.0.0-rc2": {"runtime": {"lib/net6.0/LiveChartsCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc2": {"dependencies": {"LiveChartsCore": "2.0.0-rc2", "SkiaSharp": "2.88.8", "SkiaSharp.HarfBuzz": "2.88.6"}, "runtime": {"lib/net6.0/LiveChartsCore.SkiaSharpView.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "LiveChartsCore.SkiaSharpView.Avalonia/2.0.0-rc2": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Skia": "11.2.0", "LiveChartsCore.SkiaSharpView": "2.0.0-rc2"}, "runtime": {"lib/net6.0/LiveChartsCore.SkiaSharpView.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Material.Icons/2.1.0": {"runtime": {"lib/netstandard2.0/Material.Icons.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Material.Icons.Avalonia/2.1.0": {"dependencies": {"Avalonia": "11.2.3", "Material.Icons": "2.1.0"}, "runtime": {"lib/netstandard2.0/Material.Icons.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MessageBox.Avalonia/3.2.0": {"dependencies": {"Avalonia": "11.2.3", "DialogHost.Avalonia": "0.8.1"}, "runtime": {"lib/netstandard2.0/MsBox.Avalonia.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Build.Framework/17.8.3": {"runtime": {"lib/net8.0/Microsoft.Build.Framework.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.8.3.51904"}}}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}}, "Microsoft.Build.Utilities.Core/17.8.3": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.NET.StringTools": "17.8.3", "Microsoft.VisualStudio.Setup.Configuration.Interop": "3.2.2146", "System.Configuration.ConfigurationManager": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.Build.Utilities.Core.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.8.3.51904"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/9.0.1": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.1": {}, "Microsoft.EntityFrameworkCore.Design/9.0.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.1"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.1": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.1"}}, "Microsoft.Extensions.Caching.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Caching.Memory/9.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyModel/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.1", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Primitives/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.IdentityModel.Abstractions/7.4.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.50226"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.4.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.50226"}}}, "Microsoft.IdentityModel.Logging/7.4.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.50226"}}}, "Microsoft.IdentityModel.Tokens/7.4.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.4.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.50226"}}}, "Microsoft.NET.StringTools/17.8.3": {"runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.8.3.51904"}}}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"runtime": {"lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.2146.50370"}}}, "Microsoft.Win32.SystemEvents/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.1"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "PleasantUI/4.0.1": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.ColorPicker": "11.2.3"}, "runtime": {"lib/netstandard2.0/PleasantUI.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}}, "PropertyChanged.SourceGenerator/1.1.0": {}, "Pure.DI/2.1.37": {}, "QRCoder/1.6.0": {"runtime": {"lib/net6.0/QRCoder.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}}}, "R3/1.2.9": {"runtime": {"lib/net8.0/R3.dll": {"assemblyVersion": "1.2.9.0", "fileVersion": "1.2.9.0"}}}, "ReactiveUI/20.1.1": {"dependencies": {"DynamicData": "8.4.1", "Splat": "15.1.1"}, "runtime": {"lib/net8.0/ReactiveUI.dll": {"assemblyVersion": "20.1.0.0", "fileVersion": "20.1.1.46356"}}}, "Semi.Avalonia/11.0.7": {"dependencies": {"Avalonia": "11.2.3"}, "runtime": {"lib/net6.0/Semi.Avalonia.dll": {"assemblyVersion": "11.0.7.0", "fileVersion": "11.0.7.0"}}}, "Semi.Avalonia.DataGrid/11.0.7": {"dependencies": {"Avalonia": "11.2.3", "Avalonia.Controls.DataGrid": "11.2.3"}, "runtime": {"lib/net6.0/Semi.Avalonia.DataGrid.dll": {"assemblyVersion": "11.0.7.0", "fileVersion": "11.0.7.0"}}}, "Serilog/4.2.1-dev-02337": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.1", "Serilog": "4.2.1-dev-02337"}, "runtime": {"lib/net9.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02337"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/3.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02337"}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.1-dev-02337"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.8": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.8", "SkiaSharp.NativeAssets.macOS": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.8.0"}}}, "SkiaSharp.HarfBuzz/2.88.6": {"dependencies": {"HarfBuzzSharp": "7.3.0.2", "SkiaSharp": "2.88.8"}, "runtime": {"lib/net6.0/SkiaSharp.HarfBuzz.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.6.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"dependencies": {"SkiaSharp": "2.88.8"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Splat/15.1.1": {"runtime": {"lib/net8.0/Splat.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "15.1.1.17670"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Configuration.ConfigurationManager/7.0.0": {"dependencies": {"System.Diagnostics.EventLog": "7.0.0", "System.Security.Cryptography.ProtectedData": "7.0.0", "System.Security.Permissions": "7.0.0"}, "runtime": {"lib/net7.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.EventLog/7.0.0": {"runtime": {"lib/net7.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Drawing.Common/7.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "7.0.0"}, "runtime": {"lib/net7.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IdentityModel.Tokens.Jwt/7.4.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.4.0", "Microsoft.IdentityModel.Tokens": "7.4.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.4.0.0", "fileVersion": "7.4.0.50226"}}}, "System.Reactive/6.0.1": {"runtime": {"lib/net6.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.7420"}}}, "System.Security.Cryptography.ProtectedData/7.0.0": {"runtime": {"lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Security.Permissions/7.0.0": {"dependencies": {"System.Windows.Extensions": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/9.0.1": {}, "System.Windows.Extensions/7.0.0": {"dependencies": {"System.Drawing.Common": "7.0.0"}, "runtime": {"lib/net7.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "SuperNova.Runtime/1.0.0": {"dependencies": {"Antlr4.Runtime.Standard": "4.13.1", "Antlr4BuildTasks": "12.8.0", "Avalonia": "11.2.3", "Avalonia.Diagnostics": "11.2.3", "Classic.Avalonia.Theme": "11.2.0", "LanguageExt.Core": "4.4.9"}, "runtime": {"SuperNova.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "TicketSalesApp.Core/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.1", "Serilog": "4.2.1-dev-02337", "Serilog.Settings.Configuration": "9.0.1-dev-02317", "Serilog.Sinks.File": "6.0.0", "System.Text.Json": "9.0.1"}, "runtime": {"TicketSalesApp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "TicketSalesApp.Services/1.0.0": {"dependencies": {"CsvHelper": "33.0.1", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.1", "QRCoder": "1.6.0", "TicketSalesApp.Core": "1.0.0"}, "runtime": {"TicketSalesApp.Services.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"SuperNova/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Antlr4.Runtime.Standard/4.13.1": {"type": "package", "serviceable": true, "sha512": "sha512-Da5+i4kFHUseJRZGcBG5fmZGpA/Ns180ibrQMxgZzjpQOnENVvSL5gi5HZ8Ncz8/AR2WsKbOg2lMBzjz0HUQcA==", "path": "antlr4.runtime.standard/4.13.1", "hashPath": "antlr4.runtime.standard.4.13.1.nupkg.sha512"}, "Antlr4BuildTasks/12.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZIcmPs7ykc6WDtuHSBvV5wZ1BkuR6DHbZnCrUWVAx3F5Lg4LcxHlzQEE0hB7NdyXhOoB3ykcyQ7EY88EzSflVQ==", "path": "antlr4buildtasks/12.8.0", "hashPath": "antlr4buildtasks.12.8.0.nupkg.sha512"}, "Avalonia/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-pD6woFAUfGcyEvMmrpctntU4jv4fT8752pfx1J5iRORVX3Ob0oQi8PWo0TXVaAJZiSfH0cdKTeKx0w0DzD0/mg==", "path": "avalonia/11.2.3", "hashPath": "avalonia.11.2.3.nupkg.sha512"}, "Avalonia.AvaloniaEdit/11.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-2s0bkZIviwb++fyD2S6GkX1dTsv2e3yJX5GvJKG4+R5KDcJpRtwOkH4GX1dvOL+eX35tPgmrDmrhBKCk/ezOBw==", "path": "avalonia.avaloniaedit/11.1.0", "hashPath": "avalonia.avaloniaedit.11.1.0.nupkg.sha512"}, "Avalonia.BuildServices/0.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-U4eJLQdoDNHXtEba7MZUCwrBErBTxFp6sUewXBOdAhU0Kwzwaa/EKFcYm8kpcysjzKtfB4S0S9n0uxKZFz/ikw==", "path": "avalonia.buildservices/0.0.29", "hashPath": "avalonia.buildservices.0.0.29.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-JpK84GN+CkS5H0L/mDyYa4aMAJw93dukedKK1xC4g/J5NmwQ23qFPQNqZlTs+6f9nULwJPL+jiffJpSyGMt0gg==", "path": "avalonia.controls.colorpicker/11.2.3", "hashPath": "avalonia.controls.colorpicker.11.2.3.nupkg.sha512"}, "Avalonia.Controls.DataGrid/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Ul6oWEoqs4eTQZIb4Hzf0+ajhgrCX9ypOj8TahKbkKoIznJkUoka3iV90Vpj/AuoT5AZsN6f+1+62SVPpeMApA==", "path": "avalonia.controls.datagrid/11.2.3", "hashPath": "avalonia.controls.datagrid.11.2.3.nupkg.sha512"}, "Avalonia.Controls.ProportionalStackPanel/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MV3PBPfqDEhZz9m3NSewhat8EknKzs7HNedlPhYGYnTMtvRKcXqhoygjh0GiBIV8As5E7BB6AyzFyZ6IX3JYOg==", "path": "avalonia.controls.proportionalstackpanel/11.2.0", "hashPath": "avalonia.controls.proportionalstackpanel.11.2.0.nupkg.sha512"}, "Avalonia.Controls.Recycling/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZgSzvZcXoWx5A9V32X9Mm7Azaq+BTqzVY0ZE7/Ve87kedGo/jlxN4r3nDvjxNf9L0shkuQpHCHdc/9SaRBldvw==", "path": "avalonia.controls.recycling/11.2.0", "hashPath": "avalonia.controls.recycling.11.2.0.nupkg.sha512"}, "Avalonia.Controls.Recycling.Model/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-QSvLVBhBWGZvmX4dZu5gNaTHfOkh+CtwT2fvM1WnroG2zKdpev00M3U+/x+o9wuWR+FDFodqfIsDHU/s3w6Q1A==", "path": "avalonia.controls.recycling.model/11.2.0", "hashPath": "avalonia.controls.recycling.model.11.2.0.nupkg.sha512"}, "Avalonia.Diagnostics/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Qld0UvkSLfIuZD7/gS8RIO6ww3jP+xJvsMyqU8evdphPoic7h6LAeY/ppT9NtI0r1KUDT2BpFcVDqPyQH6eSiw==", "path": "avalonia.diagnostics/11.2.3", "hashPath": "avalonia.diagnostics.11.2.3.nupkg.sha512"}, "Avalonia.Labs.Controls/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XeylBmwoi5c7Ny6O8qr4uE4H+KY10lkjboneOjy/T7XRhSou529oMn2KUG0wsnldOmQuFwsGC8zpji82bxmsJg==", "path": "avalonia.labs.controls/11.0.0", "hashPath": "avalonia.labs.controls.11.0.0.nupkg.sha512"}, "Avalonia.MarkupExtension/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-mt18kHbzB0L5UGf70NT9CJDDudIaWG1LSjBWw1cH92CrZue04y7kmYtYC7fr8TSzbu1OvNcda9nanNJA0ryRCw==", "path": "avalonia.markupextension/11.2.0", "hashPath": "avalonia.markupextension.11.2.0.nupkg.sha512"}, "Avalonia.ReactiveUI/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-MHasX3CRPkkbFN7UZC/+mRZiPvWmiIRl5SJjQYiOA+Zgw9xj6qK9mD3zNbr0Ej3yN+IRh/qDjBlTpL4TzDF88A==", "path": "avalonia.reactiveui/11.2.3", "hashPath": "avalonia.reactiveui.11.2.3.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-6V0aNtld48WmO8tAlWwlRlUmXYcOWv+1eJUSl1ETF+1blUe5yhcSmuWarPprO0hDk8Ta6wGfdfcrnVl2gITYcA==", "path": "avalonia.remote.protocol/11.2.3", "hashPath": "avalonia.remote.protocol.11.2.3.nupkg.sha512"}, "Avalonia.Skia/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-UZEwEgEV8XzkzT0XxoLx3HglqeS1J6ieaui5Kcvtni4va6XJUhUDDwaCv6FagrUU9hF0y/VkbYbVMylEN8z2Gg==", "path": "avalonia.skia/11.2.0", "hashPath": "avalonia.skia.11.2.0.nupkg.sha512"}, "Avalonia.Themes.Fluent/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQVDcxwsozHk0U3crhHxSeXfB67NwfLKmnW6I498QLt614jzq08c+lLqrjlHM0QpAPVw6vJwSubEYroSOyyErA==", "path": "avalonia.themes.fluent/11.0.0", "hashPath": "avalonia.themes.fluent.11.0.0.nupkg.sha512"}, "Avalonia.Themes.Simple/11.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Okio7RYUHUk1m/1n6VGrkoq0C3Y3J6RwtrTGEfXPkYMJsL6yPqstJZMYkMFQPISUr8TYUNVKv82hL1qLRw7hwA==", "path": "avalonia.themes.simple/11.2.3", "hashPath": "avalonia.themes.simple.11.2.3.nupkg.sha512"}, "Classic.Avalonia.Theme/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-NQLDiq6YWRZpeSgjFtNrYPq0xbRQaWp3tjyqPKrvN+QNfvQ3D6hrMmtQItKNhi3Vmqk1jLYej2cVA6UdzWI/5Q==", "path": "classic.avalonia.theme/11.2.0", "hashPath": "classic.avalonia.theme.11.2.0.nupkg.sha512"}, "Classic.Avalonia.Theme.ColorPicker/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-izYgYW/x9f/b9Rl39ZeMneHyLdBR6tjoJnPv+6bqtOXXLxMsN9kis1LLodP7pQA22e9co7R85wpIN0fwbHpp5Q==", "path": "classic.avalonia.theme.colorpicker/11.2.0", "hashPath": "classic.avalonia.theme.colorpicker.11.2.0.nupkg.sha512"}, "Classic.Avalonia.Theme.DataGrid/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-a9zk6GsPUogUiZ2VYVlfb7CGLV3Qu4AYbmJSZKetXfhvCI7G0HtCZ/tp3FM57IHx1MU8Wyp+7rAXEWiMTuM1Ew==", "path": "classic.avalonia.theme.datagrid/11.2.0", "hashPath": "classic.avalonia.theme.datagrid.11.2.0.nupkg.sha512"}, "Classic.Avalonia.Theme.Dock/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MZiiUa84hxFn/JQ9XUuIV4S7+DFJ7ed9mBjNy6CX3sZ0+DeXQYTTSz5J/efZR2uchNq9g47w4N3ZFmFXP9BpdQ==", "path": "classic.avalonia.theme.dock/11.2.0", "hashPath": "classic.avalonia.theme.dock.11.2.0.nupkg.sha512"}, "Classic.CommonControls.Avalonia/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-v2c6tbe0PQoLk4mwt4y/ggEMxfkGBni1qhEDJbumQ1v1J3/0D3RQEFHbFk03l+BVcyPRcDcJZc25DVnK/XE7Mw==", "path": "classic.commoncontrols.avalonia/11.2.0", "hashPath": "classic.commoncontrols.avalonia.11.2.0.nupkg.sha512"}, "CommunityToolkit.Mvvm/8.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-m8EolE1A0Updj68WTsZSGI6VWb6mUqHPh7QFo0kt7+JPhYMNXRS1ch8TS/oITAdcxTLrwMOp3ku1KjeG1/Zdpg==", "path": "communitytoolkit.mvvm/8.3.2", "hashPath": "communitytoolkit.mvvm.8.3.2.nupkg.sha512"}, "CsvHelper/33.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fev4lynklAU2A9GVMLtwarkwaanjSYB4wUqO2nOJX5hnzObORzUqVLe+bDYCUyIIRQM4o5Bsq3CcyJR89iMmEQ==", "path": "csvhelper/33.0.1", "hashPath": "csvhelper.33.0.1.nupkg.sha512"}, "DialogHost.Avalonia/0.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-RLBOMqjJPgSmwe0i1pzX3Q7Pn0i4xE/E1b774krd4VyEkaorz1AYMdYQHyIfuhtQv5NntZyS0MuQbxJl/PhzBg==", "path": "dialoghost.avalonia/0.8.1", "hashPath": "dialoghost.avalonia.0.8.1.nupkg.sha512"}, "Dock.Avalonia/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Y214kBlebgk0dLO2vetkQe5qDUBl0hZ8PLpZKiE9W3oAXDGOMjWQ5kLB+Nd8k9mto9NE75BCrtaTYjRwGG53g==", "path": "dock.avalonia/11.2.0", "hashPath": "dock.avalonia.11.2.0.nupkg.sha512"}, "Dock.Model/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vvgwgCr3IXq3LQQTXiaEeoWPkze31HJxTdEWw8cv84VECHIgH4TiPN1l3P+6u8fKT+4V8cgC1rD8Z4nHUQFkcA==", "path": "dock.model/11.2.0", "hashPath": "dock.model.11.2.0.nupkg.sha512"}, "Dock.Model.Avalonia/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-34AspcVs0qIP4QG792hKDEMgL23w80qLYo3jkl1SFx2umjmYx1xDLykETNt3kNNTQcL+dC3KtG9bfb2ZJyQ8Yw==", "path": "dock.model.avalonia/11.2.0", "hashPath": "dock.model.avalonia.11.2.0.nupkg.sha512"}, "Dock.Model.Mvvm/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XbE5W4Dm+YcN5xfKW3V9NyHNiwev3WYlmWdskx5sO2oFMUuVizyWvwz6JyyqjXadI86tVxi+a9DlsyuPkHCNuA==", "path": "dock.model.mvvm/11.2.0", "hashPath": "dock.model.mvvm.11.2.0.nupkg.sha512"}, "Dock.Serializer/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-uqWzINFywxWDMPtaR0XLpSVLx57fr3Uz8AZKnzej6aABZYZcXTTEvmBea4XoySURpX41laIs08fi+jqKzwaBCw==", "path": "dock.serializer/11.2.0", "hashPath": "dock.serializer.11.2.0.nupkg.sha512"}, "Dock.Settings/11.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gsifdPpUSdtTkqpUeLm9/0iBf/SfnGE7jAG8sNCeIi9ApPyJqJUIISGYTpYvQ7s/aOhHvxS2S7lKTffsstRDSQ==", "path": "dock.settings/11.2.0", "hashPath": "dock.settings.11.2.0.nupkg.sha512"}, "DynamicData/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mn1+fU/jqxgONEJq8KLQPGWEi7g/hUVTbjZyn4QM0sWWDAVOHPO9WjXWORSykwdfg/6S3GM15qsfz+2EvO+QAQ==", "path": "dynamicdata/8.4.1", "hashPath": "dynamicdata.8.4.1.nupkg.sha512"}, "HarfBuzzSharp/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-0tCd6HyCmNsX/DniCp2b00fo0xPbdNwKOs9BxxyT8oOOuMlWjcSFwzONKyeckCKVBFEsbSmsAHPDTqxoSDwZMg==", "path": "harfbuzzsharp/7.3.0.2", "hashPath": "harfbuzzsharp.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aKa5J1RqjXKAtdcZJp5wjC78klfBIzJHM6CneN76lFmQ9LLRJA9Oa0TkIDaV8lVLDKMAy5fCKHXFlXUK1YfL/g==", "path": "harfbuzzsharp.nativeassets.linux/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.linux.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nycYH/WLJ6ogm+I+QSFCdPJsdxSb5GANWYbQyp1vsd/KjXN56RVUJWPhbgP2GKb/Y7mrsHM7EProqVXlO/EMsA==", "path": "harfbuzzsharp.nativeassets.macos/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.macos.7.3.0.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0.3-preview.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Dc+dolrhmkpqwT25NfNEEgceW0//KRR2WIOvxlyIIHIIMBCn0FfUeJX5RhFll8kyaZwF8tuKsxRJtQG/rzSBog==", "path": "harfbuzzsharp.nativeassets.webassembly/7.3.0.3-preview.2.2", "hashPath": "harfbuzzsharp.nativeassets.webassembly.7.3.0.3-preview.2.2.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-DpF9JBzwws2dupOLnjME65hxQWWbN/GD40AoTkwB4S05WANvxo3n81AnQJKxWDCnrWfWhLPB36OF27TvEqzb/A==", "path": "harfbuzzsharp.nativeassets.win32/7.3.0.2", "hashPath": "harfbuzzsharp.nativeassets.win32.7.3.0.2.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "LanguageExt.Core/4.4.9": {"type": "package", "serviceable": true, "sha512": "sha512-K9VGWkThJkaomifa3zcmwysw1BaSqIZZPZc6trBnJN8u9mpmA/cMMwCWEa/v7bPv/+NnG6PbyIDB7HtxBX7yCQ==", "path": "languageext.core/4.4.9", "hashPath": "languageext.core.4.4.9.nupkg.sha512"}, "LiveChartsCore/2.0.0-rc2": {"type": "package", "serviceable": true, "sha512": "sha512-X55dG3oI3AVOGWUrly+J/bUJGmb/pmB2aTkaWJPyIXI2cr5cqPJG/6GLL17IJ8e3GOofFWgQCbg0Az0+myUQLg==", "path": "livechartscore/2.0.0-rc2", "hashPath": "livechartscore.2.0.0-rc2.nupkg.sha512"}, "LiveChartsCore.SkiaSharpView/2.0.0-rc2": {"type": "package", "serviceable": true, "sha512": "sha512-UVvUa7QAlyiPLuCjcoMKr4dI1FIUE5xW/XjcioSXgsOoqZg0E6Fq3B9k5t6MjIVE6dUU2F3tZwdm5KDiNftQ2A==", "path": "livechartscore.skiasharpview/2.0.0-rc2", "hashPath": "livechartscore.skiasharpview.2.0.0-rc2.nupkg.sha512"}, "LiveChartsCore.SkiaSharpView.Avalonia/2.0.0-rc2": {"type": "package", "serviceable": true, "sha512": "sha512-1ykqsUd8qKP2Iste0cnRa+mpnC49IHElE5saz8y+eeYynfTLo15YhnPJGWGit4mdvrWIQgT/1QCcE26tfdKvDw==", "path": "livechartscore.skiasharpview.avalonia/2.0.0-rc2", "hashPath": "livechartscore.skiasharpview.avalonia.2.0.0-rc2.nupkg.sha512"}, "Material.Icons/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-oMMrh8mKnZwOKZJEI3hc4Y18+/FQDfXSMv21xbX4kq05gcvcD1M2zxrjgNQvygHhP4DTcFmDidqUJRmuPyV9Fg==", "path": "material.icons/2.1.0", "hashPath": "material.icons.2.1.0.nupkg.sha512"}, "Material.Icons.Avalonia/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEuKf8mrByuxvjsM9LPWs+Ic0JPViDwZS7q1FzpHpu3QCEKOIriDiMNI6GaAtUA3uzz9OfAbd5vSjTi6SVbJrQ==", "path": "material.icons.avalonia/2.1.0", "hashPath": "material.icons.avalonia.2.1.0.nupkg.sha512"}, "MessageBox.Avalonia/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-f4iJxmyF0fKoJQdMCiSSRitP/niUl4g1mSBLGQ9+/58ZXrhjUImnuLdbuBY36n+JpMegy4FA62s588yJkbbjCA==", "path": "messagebox.avalonia/3.2.0", "hashPath": "messagebox.avalonia.3.2.0.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.Build.Utilities.Core/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-ex8Sjx02q0FmForLRFItB82sJx5s2JRWIpJgYDW1g7xLUoFlKLoNp67UwS5xN8YcYBkT7vRxXeYx/dQ96KaWtg==", "path": "microsoft.build.utilities.core/17.8.3", "hashPath": "microsoft.build.utilities.core.17.8.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-useMNbAupB8gpEp/SjanW3LvvyFG9DWPMUcXFwVNjNuFWIxNcrs5zOu9BTmNJEyfDpLlrsSBmcBv7keYVG8UhA==", "path": "microsoft.data.sqlite.core/9.0.1", "hashPath": "microsoft.data.sqlite.core.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E25w4XugXNykTr5Y/sLDGaQ4lf67n9aXVPvsdGsIZjtuLmbvb9AoYP8D50CDejY8Ro4D9GK2kNHz5lWHqSK+wg==", "path": "microsoft.entityframeworkcore/9.0.1", "hashPath": "microsoft.entityframeworkcore.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qy+taGVLUs82zeWfc32hgGL8Z02ZqAneYvqZiiXbxF4g4PBUcPRuxHM9K20USmpeJbn4/fz40GkCbyyCy5ojOA==", "path": "microsoft.entityframeworkcore.abstractions/9.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-c6ZZJZhPKrXFkE2z/81PmuT69HBL6Y68Cl0xJ5SRrDjJyq5Aabkq15yCqPg9RQ3R0aFLVaJok2DA8R3TKpejDQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.1", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/pchcadGU57ChRYH0/bvLTeU/n1mpWO+0pVK7pUzzuwRu5SIQb8dVMZVPhzvEI2VO5rP1yricSQBBnOmDqQhvg==", "path": "microsoft.entityframeworkcore.design/9.0.1", "hashPath": "microsoft.entityframeworkcore.design.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Iu0h4oevRvH4IwPzmxuIJGYRt55TapoREGlluk75KCO7lenN0+QnzCl6cQDY48uDoxAUpJbpK2xW7o8Ix69dw==", "path": "microsoft.entityframeworkcore.relational/9.0.1", "hashPath": "microsoft.entityframeworkcore.relational.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rt8P3/rJClgwlebCzAXdFt5/TemuP5IqBXLIjE2ZeJgaaDezPt9g8Pk3dqUj8YXb4pKcrFvuzZylYMZLCZWJzA==", "path": "microsoft.entityframeworkcore.sqlite/9.0.1", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eAo/tMaOLN2KI3KYwnhl9Ibmtry3gdRpVxdSxzyFiS1q8zvPNKtHU+fi1723JyuQEhUGpp551aQZIKGMmenk+Q==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.1", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yu0+5OInULF5PJX/ILZFWNT4ODBv4CSbS1TCXhkZYI6FxC7WguanhmCY9DL6U1R1YQ1tC38RspbxklmBBuk1SA==", "path": "microsoft.entityframeworkcore.tools/9.0.1", "hashPath": "microsoft.entityframeworkcore.tools.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eghsg9SyIvq0c8x6cUpe71BbQoOmsytXxqw2+ZNiTnP8a8SBLKgEor1zZeWhC0588IbS2M0PP4gXGAd9qF862Q==", "path": "microsoft.extensions.caching.abstractions/9.0.1", "hashPath": "microsoft.extensions.caching.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Je<PERSON>+PP0BCKMwwLezPGDaciJSTfcFG4KjsG8rX4XZ6RSvzdxofrFmcnmW2L4+cWUcZSBTQ+Dd7H5Gs9XZz/OlCA==", "path": "microsoft.extensions.caching.memory/9.0.1", "hashPath": "microsoft.extensions.caching.memory.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4hfFIY1UjBCXFTTOd+ojlDPq6mep3h5Vq5SYE3Pjucr7dNXmq4S/6P/LoVnZFz2e/5gWp/om4svUFgznfULcA==", "path": "microsoft.extensions.configuration.abstractions/9.0.1", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FHPy9cbb0y09riEpsrU5XYpOgf4nTfHj7a0m1wLC5DosGtjJn9g03gGg1GTJmEdRFBQrJwbwTnHqLCdNLsoYgA==", "path": "microsoft.extensions.dependencymodel/9.0.1", "hashPath": "microsoft.extensions.dependencymodel.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E/k5r7S44DOW+08xQPnNbO8DKAQHhkspDboTThNJ6Z3/QBb4LC6gStNWzVmy3IvW7sUD+iJKf4fj0xEkqE7vnQ==", "path": "microsoft.extensions.logging/9.0.1", "hashPath": "microsoft.extensions.logging.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y5wvSoKT6G9gi94FmTwyMCRDjMBVwCnDTXT9yyy95jS3lq3IRXJ/Fh+tqmsluA181RzAtXgK5SsvGGkcWm2n0g==", "path": "microsoft.identitymodel.abstractions/7.4.0", "hashPath": "microsoft.identitymodel.abstractions.7.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-8Gxy/KSjzEXg4hEyMvR0EsuLpaYLERoX1grIxechhoS8ugUyVvX9zxhMUg0EC/Oi3slhQ+rWrvN6M/M26RnRLQ==", "path": "microsoft.identitymodel.jsonwebtokens/7.4.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-TuCWnlQprih0PieviKAdvHtjuVLUjEBymEUlBqAe8BUBWlMjB7eHaZ1pi5mkPR5bbrVWLYu8/qiMVc0+neE6dQ==", "path": "microsoft.identitymodel.logging/7.4.0", "hashPath": "microsoft.identitymodel.logging.7.4.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2GXnFtFNv51QaIFw7pC3+OSRG+wDxj53MY6o6DRFFp/wAsTUTeasN+7Gkc6zSuI4cbrU9KXSVzs2cnW6EQ5szg==", "path": "microsoft.identitymodel.tokens/7.4.0", "hashPath": "microsoft.identitymodel.tokens.7.4.0.nupkg.sha512"}, "Microsoft.NET.StringTools/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-y6DiuacjlIfXH3XVQG5htf+4oheinZAo7sHbITB3z7yCXQec48f9ZhGSXkr+xn1bfl73Yc3ZQEW2peJ5X68AvQ==", "path": "microsoft.net.stringtools/17.8.3", "hashPath": "microsoft.net.stringtools.17.8.3.nupkg.sha512"}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"type": "package", "serviceable": true, "sha512": "sha512-gMq8uGy8zTIp0kQGTI45buZC3JOStGJyjGD8gksskk83aQISW65IESErLE/WDT7Bdy+QWbdUi7QyO1LEzUSOFA==", "path": "microsoft.visualstudio.setup.configuration.interop/3.2.2146", "hashPath": "microsoft.visualstudio.setup.configuration.interop.3.2.2146.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nXPrhdAyAzir0gLl8Yy8S5Mnm/uBSQQA7jEsILOS1MTyS7DbmV1NgViMtvV1sfCD1ebITpNwb1NIinKeJgUVQ==", "path": "microsoft.win32.systemevents/7.0.0", "hashPath": "microsoft.win32.systemevents.7.0.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "PleasantUI/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fnuQEv8x0j4PwTRZMCld+aAO+W9cvG3U54eRLTQZFXLdmvXtW424izaVbuFcPN/7vJBBVhOdLZGxevpCSNh9qg==", "path": "pleasantui/4.0.1", "hashPath": "pleasantui.4.0.1.nupkg.sha512"}, "PropertyChanged.SourceGenerator/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Tham7wPyGM9oZWa+xFVScGwsJ+Of+siIfmpVaClOHU/KfIhw5vep/eP3eSLcNj37l0v/havpsS2UBLD0BeMdA==", "path": "propertychanged.sourcegenerator/1.1.0", "hashPath": "propertychanged.sourcegenerator.1.1.0.nupkg.sha512"}, "Pure.DI/2.1.37": {"type": "package", "serviceable": true, "sha512": "sha512-HV9prPvYJgv/wDy/4zSsgiEb/dpR4F21F+K/iUe/+Dw8IZN36xmgRTPvAia3A+Gg5UKk17jeCwIQdE2wfXU/Aw==", "path": "pure.di/2.1.37", "hashPath": "pure.di.2.1.37.nupkg.sha512"}, "QRCoder/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XmPA81eo+oRxBuyVdswsSkTGTE1d3thfF11Z1PdD7oB56A6HU4G4AAOdySmGRMb/cljwlFTMWUtosGEnwpS6GA==", "path": "qrcoder/1.6.0", "hashPath": "qrcoder.1.6.0.nupkg.sha512"}, "R3/1.2.9": {"type": "package", "serviceable": true, "sha512": "sha512-dKMFt90XW+n7JK2P40dx9uuLg57Pcj4cA/9n1NwdKWFcMAM6j49OU8h9EborpVe4KXI+2MV/EjKc1LG7fhQJUA==", "path": "r3/1.2.9", "hashPath": "r3.1.2.9.nupkg.sha512"}, "ReactiveUI/20.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-9hNPknWjijnaSWs6auypoXqUptPZcRpUypF+cf1zD50fgW+SEoQda502N3fVZ2eWPcaiUad+z6GaLwOWmUVHNw==", "path": "reactiveui/20.1.1", "hashPath": "reactiveui.20.1.1.nupkg.sha512"}, "Semi.Avalonia/11.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-G0CRhW80yJ6/t+/gP30m3I/+FR7UL07t51tNycD1keCbRjERyHEZwuz7CpvCwrArjeE/f1+wpgiWWVjDaNnpcA==", "path": "semi.avalonia/11.0.7", "hashPath": "semi.avalonia.11.0.7.nupkg.sha512"}, "Semi.Avalonia.DataGrid/11.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-Zfbkx6H0Bau3p5IgjSru7X9/jNHy1JArvno5kNP7XBRC6TCMp0G2U2BRYr3QPefYC7vpv3JMrLa0G2xGLWqzlw==", "path": "semi.avalonia.datagrid/11.0.7", "hashPath": "semi.avalonia.datagrid.11.0.7.nupkg.sha512"}, "Serilog/4.2.1-dev-02337": {"type": "package", "serviceable": true, "sha512": "sha512-Z/9dysE96W13j3hezabI2tFwQ/uNfqt/c5UBaj72BLFxP/R3MorhJy16xXC99uRg8+8c7W2sdB25yXLZWuE2rg==", "path": "serilog/4.2.1-dev-02337", "hashPath": "serilog.4.2.1-dev-02337.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.1-dev-02317": {"type": "package", "serviceable": true, "sha512": "sha512-1/PPRG1VvYCuFJL8Dc7lkpHNFRZq6n0cwy976CgK21qRwmAIR2GgEkzIc9LZw8TVlvSmoUhZRyeBoU7bB9TjIw==", "path": "serilog.settings.configuration/9.0.1-dev-02317", "hashPath": "serilog.settings.configuration.9.0.1-dev-02317.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "path": "serilog.sinks.debug/3.0.0", "hashPath": "serilog.sinks.debug.3.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "SkiaSharp/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-bRkp3uKp5ZI8gXYQT57uKwil1uobb2p8c69n7v5evlB/2JNcMAXVcw9DZAP5Ig3WSvgzGm2YSn27UVeOi05NlA==", "path": "skiasharp/2.88.8", "hashPath": "skiasharp.2.88.8.nupkg.sha512"}, "SkiaSharp.HarfBuzz/2.88.6": {"type": "package", "serviceable": true, "sha512": "sha512-s5yZTHdqbXKiTL06ns6zW3asELfX60dEceA4ZdYmNlOkz/OyWDfdjHAuu4HGDA7Mxx5iaUEzDZgPkEe+OVr/jg==", "path": "skiasharp.harfbuzz/2.88.6", "hashPath": "skiasharp.harfbuzz.2.88.6.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-0FO6YA7paNFBMJULvEyecPmCvL9/STvOAi5VOUw2srqJ7pNTbiiZkfl7sulAzcumbWgfzaVjRXYTgMj7SoUnWQ==", "path": "skiasharp.nativeassets.linux/2.88.8", "hashPath": "skiasharp.nativeassets.linux.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-6Kn5TSkKlfyS6azWHF3Jk2sW5C4jCE5uSshM/5AbfFrR+5n6qM5XEnz9h4VaVl7LTxBvHvMkuPb/3bpbq0vxTw==", "path": "skiasharp.nativeassets.macos/2.88.8", "hashPath": "skiasharp.nativeassets.macos.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-S3qRo8c+gVYOyfrdf6FYnjx/ft+gPkb4dNY2IPv5Oy5yNBhDhXhKqHFr9h4+ne6ZU+7D4dbuRQqsIqCo8u1/DA==", "path": "skiasharp.nativeassets.webassembly/2.88.8", "hashPath": "skiasharp.nativeassets.webassembly.2.88.8.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.8": {"type": "package", "serviceable": true, "sha512": "sha512-O9QXoWEXA+6cweR4h3BOnwMz+pO9vL9mXdjLrpDd0w1QzCgWmLQBxa1VgySDITiH7nQndrDG1h6937zm9pLj1Q==", "path": "skiasharp.nativeassets.win32/2.88.8", "hashPath": "skiasharp.nativeassets.win32.2.88.8.nupkg.sha512"}, "Splat/15.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RHDTdF90FwVbRia2cmuIzkiVoETqnXSB2dDBBi/I35HWXqv4OKGqoMcfcd6obMvO2OmmY5PjU1M62K8LkJafAA==", "path": "splat/15.1.1", "hashPath": "splat.15.1.1.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WvRUdlL1lB0dTRZSs5XcQOd5q9MYNk90GkbmRmiCvRHThWiojkpGqWdmEDJdXyHbxG/BhE5hmVbMfRLXW9FJVA==", "path": "system.configuration.configurationmanager/7.0.0", "hashPath": "system.configuration.configurationmanager.7.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUDP47obqQm3SFJfP6z+Fx2nJ4KKTQbXB4Q9Uesnzw9SbYdhjyoGXuvDn/gEmFY6N5Z3bFFbpAQGA7m6hrYJCw==", "path": "system.diagnostics.eventlog/7.0.0", "hashPath": "system.diagnostics.eventlog.7.0.0.nupkg.sha512"}, "System.Drawing.Common/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K<PERSON>+oBU38pxkKPxvLcLfIkOV5Ien8ReN78wro7OF5/erwcmortzeFx+iBswlh2Vz6gVne0khocQudGwaO1Ey6A==", "path": "system.drawing.common/7.0.0", "hashPath": "system.drawing.common.7.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-9XayJBAY+Bs7+ISFJ6BXJuCf6q9DyP8rsn/D1uNaKHDLyADjeKLRDa5YbFRfSBo/+8cuMzlD2VIUR4VuAPoK7w==", "path": "system.identitymodel.tokens.jwt/7.4.0", "hashPath": "system.identitymodel.tokens.jwt.7.4.0.nupkg.sha512"}, "System.Reactive/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rHaWtKDwCi9qJ3ObKo8LHPMuuwv33YbmQi7TcUK1C264V3MFnOr5Im7QgCTdLniztP3GJyeiSg5x8NqYJFqRmg==", "path": "system.reactive/6.0.1", "hashPath": "system.reactive.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xSPiLNlHT6wAHtugASbKAJwV5GVqQK351crnILAucUioFqqieDN79evO1rku1ckt/GfjIn+b17UaSskoY03JuA==", "path": "system.security.cryptography.protecteddata/7.0.0", "hashPath": "system.security.cryptography.protecteddata.7.0.0.nupkg.sha512"}, "System.Security.Permissions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vmp0iRmCEno9BWiskOW5pxJ3d9n+jUqKxvX4GhLwFhnQaySZmBN2FuC0N5gjFHgyFMUjC5sfIJ8KZfoJwkcMmA==", "path": "system.security.permissions/7.0.0", "hashPath": "system.security.permissions.7.0.0.nupkg.sha512"}, "System.Text.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-eqWHDZqYPv1PvuvoIIx5pF74plL3iEOZOl/0kQP+Y0TEbtgNnM2W6k8h8EPYs+LTJZsXuWa92n5W5sHTWvE3VA==", "path": "system.text.json/9.0.1", "hashPath": "system.text.json.9.0.1.nupkg.sha512"}, "System.Windows.Extensions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bR4qdCmssMMbo9Fatci49An5B1UaVJZHKNq70PRgzoLYIlitb8Tj7ns/Xt5Pz1CkERiTjcVBDU2y1AVrPBYkaw==", "path": "system.windows.extensions/7.0.0", "hashPath": "system.windows.extensions.7.0.0.nupkg.sha512"}, "SuperNova.Runtime/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TicketSalesApp.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TicketSalesApp.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}