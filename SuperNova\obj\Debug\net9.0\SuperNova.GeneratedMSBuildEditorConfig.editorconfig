is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property.TargetFrameworkIdentifier = .NETCoreApp
build_property.TargetFrameworkVersion = v9.0
build_property.RootNamespace = SuperNova
build_property.ProjectDir = G:\codesecondfolder\BRU-Avtopark-Avtobusov\SuperNova\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Controls/MDI/MDIHost.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Controls/MDI/MDIWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Controls/Properties/PropertyBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Controls/ResizeAdorner.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/BusManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/EmployeeManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/IncomeReportWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/JobManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/MaintenanceManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/RouteManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/SalesManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/SalesStatisticsWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/TicketManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/AdministratorUi/Views/UserManagementWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/AboutWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/AddInManagerView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/AddProcedureView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/AuthWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/CodeEditorView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/ComponentsView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/HelpWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/NewProjectView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/OptionsView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/ProjectPropertiesView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/ReferencesView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/RuntimeErrorView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/SaveChangesView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Forms/Views/SplashScreen.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/IDE/DialogWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/IDE/MDIControllerView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/MainView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/ColorPalette/ColorPaletteToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/FormLayout/FormLayoutToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Immediate/ImmediateToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Locals/LocalsToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Navigation/NavigationToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Projects/ProjectToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Properties/PropertiesToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Reports/ReportsToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/Tools/Watches/WatchesToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/VisualDesigner/Views/FormEditView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/VisualDesigner/Views/MenuEditorView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/VisualDesigner/Views/PropertiesToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[G:/codesecondfolder/BRU-Avtopark-Avtobusov/SuperNova/VisualDesigner/Views/ToolBoxToolView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
