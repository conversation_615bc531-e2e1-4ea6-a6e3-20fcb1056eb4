2025-07-22 17:26:33.179 +03:00 [INF] Starting web application
2025-07-22 17:26:33.231 +03:00 [INF] Starting web host
2025-07-22 17:26:33.288 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:26:34.565 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:26:34.590 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.594 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.599 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.607 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.609 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:35.018 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:26:35.349 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:26:35.377 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:26:35.379 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:26:35.449 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:26:35.476 +03:00 [INF] Hosting environment: Development
2025-07-22 17:26:35.478 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:26:36.801 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:26:36.838 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 41.9031ms
2025-07-22 17:26:36.844 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:26:36.888 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 44.7478ms
2025-07-22 17:26:51.988 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:26:52.104 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:26:52.120 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:26:52.253 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:26:52.258 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:26:52.263 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:26:52.425 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:26:52.523 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:26:52.529 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:26:52.535 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:26:52.5347249Z"
2025-07-22 17:26:52.560 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:26:52.565 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 306.7392ms.
2025-07-22 17:26:52.586 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:26:52.595 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 455.5726ms
2025-07-22 17:26:52.598 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:26:52.619 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 630.8076ms
2025-07-22 17:27:00.185 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Tickets - null null
2025-07-22 17:27:00.240 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 17:27:00.244 +03:00 [INF] Route matched with {action = "GetTickets", controller = "Tickets"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Bilet]]] GetTickets() on controller TicketSalesApp.AdminServer.Controllers.TicketsController (TicketSalesApp.AdminServer).
2025-07-22 17:27:00.283 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:27:00.286 +03:00 [INF] Fetching all tickets with route information
2025-07-22 17:27:00.435 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone"
FROM "Bilety" AS "b"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
LEFT JOIN "Prodazhi" AS "p" ON "b"."TicketId" = "p"."TicketId"
ORDER BY "b"."TicketId", "m"."RouteId"
2025-07-22 17:27:00.731 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 446.1685ms.
2025-07-22 17:27:00.746 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Bilet, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:27:00.763 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) in 503.0684ms
2025-07-22 17:27:00.766 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 17:27:00.778 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Tickets - 200 null application/json; charset=utf-8 592.6785ms
2025-07-22 17:27:00.781 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Routes - null null
2025-07-22 17:27:00.801 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 17:27:00.806 +03:00 [INF] Route matched with {action = "GetRoutes", controller = "Routes"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Marshut]]] GetRoutes() on controller TicketSalesApp.AdminServer.Controllers.RoutesController (TicketSalesApp.AdminServer).
2025-07-22 17:27:00.835 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:27:00.838 +03:00 [INF] Fetching all routes with their related data
2025-07-22 17:27:00.907 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "a"."BusId", "a"."Model", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "b"."TicketId", "b"."RouteId", "b"."TicketPrice"
FROM "Marshuti" AS "m"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
LEFT JOIN "Bilety" AS "b" ON "m"."RouteId" = "b"."RouteId"
ORDER BY "m"."RouteId", "a"."BusId", "e"."EmpId"
2025-07-22 17:27:01.121 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 284.2057ms.
2025-07-22 17:27:01.134 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Marshut, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:27:01.139 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) in 318.3924ms
2025-07-22 17:27:01.141 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 17:27:01.153 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Routes - 200 null application/json; charset=utf-8 372.2571ms
2025-07-22 17:27:25.993 +03:00 [INF] Starting web application
2025-07-22 17:27:26.034 +03:00 [INF] Starting web host
2025-07-22 17:27:26.073 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:27:27.149 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:27:27.192 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.195 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.198 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.205 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.208 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.613 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:27:27.865 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:27:27.891 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:27:27.894 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:27:27.958 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:27:27.960 +03:00 [INF] Hosting environment: Development
2025-07-22 17:27:27.961 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:27:29.744 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:27:29.776 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 36.3232ms
2025-07-22 17:27:29.782 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:27:29.816 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 34.5114ms
2025-07-22 17:27:40.451 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:27:40.535 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:27:40.549 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:27:40.665 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:27:40.669 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:27:40.673 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:27:40.769 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:27:40.834 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:27:40.839 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:27:40.845 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:27:40.8446032Z"
2025-07-22 17:27:40.914 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:27:40.919 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 249.4654ms.
2025-07-22 17:27:40.937 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:27:40.944 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 376.7419ms
2025-07-22 17:27:40.946 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:27:40.960 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 508.5573ms
2025-07-22 17:29:02.577 +03:00 [INF] Starting web application
2025-07-22 17:29:02.613 +03:00 [INF] Starting web host
2025-07-22 17:29:02.657 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:29:03.735 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:29:03.753 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.756 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.759 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.767 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.769 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:04.120 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:29:04.440 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:29:04.466 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:29:04.468 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:29:04.537 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:29:04.540 +03:00 [INF] Hosting environment: Development
2025-07-22 17:29:04.542 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:29:06.382 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:29:06.416 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 38.1302ms
2025-07-22 17:29:06.422 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:29:06.462 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 39.5968ms
2025-07-22 17:29:19.423 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:29:19.520 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:29:19.533 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:29:19.653 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:29:19.658 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:29:19.661 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:29:19.752 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:29:19.820 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:29:19.825 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:29:19.831 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:29:19.8315093Z"
2025-07-22 17:29:19.853 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:29:19.858 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 200.1696ms.
2025-07-22 17:29:19.878 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:29:19.886 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 334.2512ms
2025-07-22 17:29:19.889 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:29:19.907 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 483.6726ms
2025-07-22 17:29:25.216 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Users - null null
2025-07-22 17:29:25.267 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer)'
2025-07-22 17:29:25.272 +03:00 [INF] Route matched with {action = "GetUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.User]]] GetUsers() on controller TicketSalesApp.AdminServer.Controllers.UsersController (TicketSalesApp.AdminServer).
2025-07-22 17:29:25.307 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:29:25.315 +03:00 [INF] Fetching all users
2025-07-22 17:29:25.389 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
2025-07-22 17:29:25.408 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 98.6664ms.
2025-07-22 17:29:25.421 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.User, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:29:25.435 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer) in 149.207ms
2025-07-22 17:29:25.437 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer)'
2025-07-22 17:29:25.449 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Users - 200 null application/json; charset=utf-8 233.1683ms
2025-07-22 17:33:25.548 +03:00 [INF] Starting web application
2025-07-22 17:33:25.604 +03:00 [INF] Starting web host
2025-07-22 17:33:25.740 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:33:27.138 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:33:27.157 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.160 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.163 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.169 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.171 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.558 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:33:28.028 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:33:28.142 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:33:28.149 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:33:28.242 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:33:28.245 +03:00 [INF] Hosting environment: Development
2025-07-22 17:33:28.248 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:33:51.462 +03:00 [INF] Starting web application
2025-07-22 17:33:51.490 +03:00 [INF] Starting web host
2025-07-22 17:33:51.535 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:33:52.654 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:33:52.669 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.672 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.674 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.679 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.681 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:53.125 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:33:53.370 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:33:53.391 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:33:53.393 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:33:53.448 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:33:53.450 +03:00 [INF] Hosting environment: Development
2025-07-22 17:33:53.452 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:34:14.485 +03:00 [INF] Starting web application
2025-07-22 17:34:14.507 +03:00 [INF] Starting web host
2025-07-22 17:34:14.543 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:34:15.409 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:34:15.423 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.426 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.427 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.432 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.434 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.792 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:34:16.061 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:34:16.108 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:34:16.110 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:34:16.169 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:34:16.196 +03:00 [INF] Hosting environment: Development
2025-07-22 17:34:16.198 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:35:06.064 +03:00 [INF] Starting web application
2025-07-22 17:35:06.093 +03:00 [INF] Starting web host
2025-07-22 17:35:06.134 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:35:07.043 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:35:07.057 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.059 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.061 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.067 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.069 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.405 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:35:07.670 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:35:07.693 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:35:07.696 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:35:07.759 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:35:07.762 +03:00 [INF] Hosting environment: Development
2025-07-22 17:35:07.766 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:35:09.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:35:09.967 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 48.6767ms
2025-07-22 17:35:09.975 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:35:10.009 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 34.1431ms
2025-07-22 17:35:21.391 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:35:21.512 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:35:21.527 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:35:21.654 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:35:21.659 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:35:21.664 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:35:21.774 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:35:21.850 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:35:21.857 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:35:21.862 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:35:21.8621773Z"
2025-07-22 17:35:21.887 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:35:21.892 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 233.5537ms.
2025-07-22 17:35:21.931 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:35:21.938 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 392.6198ms
2025-07-22 17:35:21.942 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:35:21.958 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 567.0961ms
2025-07-22 17:35:35.992 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 17:35:36.043 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:35:36.050 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 17:35:36.259 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:35:36.265 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 17:35:36.411 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 17:35:36.427 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 166.8941ms.
2025-07-22 17:35:36.440 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:35:36.452 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 389.5612ms
2025-07-22 17:35:36.454 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:35:36.468 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 475.7002ms
