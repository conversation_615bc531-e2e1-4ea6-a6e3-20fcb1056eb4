2025-07-22 17:26:33.179 +03:00 [INF] Starting web application
2025-07-22 17:26:33.231 +03:00 [INF] Starting web host
2025-07-22 17:26:33.288 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:26:34.565 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:26:34.590 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.594 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.599 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.607 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:34.609 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:26:35.018 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:26:35.349 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:26:35.377 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:26:35.379 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:26:35.449 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:26:35.476 +03:00 [INF] Hosting environment: Development
2025-07-22 17:26:35.478 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:26:36.801 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:26:36.838 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 41.9031ms
2025-07-22 17:26:36.844 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:26:36.888 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 44.7478ms
2025-07-22 17:26:51.988 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:26:52.104 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:26:52.120 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:26:52.253 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:26:52.258 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:26:52.263 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:26:52.425 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:26:52.523 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:26:52.529 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:26:52.535 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:26:52.5347249Z"
2025-07-22 17:26:52.560 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:26:52.565 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 306.7392ms.
2025-07-22 17:26:52.586 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:26:52.595 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 455.5726ms
2025-07-22 17:26:52.598 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:26:52.619 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 630.8076ms
2025-07-22 17:27:00.185 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Tickets - null null
2025-07-22 17:27:00.240 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 17:27:00.244 +03:00 [INF] Route matched with {action = "GetTickets", controller = "Tickets"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Bilet]]] GetTickets() on controller TicketSalesApp.AdminServer.Controllers.TicketsController (TicketSalesApp.AdminServer).
2025-07-22 17:27:00.283 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:27:00.286 +03:00 [INF] Fetching all tickets with route information
2025-07-22 17:27:00.435 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone"
FROM "Bilety" AS "b"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
LEFT JOIN "Prodazhi" AS "p" ON "b"."TicketId" = "p"."TicketId"
ORDER BY "b"."TicketId", "m"."RouteId"
2025-07-22 17:27:00.731 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 446.1685ms.
2025-07-22 17:27:00.746 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Bilet, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:27:00.763 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) in 503.0684ms
2025-07-22 17:27:00.766 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 17:27:00.778 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Tickets - 200 null application/json; charset=utf-8 592.6785ms
2025-07-22 17:27:00.781 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Routes - null null
2025-07-22 17:27:00.801 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 17:27:00.806 +03:00 [INF] Route matched with {action = "GetRoutes", controller = "Routes"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Marshut]]] GetRoutes() on controller TicketSalesApp.AdminServer.Controllers.RoutesController (TicketSalesApp.AdminServer).
2025-07-22 17:27:00.835 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:27:00.838 +03:00 [INF] Fetching all routes with their related data
2025-07-22 17:27:00.907 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "a"."BusId", "a"."Model", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "b"."TicketId", "b"."RouteId", "b"."TicketPrice"
FROM "Marshuti" AS "m"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
LEFT JOIN "Bilety" AS "b" ON "m"."RouteId" = "b"."RouteId"
ORDER BY "m"."RouteId", "a"."BusId", "e"."EmpId"
2025-07-22 17:27:01.121 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 284.2057ms.
2025-07-22 17:27:01.134 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Marshut, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:27:01.139 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) in 318.3924ms
2025-07-22 17:27:01.141 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 17:27:01.153 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Routes - 200 null application/json; charset=utf-8 372.2571ms
2025-07-22 17:27:25.993 +03:00 [INF] Starting web application
2025-07-22 17:27:26.034 +03:00 [INF] Starting web host
2025-07-22 17:27:26.073 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:27:27.149 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:27:27.192 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.195 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.198 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.205 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.208 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:27:27.613 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:27:27.865 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:27:27.891 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:27:27.894 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:27:27.958 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:27:27.960 +03:00 [INF] Hosting environment: Development
2025-07-22 17:27:27.961 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:27:29.744 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:27:29.776 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 36.3232ms
2025-07-22 17:27:29.782 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:27:29.816 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 34.5114ms
2025-07-22 17:27:40.451 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:27:40.535 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:27:40.549 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:27:40.665 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:27:40.669 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:27:40.673 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:27:40.769 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:27:40.834 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:27:40.839 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:27:40.845 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:27:40.8446032Z"
2025-07-22 17:27:40.914 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:27:40.919 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 249.4654ms.
2025-07-22 17:27:40.937 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:27:40.944 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 376.7419ms
2025-07-22 17:27:40.946 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:27:40.960 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 508.5573ms
2025-07-22 17:29:02.577 +03:00 [INF] Starting web application
2025-07-22 17:29:02.613 +03:00 [INF] Starting web host
2025-07-22 17:29:02.657 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:29:03.735 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:29:03.753 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.756 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.759 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.767 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:03.769 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:29:04.120 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:29:04.440 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:29:04.466 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:29:04.468 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:29:04.537 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:29:04.540 +03:00 [INF] Hosting environment: Development
2025-07-22 17:29:04.542 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:29:06.382 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:29:06.416 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 38.1302ms
2025-07-22 17:29:06.422 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:29:06.462 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 39.5968ms
2025-07-22 17:29:19.423 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:29:19.520 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:29:19.533 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:29:19.653 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:29:19.658 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:29:19.661 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:29:19.752 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:29:19.820 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:29:19.825 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:29:19.831 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:29:19.8315093Z"
2025-07-22 17:29:19.853 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:29:19.858 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 200.1696ms.
2025-07-22 17:29:19.878 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:29:19.886 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 334.2512ms
2025-07-22 17:29:19.889 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:29:19.907 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 483.6726ms
2025-07-22 17:29:25.216 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Users - null null
2025-07-22 17:29:25.267 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer)'
2025-07-22 17:29:25.272 +03:00 [INF] Route matched with {action = "GetUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.User]]] GetUsers() on controller TicketSalesApp.AdminServer.Controllers.UsersController (TicketSalesApp.AdminServer).
2025-07-22 17:29:25.307 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:29:25.315 +03:00 [INF] Fetching all users
2025-07-22 17:29:25.389 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
2025-07-22 17:29:25.408 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 98.6664ms.
2025-07-22 17:29:25.421 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.User, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:29:25.435 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer) in 149.207ms
2025-07-22 17:29:25.437 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.UsersController.GetUsers (TicketSalesApp.AdminServer)'
2025-07-22 17:29:25.449 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Users - 200 null application/json; charset=utf-8 233.1683ms
2025-07-22 17:33:25.548 +03:00 [INF] Starting web application
2025-07-22 17:33:25.604 +03:00 [INF] Starting web host
2025-07-22 17:33:25.740 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:33:27.138 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:33:27.157 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.160 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.163 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.169 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.171 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:27.558 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:33:28.028 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:33:28.142 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:33:28.149 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:33:28.242 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:33:28.245 +03:00 [INF] Hosting environment: Development
2025-07-22 17:33:28.248 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:33:51.462 +03:00 [INF] Starting web application
2025-07-22 17:33:51.490 +03:00 [INF] Starting web host
2025-07-22 17:33:51.535 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:33:52.654 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:33:52.669 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.672 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.674 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.679 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:52.681 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:33:53.125 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:33:53.370 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:33:53.391 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:33:53.393 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:33:53.448 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:33:53.450 +03:00 [INF] Hosting environment: Development
2025-07-22 17:33:53.452 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:34:14.485 +03:00 [INF] Starting web application
2025-07-22 17:34:14.507 +03:00 [INF] Starting web host
2025-07-22 17:34:14.543 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:34:15.409 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:34:15.423 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.426 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.427 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.432 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.434 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:34:15.792 +03:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:34:16.061 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:34:16.108 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:34:16.110 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:34:16.169 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:34:16.196 +03:00 [INF] Hosting environment: Development
2025-07-22 17:34:16.198 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:35:06.064 +03:00 [INF] Starting web application
2025-07-22 17:35:06.093 +03:00 [INF] Starting web host
2025-07-22 17:35:06.134 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:35:07.043 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:35:07.057 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.059 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.061 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.067 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.069 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:35:07.405 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:35:07.670 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:35:07.693 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:35:07.696 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:35:07.759 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:35:07.762 +03:00 [INF] Hosting environment: Development
2025-07-22 17:35:07.766 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:35:09.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:35:09.967 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 48.6767ms
2025-07-22 17:35:09.975 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:35:10.009 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 34.1431ms
2025-07-22 17:35:21.391 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:35:21.512 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:35:21.527 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:35:21.654 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:35:21.659 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:35:21.664 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:35:21.774 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:35:21.850 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:35:21.857 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:35:21.862 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:35:21.8621773Z"
2025-07-22 17:35:21.887 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:35:21.892 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 233.5537ms.
2025-07-22 17:35:21.931 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:35:21.938 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 392.6198ms
2025-07-22 17:35:21.942 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:35:21.958 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 567.0961ms
2025-07-22 17:35:35.992 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 17:35:36.043 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:35:36.050 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 17:35:36.259 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:35:36.265 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 17:35:36.411 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 17:35:36.427 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 166.8941ms.
2025-07-22 17:35:36.440 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:35:36.452 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 389.5612ms
2025-07-22 17:35:36.454 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:35:36.468 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 475.7002ms
2025-07-22 17:39:25.934 +03:00 [INF] Starting web application
2025-07-22 17:39:25.961 +03:00 [INF] Starting web host
2025-07-22 17:39:26.008 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:39:26.986 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:39:27.008 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:39:27.011 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:39:27.013 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:39:27.020 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:39:27.022 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:39:27.367 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:39:27.657 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:39:27.681 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:39:27.683 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:39:27.741 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:39:27.743 +03:00 [INF] Hosting environment: Development
2025-07-22 17:39:27.746 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:39:30.019 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:39:30.053 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 38.4896ms
2025-07-22 17:39:30.059 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:39:30.158 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 99.4638ms
2025-07-22 17:39:41.964 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:39:42.225 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:39:42.241 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:39:42.370 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:39:42.375 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:39:42.379 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:39:42.468 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:39:42.551 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:39:42.557 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:39:42.563 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:39:42.5631239Z"
2025-07-22 17:39:42.585 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:39:42.590 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 214.9045ms.
2025-07-22 17:39:42.608 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:39:42.616 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 355.1784ms
2025-07-22 17:39:42.619 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:39:42.635 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 671.3663ms
2025-07-22 17:39:55.303 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 17:39:55.354 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:39:55.360 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 17:39:55.479 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:39:55.487 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 17:39:55.684 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 17:39:55.698 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 216.327ms.
2025-07-22 17:39:55.711 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:39:55.722 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 348.3479ms
2025-07-22 17:39:55.725 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:39:55.738 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 435.2191ms
2025-07-22 17:39:59.772 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2008-06-22&endDate=2025-07-22 - null null
2025-07-22 17:39:59.818 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:39:59.820 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 17:39:59.954 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:39:59.956 +03:00 [INF] Searching sales with start date: 22.06.2008 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 17:39:59.983 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2008-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 17:40:00.359 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 403.5025ms.
2025-07-22 17:40:00.373 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:40:00.384 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 542.6164ms
2025-07-22 17:40:00.387 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:40:00.402 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2008-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 629.8072ms
2025-07-22 17:47:59.046 +03:00 [INF] Starting web application
2025-07-22 17:47:59.077 +03:00 [INF] Starting web host
2025-07-22 17:47:59.125 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 17:48:00.407 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 17:48:00.429 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:48:00.433 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:48:00.436 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:48:00.444 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:48:00.450 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 17:48:00.855 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 17:48:01.384 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 17:48:01.413 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 17:48:01.415 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 17:48:01.490 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 17:48:01.492 +03:00 [INF] Hosting environment: Development
2025-07-22 17:48:01.496 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 17:48:03.650 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 17:48:03.686 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 41.7035ms
2025-07-22 17:48:03.693 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 17:48:03.732 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 38.8518ms
2025-07-22 17:48:15.409 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 17:48:15.550 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:48:15.566 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 17:48:15.701 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:48:15.706 +03:00 [INF] Login attempt started for user admin
2025-07-22 17:48:15.710 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 17:48:15.856 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 17:48:15.927 +03:00 [INF] User admin authenticated successfully
2025-07-22 17:48:15.932 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 17:48:15.937 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T16:48:15.9371013Z"
2025-07-22 17:48:15.959 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 17:48:15.963 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 258.2766ms.
2025-07-22 17:48:15.981 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 17:48:15.988 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 402.2978ms
2025-07-22 17:48:15.990 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 17:48:16.002 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 593.367ms
2025-07-22 17:48:24.828 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 17:48:24.946 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:48:24.957 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 17:48:25.110 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:48:25.118 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 17:48:25.288 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 17:48:25.313 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 201.2235ms.
2025-07-22 17:48:25.338 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:48:25.352 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 373.7484ms
2025-07-22 17:48:25.355 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:48:25.367 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 541.0527ms
2025-07-22 17:48:26.680 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2013-06-22&endDate=2025-07-22 - null null
2025-07-22 17:48:26.699 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:48:26.701 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 17:48:26.800 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 17:48:26.802 +03:00 [INF] Searching sales with start date: 22.06.2013 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 17:48:26.825 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2013-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 17:48:27.287 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 485.3099ms.
2025-07-22 17:48:27.299 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 17:48:27.310 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 596.7514ms
2025-07-22 17:48:27.312 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 17:48:27.322 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2013-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 642.2793ms
2025-07-22 18:24:28.809 +03:00 [INF] Starting web application
2025-07-22 18:24:28.838 +03:00 [INF] Starting web host
2025-07-22 18:24:28.909 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:24:30.399 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:24:30.420 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:24:30.423 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:24:30.426 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:24:30.431 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:24:30.434 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:24:31.044 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:24:31.515 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:24:31.574 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:24:31.576 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:24:31.660 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:24:31.663 +03:00 [INF] Hosting environment: Development
2025-07-22 18:24:31.665 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:24:33.189 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:24:33.268 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 84.883ms
2025-07-22 18:24:33.276 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:24:33.312 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 36.1838ms
2025-07-22 18:24:47.390 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:24:47.647 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:24:47.694 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:24:47.839 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:24:47.845 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:24:47.849 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:24:47.988 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:24:48.154 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:24:48.159 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:24:48.165 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:24:48.1647857Z"
2025-07-22 18:24:48.198 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:24:48.204 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 358.342ms.
2025-07-22 18:24:48.225 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:24:48.235 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 520.6262ms
2025-07-22 18:24:48.237 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:24:48.253 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 862.8016ms
2025-07-22 18:24:55.006 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Tickets - null null
2025-07-22 18:24:55.066 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 18:24:55.071 +03:00 [INF] Route matched with {action = "GetTickets", controller = "Tickets"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Bilet]]] GetTickets() on controller TicketSalesApp.AdminServer.Controllers.TicketsController (TicketSalesApp.AdminServer).
2025-07-22 18:24:55.125 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:24:55.128 +03:00 [INF] Fetching all tickets with route information
2025-07-22 18:24:55.299 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone"
FROM "Bilety" AS "b"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
LEFT JOIN "Prodazhi" AS "p" ON "b"."TicketId" = "p"."TicketId"
ORDER BY "b"."TicketId", "m"."RouteId"
2025-07-22 18:24:55.577 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 449.825ms.
2025-07-22 18:24:55.588 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Bilet, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:24:55.607 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) in 521.2421ms
2025-07-22 18:24:55.609 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 18:24:55.619 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Tickets - 200 null application/json; charset=utf-8 612.8047ms
2025-07-22 18:24:55.622 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Routes - null null
2025-07-22 18:24:55.639 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 18:24:55.643 +03:00 [INF] Route matched with {action = "GetRoutes", controller = "Routes"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Marshut]]] GetRoutes() on controller TicketSalesApp.AdminServer.Controllers.RoutesController (TicketSalesApp.AdminServer).
2025-07-22 18:24:55.676 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:24:55.680 +03:00 [INF] Fetching all routes with their related data
2025-07-22 18:24:55.830 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "a"."BusId", "a"."Model", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "b"."TicketId", "b"."RouteId", "b"."TicketPrice"
FROM "Marshuti" AS "m"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
LEFT JOIN "Bilety" AS "b" ON "m"."RouteId" = "b"."RouteId"
ORDER BY "m"."RouteId", "a"."BusId", "e"."EmpId"
2025-07-22 18:24:56.030 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 351.49ms.
2025-07-22 18:24:56.041 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Marshut, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:24:56.044 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) in 384.3082ms
2025-07-22 18:24:56.046 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 18:24:56.057 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Routes - 200 null application/json; charset=utf-8 435.2956ms
2025-07-22 18:25:05.043 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 18:25:05.062 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:25:05.069 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:25:05.190 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:25:05.200 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:25:05.287 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:25:05.300 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 107.7707ms.
2025-07-22 18:25:05.312 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:25:05.315 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 231.512ms
2025-07-22 18:25:05.317 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:25:05.326 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 283.0698ms
2025-07-22 18:25:07.100 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2009-06-21&endDate=2025-07-22 - null null
2025-07-22 18:25:07.121 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:25:07.123 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:25:07.253 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:25:07.255 +03:00 [INF] Searching sales with start date: 21.06.2009 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:25:07.282 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2009-06-21T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:25:07.569 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 314.1634ms.
2025-07-22 18:25:07.582 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:25:07.585 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 448.6467ms
2025-07-22 18:25:07.587 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:25:07.598 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2009-06-21&endDate=2025-07-22 - 200 null application/json; charset=utf-8 498.1412ms
2025-07-22 18:30:01.411 +03:00 [INF] Starting web application
2025-07-22 18:30:01.446 +03:00 [INF] Starting web host
2025-07-22 18:30:01.560 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:30:02.682 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:30:02.697 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:30:02.699 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:30:02.702 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:30:02.707 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:30:02.709 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:30:03.058 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:30:03.492 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:30:03.516 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:30:03.518 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:30:03.583 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:30:03.585 +03:00 [INF] Hosting environment: Development
2025-07-22 18:30:03.587 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:30:05.490 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:30:05.550 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 63.8709ms
2025-07-22 18:30:05.556 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:30:05.591 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 34.8805ms
2025-07-22 18:30:16.477 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:30:16.564 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:30:16.577 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:30:16.734 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:30:16.741 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:30:16.763 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:30:16.955 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:30:17.043 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:30:17.049 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:30:17.055 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:30:17.0550227Z"
2025-07-22 18:30:17.079 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:30:17.084 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 342.9876ms.
2025-07-22 18:30:17.104 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:30:17.112 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 518.0985ms
2025-07-22 18:30:17.115 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:30:17.131 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 653.7538ms
2025-07-22 18:30:21.922 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 18:30:21.975 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:30:21.983 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:30:22.119 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:30:22.128 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:30:22.301 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:30:22.318 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 195.5709ms.
2025-07-22 18:30:22.334 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:30:22.353 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 358.0689ms
2025-07-22 18:30:22.356 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:30:22.367 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 444.9925ms
2025-07-22 18:30:23.782 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2013-06-19&endDate=2025-07-22 - null null
2025-07-22 18:30:23.802 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:30:23.804 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:30:23.897 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:30:23.899 +03:00 [INF] Searching sales with start date: 19.06.2013 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:30:23.930 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2013-06-19T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:30:24.301 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 401.7883ms.
2025-07-22 18:30:24.315 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:30:24.323 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 507.1234ms
2025-07-22 18:30:24.325 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:30:24.337 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2013-06-19&endDate=2025-07-22 - 200 null application/json; charset=utf-8 554.5704ms
2025-07-22 18:36:31.554 +03:00 [INF] Starting web application
2025-07-22 18:36:31.580 +03:00 [INF] Starting web host
2025-07-22 18:36:31.644 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:36:32.676 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:36:32.782 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:36:32.804 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:36:32.807 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:36:32.814 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:36:32.816 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:36:33.175 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:36:33.439 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:36:33.462 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:36:33.464 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:36:33.527 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:36:33.529 +03:00 [INF] Hosting environment: Development
2025-07-22 18:36:33.531 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:36:35.243 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:36:35.274 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 36.077ms
2025-07-22 18:36:35.280 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:36:35.313 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 32.9496ms
2025-07-22 18:36:46.930 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:36:47.034 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:36:47.047 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:36:47.195 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:36:47.201 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:36:47.205 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:36:47.422 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:36:47.500 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:36:47.506 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:36:47.513 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:36:47.5127067Z"
2025-07-22 18:36:47.545 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:36:47.551 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 349.8998ms.
2025-07-22 18:36:47.582 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:36:47.592 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 521.9084ms
2025-07-22 18:36:47.595 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:36:47.614 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 684.0949ms
2025-07-22 18:36:52.500 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 18:36:52.594 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:36:52.614 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:36:52.773 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:36:52.781 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:36:52.954 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:36:52.969 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 193.7061ms.
2025-07-22 18:36:52.982 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:36:52.993 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 358.76ms
2025-07-22 18:36:52.996 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:36:53.012 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 512.5889ms
2025-07-22 18:36:55.172 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2013-06-13&endDate=2025-07-22 - null null
2025-07-22 18:36:55.206 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:36:55.209 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:36:55.347 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:36:55.350 +03:00 [INF] Searching sales with start date: 13.06.2013 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:36:55.379 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__startDate_Value_0='2013-06-13T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:36:55.891 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 541.6644ms.
2025-07-22 18:36:55.907 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:36:55.915 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 681.3333ms
2025-07-22 18:36:55.919 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:36:55.931 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2013-06-13&endDate=2025-07-22 - 200 null application/json; charset=utf-8 758.7532ms
2025-07-22 18:43:36.696 +03:00 [INF] Starting web application
2025-07-22 18:43:36.725 +03:00 [INF] Starting web host
2025-07-22 18:43:36.783 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:43:38.181 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:43:38.219 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:43:38.222 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:43:38.225 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:43:38.234 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:43:38.237 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:43:38.611 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:43:38.974 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:43:39.000 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:43:39.003 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:43:39.067 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:43:39.069 +03:00 [INF] Hosting environment: Development
2025-07-22 18:43:39.072 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:43:40.558 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:43:40.603 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 51.3006ms
2025-07-22 18:43:40.610 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:43:40.659 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 48.8798ms
2025-07-22 18:43:51.996 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:43:52.128 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:43:52.143 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:43:52.270 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:43:52.274 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:43:52.277 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:43:52.452 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:43:52.531 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:43:52.536 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:43:52.541 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:43:52.5413797Z"
2025-07-22 18:43:52.562 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:43:52.567 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 293.9749ms.
2025-07-22 18:43:52.587 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:43:52.596 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 431.9088ms
2025-07-22 18:43:52.598 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:43:52.617 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 620.4243ms
2025-07-22 18:43:57.503 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Tickets - null null
2025-07-22 18:43:57.557 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 18:43:57.562 +03:00 [INF] Route matched with {action = "GetTickets", controller = "Tickets"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Bilet]]] GetTickets() on controller TicketSalesApp.AdminServer.Controllers.TicketsController (TicketSalesApp.AdminServer).
2025-07-22 18:43:57.599 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:43:57.602 +03:00 [INF] Fetching all tickets with route information
2025-07-22 18:43:57.789 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone"
FROM "Bilety" AS "b"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
LEFT JOIN "Prodazhi" AS "p" ON "b"."TicketId" = "p"."TicketId"
ORDER BY "b"."TicketId", "m"."RouteId"
2025-07-22 18:43:58.116 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 515.2288ms.
2025-07-22 18:43:58.130 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Bilet, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:43:58.146 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer) in 568.8776ms
2025-07-22 18:43:58.148 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketsController.GetTickets (TicketSalesApp.AdminServer)'
2025-07-22 18:43:58.159 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Tickets - 200 null application/json; charset=utf-8 656.1017ms
2025-07-22 18:43:58.167 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Routes - null null
2025-07-22 18:43:58.192 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 18:43:58.198 +03:00 [INF] Route matched with {action = "GetRoutes", controller = "Routes"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Marshut]]] GetRoutes() on controller TicketSalesApp.AdminServer.Controllers.RoutesController (TicketSalesApp.AdminServer).
2025-07-22 18:43:58.240 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:43:58.243 +03:00 [INF] Fetching all routes with their related data
2025-07-22 18:43:58.374 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "a"."BusId", "a"."Model", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "b"."TicketId", "b"."RouteId", "b"."TicketPrice"
FROM "Marshuti" AS "m"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
LEFT JOIN "Bilety" AS "b" ON "m"."RouteId" = "b"."RouteId"
ORDER BY "m"."RouteId", "a"."BusId", "e"."EmpId"
2025-07-22 18:43:58.649 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 406.55ms.
2025-07-22 18:43:58.662 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Marshut, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:43:58.666 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer) in 441.8845ms
2025-07-22 18:43:58.669 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.RoutesController.GetRoutes (TicketSalesApp.AdminServer)'
2025-07-22 18:43:58.682 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Routes - 200 null application/json; charset=utf-8 515.3221ms
2025-07-22 18:44:03.788 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 18:44:03.812 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:44:03.817 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:44:03.929 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:44:03.936 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:44:04.026 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:44:04.044 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 112.5344ms.
2025-07-22 18:44:04.056 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:44:04.059 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 226.8996ms
2025-07-22 18:44:04.061 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:44:04.073 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 285.5177ms
2025-07-22 18:44:05.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2014-06-18&endDate=2025-07-22 - null null
2025-07-22 18:44:05.710 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:44:05.712 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:44:05.823 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:44:05.826 +03:00 [INF] Searching sales with start date: 18.06.2014 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:44:05.854 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2014-06-18T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:44:06.207 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 381.1533ms.
2025-07-22 18:44:06.253 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:44:06.256 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 529.387ms
2025-07-22 18:44:06.258 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:44:06.269 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2014-06-18&endDate=2025-07-22 - 200 null application/json; charset=utf-8 580.6562ms
2025-07-22 18:50:43.726 +03:00 [INF] Starting web application
2025-07-22 18:50:43.786 +03:00 [INF] Starting web host
2025-07-22 18:50:43.840 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:50:45.079 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:50:45.094 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:50:45.096 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:50:45.099 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:50:45.105 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:50:45.108 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:50:45.455 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:50:45.724 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:50:45.749 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:50:45.751 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:50:45.819 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:50:45.821 +03:00 [INF] Hosting environment: Development
2025-07-22 18:50:45.823 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:50:47.628 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:50:47.663 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 39.3804ms
2025-07-22 18:50:47.669 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:50:47.705 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 35.9541ms
2025-07-22 18:50:58.938 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:50:59.112 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:50:59.129 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:50:59.285 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:50:59.289 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:50:59.294 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:50:59.437 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:50:59.518 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:50:59.523 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:50:59.529 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:50:59.5284986Z"
2025-07-22 18:50:59.557 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:50:59.564 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 273.1334ms.
2025-07-22 18:50:59.584 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:50:59.594 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 443.8275ms
2025-07-22 18:50:59.596 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:50:59.610 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 672.28ms
2025-07-22 18:51:06.421 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:51:06.440 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:51:06.442 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:51:06.512 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:51:06.514 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:51:06.517 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:51:06.605 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:51:06.621 +03:00 [WRN] Authentication failed: Invalid password for user: admin
2025-07-22 18:51:06.623 +03:00 [WRN] Failed login attempt for user admin: Invalid credentials
2025-07-22 18:51:06.627 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.UnauthorizedObjectResult in 112.7789ms.
2025-07-22 18:51:06.644 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:51:06.648 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 191.0852ms
2025-07-22 18:51:06.650 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:51:06.671 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 401 null application/json; charset=utf-8 249.3985ms
2025-07-22 18:51:11.985 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:51:12.004 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:51:12.006 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:51:12.063 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:51:12.066 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:51:12.068 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:51:12.093 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:51:12.109 +03:00 [WRN] Authentication failed: Invalid password for user: admin
2025-07-22 18:51:12.111 +03:00 [WRN] Failed login attempt for user admin: Invalid credentials
2025-07-22 18:51:12.114 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.UnauthorizedObjectResult in 47.5224ms.
2025-07-22 18:51:12.127 +03:00 [INF] Executing UnauthorizedObjectResult, writing value of type '<>f__AnonymousType0`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:51:12.129 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 106.9653ms
2025-07-22 18:51:12.131 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:51:12.143 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 401 null application/json; charset=utf-8 158.3858ms
2025-07-22 18:51:25.225 +03:00 [INF] Starting web application
2025-07-22 18:51:25.258 +03:00 [INF] Starting web host
2025-07-22 18:51:25.300 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:51:26.361 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:51:26.379 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:51:26.381 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:51:26.384 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:51:26.389 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:51:26.392 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:51:26.803 +03:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:51:27.114 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:51:27.142 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:51:27.144 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:51:27.214 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:51:27.216 +03:00 [INF] Hosting environment: Development
2025-07-22 18:51:27.218 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:51:29.194 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:51:29.227 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 37.1547ms
2025-07-22 18:51:29.233 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:51:29.266 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 33.0059ms
2025-07-22 18:51:40.071 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:51:40.195 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:51:40.208 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:51:40.490 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:51:40.499 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:51:40.504 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:51:40.626 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:51:40.699 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:51:40.705 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:51:40.711 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:51:40.7108802Z"
2025-07-22 18:51:40.740 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:51:40.746 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 247.333ms.
2025-07-22 18:51:40.767 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:51:40.775 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 548.8714ms
2025-07-22 18:51:40.778 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:51:40.793 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 721.889ms
2025-07-22 18:54:04.174 +03:00 [INF] Starting web application
2025-07-22 18:54:04.206 +03:00 [INF] Starting web host
2025-07-22 18:54:04.256 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 18:54:05.828 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 18:54:05.844 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:54:05.847 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:54:05.850 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:54:05.856 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:54:05.859 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 18:54:06.253 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 18:54:06.675 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 18:54:06.704 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 18:54:06.707 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 18:54:06.785 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 18:54:06.788 +03:00 [INF] Hosting environment: Development
2025-07-22 18:54:06.790 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 18:54:08.604 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 18:54:08.642 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 44.0734ms
2025-07-22 18:54:08.649 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 18:54:08.687 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 38.3639ms
2025-07-22 18:54:20.231 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 18:54:20.413 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:54:20.431 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 18:54:20.584 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:54:20.589 +03:00 [INF] Login attempt started for user admin
2025-07-22 18:54:20.623 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 18:54:20.758 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 18:54:20.955 +03:00 [INF] User admin authenticated successfully
2025-07-22 18:54:20.963 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 18:54:20.972 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T17:54:20.9717020Z"
2025-07-22 18:54:20.999 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 18:54:21.006 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 417.2692ms.
2025-07-22 18:54:21.038 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 18:54:21.048 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 588.9212ms
2025-07-22 18:54:21.052 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 18:54:21.079 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 848.544ms
2025-07-22 18:54:26.786 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 18:54:26.912 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:54:26.918 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:54:27.077 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:54:27.084 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:54:27.355 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:54:27.376 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 296.2387ms.
2025-07-22 18:54:27.422 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:54:27.437 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 499.2445ms
2025-07-22 18:54:27.441 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:54:27.458 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 671.8286ms
2025-07-22 18:54:29.098 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2009-06-21&endDate=2025-07-22 - null null
2025-07-22 18:54:29.161 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:54:29.165 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 18:54:29.282 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 18:54:29.284 +03:00 [INF] Searching sales with start date: 21.06.2009 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 18:54:29.312 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2009-06-21T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 18:54:29.891 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 606.5865ms.
2025-07-22 18:54:29.906 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 18:54:29.915 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 732.0986ms
2025-07-22 18:54:29.919 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 18:54:29.931 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2009-06-21&endDate=2025-07-22 - 200 null application/json; charset=utf-8 833.4718ms
2025-07-22 19:02:08.622 +03:00 [INF] Starting web application
2025-07-22 19:02:08.650 +03:00 [INF] Starting web host
2025-07-22 19:02:08.687 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 19:02:09.860 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 19:02:09.877 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:02:09.879 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:02:09.882 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:02:09.888 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:02:09.890 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:02:10.239 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 19:02:10.470 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 19:02:10.498 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 19:02:10.500 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 19:02:10.566 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 19:02:10.569 +03:00 [INF] Hosting environment: Development
2025-07-22 19:02:10.571 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 19:02:12.727 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 19:02:12.771 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 47.186ms
2025-07-22 19:02:12.778 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 19:02:12.812 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 33.9198ms
2025-07-22 19:02:25.217 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 19:02:25.317 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 19:02:25.340 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 19:02:25.720 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 19:02:25.726 +03:00 [INF] Login attempt started for user admin
2025-07-22 19:02:25.730 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 19:02:25.848 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 19:02:25.927 +03:00 [INF] User admin authenticated successfully
2025-07-22 19:02:25.934 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 19:02:25.939 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T18:02:25.9394118Z"
2025-07-22 19:02:25.968 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 19:02:25.974 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 247.7199ms.
2025-07-22 19:02:26.001 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 19:02:26.012 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 602.7024ms
2025-07-22 19:02:26.021 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 19:02:26.038 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 821.1547ms
2025-07-22 19:02:31.394 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 19:02:31.514 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:02:31.520 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 19:02:31.690 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 19:02:31.698 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 19:02:31.873 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 19:02:31.891 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 198.8608ms.
2025-07-22 19:02:31.907 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 19:02:31.919 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 347.9136ms
2025-07-22 19:02:31.921 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:02:31.932 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 537.8591ms
2025-07-22 19:02:33.199 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2007-06-19&endDate=2025-07-22 - null null
2025-07-22 19:02:33.227 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:02:33.229 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 19:02:33.355 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 19:02:33.357 +03:00 [INF] Searching sales with start date: 19.06.2007 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 19:02:33.389 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__startDate_Value_0='2007-06-19T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 19:02:33.782 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 425.488ms.
2025-07-22 19:02:33.796 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 19:02:33.806 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 559.7323ms
2025-07-22 19:02:33.808 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:02:33.841 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2007-06-19&endDate=2025-07-22 - 200 null application/json; charset=utf-8 641.3074ms
2025-07-22 19:05:26.152 +03:00 [INF] Starting web application
2025-07-22 19:05:26.178 +03:00 [INF] Starting web host
2025-07-22 19:05:26.227 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-07-22 19:05:27.350 +03:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-22 19:05:27.365 +03:00 [WRN] The property 'RouteSchedules.BusTypes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:05:27.369 +03:00 [WRN] The property 'RouteSchedules.DaysOfWeek' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:05:27.371 +03:00 [WRN] The property 'RouteSchedules.EstimatedStopTimes' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:05:27.376 +03:00 [WRN] The property 'RouteSchedules.RouteStops' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:05:27.379 +03:00 [WRN] The property 'RouteSchedules.StopDistances' is a collection or enumeration type with a value converter but with no value comparer. Set a value comparer to ensure the collection/enumeration elements are compared correctly.
2025-07-22 19:05:27.774 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

                            SELECT 
                                name as TableName, 
                                (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as RowCount,
                                0 as TableCount
                            FROM sqlite_master 
                            WHERE type='table'
2025-07-22 19:05:28.075 +03:00 [WRN] Overriding address(es) 'https://localhost:7265, http://localhost:5167'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-07-22 19:05:28.100 +03:00 [INF] Now listening on: http://0.0.0.0:5000
2025-07-22 19:05:28.102 +03:00 [INF] Now listening on: https://0.0.0.0:5001
2025-07-22 19:05:28.198 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-22 19:05:28.200 +03:00 [INF] Hosting environment: Development
2025-07-22 19:05:28.202 +03:00 [INF] Content root path: G:\codesecondfolder\BRU-Avtopark-Avtobusov\TicketSalesApp.AdminServer
2025-07-22 19:05:30.006 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-07-22 19:05:30.074 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 71.4369ms
2025-07-22 19:05:30.080 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-07-22 19:05:30.116 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 36.018ms
2025-07-22 19:05:42.258 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/api/Auth/Login - application/json; charset=utf-8 null
2025-07-22 19:05:42.359 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 19:05:42.374 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.String]] Login(TicketSalesApp.AdminServer.Controllers.LoginModel) on controller TicketSalesApp.AdminServer.Controllers.AuthController (TicketSalesApp.AdminServer).
2025-07-22 19:05:42.502 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 19:05:42.506 +03:00 [INF] Login attempt started for user admin
2025-07-22 19:05:42.510 +03:00 [INF] Attempting to authenticate user: admin
2025-07-22 19:05:42.635 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[@__login_0='admin' (Size = 5)], CommandType='"Text"', CommandTimeout='30']
SELECT "u"."UserId", "u"."CreatedAt", "u"."Email", "u"."GuidId", "u"."IsActive", "u"."LastLoginAt", "u"."Login", "u"."PasswordHash", "u"."PhoneNumber", "u"."Role"
FROM "Users" AS "u"
WHERE "u"."Login" = @__login_0
LIMIT 1
2025-07-22 19:05:42.703 +03:00 [INF] User admin authenticated successfully
2025-07-22 19:05:42.707 +03:00 [INF] Starting JWT token generation for user admin
2025-07-22 19:05:42.713 +03:00 [INF] Successfully generated JWT token for user admin with expiration at "2025-07-22T18:05:42.7131199Z"
2025-07-22 19:05:42.735 +03:00 [INF] Successful login for user admin with role 1
2025-07-22 19:05:42.740 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 233.4725ms.
2025-07-22 19:05:42.760 +03:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType1`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-22 19:05:42.767 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer) in 373.4266ms
2025-07-22 19:05:42.770 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.AuthController.Login (TicketSalesApp.AdminServer)'
2025-07-22 19:05:42.785 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/api/Auth/Login - 200 null application/json; charset=utf-8 527.3851ms
2025-07-22 19:05:47.526 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - null null
2025-07-22 19:05:47.580 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:05:47.588 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 19:05:47.777 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 19:05:47.788 +03:00 [INF] Searching sales with start date: 22.06.2025 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 19:05:47.967 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[@__startDate_Value_0='2025-06-22T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 19:05:47.985 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 205.7231ms.
2025-07-22 19:05:47.999 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 19:05:48.010 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 407.9567ms
2025-07-22 19:05:48.013 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:05:48.026 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2025-06-22&endDate=2025-07-22 - 200 null application/json; charset=utf-8 500.4216ms
2025-07-22 19:05:50.515 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2006-06-20&endDate=2025-07-22 - null null
2025-07-22 19:05:50.542 +03:00 [INF] Executing endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:05:50.544 +03:00 [INF] Route matched with {action = "SearchSales", controller = "TicketSales"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.IEnumerable`1[TicketSalesApp.Core.Models.Prodazha]]] SearchSales(System.Nullable`1[System.DateTime], System.Nullable`1[System.DateTime], System.Nullable`1[System.Decimal], System.Nullable`1[System.Decimal], System.String) on controller TicketSalesApp.AdminServer.Controllers.TicketSalesController (TicketSalesApp.AdminServer).
2025-07-22 19:05:50.650 +03:00 [INF] Executing action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) - Validation state: "Valid"
2025-07-22 19:05:50.653 +03:00 [INF] Searching sales with start date: 20.06.2006 00:00:00, end date: 22.07.2025 00:00:00, min price: any, max price: any, user: any
2025-07-22 19:05:50.679 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startDate_Value_0='2006-06-20T00:00:00.0000000' (DbType = DateTime), @__endDate_Value_1='2025-07-22T00:00:00.0000000' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT "p"."SaleId", "p"."SaleDate", "p"."TicketId", "p"."TicketSoldToUser", "p"."TicketSoldToUserPhone", "b"."TicketId", "b"."RouteId", "b"."TicketPrice", "m"."RouteId", "m"."BusId", "m"."DriverId", "m"."EndPoint", "m"."StartPoint", "m"."TravelTime", "e"."EmpId", "e"."EmployedSince", "e"."JobId", "e"."Name", "e"."Patronym", "e"."Surname", "a"."BusId", "a"."Model"
FROM "Prodazhi" AS "p"
INNER JOIN "Bilety" AS "b" ON "p"."TicketId" = "b"."TicketId"
INNER JOIN "Marshuti" AS "m" ON "b"."RouteId" = "m"."RouteId"
INNER JOIN "Employees" AS "e" ON "m"."DriverId" = "e"."EmpId"
INNER JOIN "Avtobusy" AS "a" ON "m"."BusId" = "a"."BusId"
WHERE "p"."SaleDate" >= @__startDate_Value_0 AND "p"."SaleDate" <= @__endDate_Value_1
ORDER BY "p"."SaleDate" DESC
2025-07-22 19:05:51.134 +03:00 [INF] Executed action method TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 480.8245ms.
2025-07-22 19:05:51.145 +03:00 [INF] Executing ObjectResult, writing value of type 'System.Collections.Generic.List`1[[TicketSalesApp.Core.Models.Prodazha, TicketSalesApp.Core, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-07-22 19:05:51.153 +03:00 [INF] Executed action TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer) in 594.6009ms
2025-07-22 19:05:51.155 +03:00 [INF] Executed endpoint 'TicketSalesApp.AdminServer.Controllers.TicketSalesController.SearchSales (TicketSalesApp.AdminServer)'
2025-07-22 19:05:51.166 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/TicketSales/search?startDate=2006-06-20&endDate=2025-07-22 - 200 null application/json; charset=utf-8 651.2522ms
