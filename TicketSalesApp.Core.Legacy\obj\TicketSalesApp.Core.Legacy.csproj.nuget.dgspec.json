{"format": 1, "restore": {"G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj": {}}, "projects": {"G:\\codesecondfolder\\BRU-Avtopark-Avtobusov\\TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj", "projectName": "TicketSalesApp.Core.Legacy", "projectPath": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\TicketSalesApp.Core.Legacy.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\codesecondfolder\\BRU-Avtopark-<PERSON><PERSON><PERSON><PERSON><PERSON>\\TicketSalesApp.Core.Legacy\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "D:\\sdk\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net40"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\vs2010nugetlocal": {}, "https://www.nuget.org/api/v2/": {}}, "frameworks": {"net40": {"targetAlias": "net40", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net40": {"targetAlias": "net40", "dependencies": {"EntityFramework": {"target": "Package", "version": "[6.4.4, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[6.0.8, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}, "System.Data.SQLite.Core": {"target": "Package", "version": "[1.0.118, )"}, "System.Data.SQLite.EF6": {"target": "Package", "version": "[1.0.118, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.1.25120.13\\RuntimeIdentifierGraph.json"}}}}}